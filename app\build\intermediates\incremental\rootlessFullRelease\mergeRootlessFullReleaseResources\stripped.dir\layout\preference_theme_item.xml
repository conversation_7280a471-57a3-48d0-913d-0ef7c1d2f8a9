<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="114dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="4dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/theme_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:checkable="true"
        android:clickable="true"
        android:focusable="true"
        android:importantForAccessibility="no"
        app:cardCornerRadius="17dp"
        app:strokeColor="?attr/colorAccent"
        app:strokeWidth="4dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="170dp"
            android:background="?android:attr/colorBackground">

            <View
                android:id="@+id/top_nav"
                android:layout_width="0dp"
                android:layout_height="40dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/top_nav_text"
                android:layout_width="54dp"
                android:layout_height="17dp"
                android:layout_marginTop="5dp"
                android:layout_marginStart="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/top_nav"
                app:layout_constraintStart_toStartOf="@+id/top_nav"
                app:layout_constraintTop_toTopOf="@+id/top_nav"
                app:cardBackgroundColor="?attr/colorOnSurface"
                app:cardCornerRadius="4dp"/>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cover_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="4dp"
                app:layout_constraintTop_toBottomOf="@+id/top_nav"
                app:layout_constraintDimensionRatio="2:2.7"
                app:layout_constraintStart_toStartOf="@id/top_nav_text"
                app:layout_constraintEnd_toStartOf="@id/center_guideline"
                app:cardBackgroundColor="?android:attr/divider"
                app:cardElevation="0dp" />

            <com.google.android.material.card.MaterialCardView
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 android:layout_marginStart="4dp"
                 android:layout_marginTop="4dp"
                 app:layout_constraintStart_toStartOf="@+id/cover_container"
                 app:layout_constraintTop_toTopOf="@+id/cover_container"
                 app:cardCornerRadius="6dp">

                 <LinearLayout
                     android:id="@+id/badges"
                     android:layout_width="wrap_content"
                     android:layout_height="wrap_content"
                     android:background="@drawable/shape_rounded_rectangle">

                     <View
                         android:layout_width="12dp"
                         android:layout_height="16dp"
                         android:background="?attr/colorTertiary" />

                     <View
                         android:layout_width="12dp"
                         android:layout_height="16dp"
                         android:background="?attr/colorSecondary" />

                 </LinearLayout>

             </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/bottom_nav"
                android:layout_width="0dp"
                android:layout_height="40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:cardCornerRadius="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:layout_gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="12dp">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bottom_nav_selected_item"
                        android:layout_width="17dp"
                        android:layout_height="17dp"
                        android:layout_marginEnd="8dp"
                        app:cardBackgroundColor="?attr/colorPrimary"
                        app:cardCornerRadius="100dp"/>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bottom_nav_unselected_item"
                        android:layout_width="match_parent"
                        android:layout_height="17dp"
                        android:alpha="0.6"
                        app:cardBackgroundColor="?attr/colorOnSurface"
                        app:cardCornerRadius="4dp"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/center_guideline"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="38sp"
        android:maxLines="2"
        android:layout_marginTop="4dp"
        android:textAlignment="center"
        android:textAppearance="?android:attr/textAppearanceListItemSecondary"
        android:scrollbars="none"
        tools:text="Theme Name" />

</LinearLayout>
