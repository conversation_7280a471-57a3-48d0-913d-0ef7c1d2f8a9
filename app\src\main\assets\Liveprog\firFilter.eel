desc: FIR filtering

//tags: fir filter

@init
coefficients = 0;
hLen = importFLTFromStr("-0.000814106447125912,-0.00101893557539458,0.00439540510163513,0.0191817066643173,0.0348758447331096,0.0296722242827697,-0.00949579830159734,-0.0574346591733406,-0.0538264189367632,0.0446871259733802,0.207161817163176,0.333544729155861,0.333544729155861,0.207161817163176,0.0446871259733802,-0.0538264189367632,-0.0574346591733406,-0.00949579830159734,0.0296722242827697,0.0348758447331096,0.0191817066643173,0.00439540510163513,-0.00101893557539458,-0.000814106447125912", coefficients);
ptr1 = coefficients + hLen;
req = FIRInit(ptr1, hLen);
ptr2 = ptr1 + req;
req = FIRInit(ptr2, hLen);

@sample
spl0 = FIRProcess(ptr1, spl0, coefficients);
spl1 = FIRProcess(ptr2, spl1, coefficients);