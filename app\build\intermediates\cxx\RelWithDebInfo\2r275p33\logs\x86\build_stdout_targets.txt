ninja: Entering directory `D:\Users\Cai_Mouhui\Documents\1145\RootlessJamesDSP\app\.cxx\RelWithDebInfo\2r275p33\x86'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/.cxx/RelWithDebInfo/2r275p33/x86
[0/2] Re-checking globbed directories...
[1/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/cpthread.c.o
[2/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/HPFloat/xsigerr.c.o
[3/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/HPFloat/xsqrt.c.o
[4/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/HPFloat/xtoflt.c.o
[5/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/MersenneTwister.c.o
[6/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/HPFloat/xtodbl.c.o
[7/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/HPFloat/xtrig.c.o
[8/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/src_linear.c.o
[9/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/samplerate.c.o
[10/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/inv.c.o
[11/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[12/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[13/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/cpthread.c.o
[14/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[15/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[16/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[17/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-ram.c.o
[18/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/src_sinc.c.o
[19/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o
FAILED: CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djdspimprestoolbox_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/../libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -MD -MT CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o -MF CMakeFiles\jdspimprestoolbox.dir\libjdspimptoolbox\main\JdspImpResToolbox.c.o.d -o CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[20/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[21/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/FilterDesign/polyphaseFilterbank.c.o
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/FilterDesign/polyphaseFilterbank.c:55:1: warning: non-void function does not return a value in all control paths [-Wreturn-type]
   55 | }
      | ^
1 warning generated.
[22/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/fft.c.o
[23/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o
FAILED: CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.********\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.********/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_wrapper_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libcrashlytics-connector -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/../libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -MD -MT CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o -MF CMakeFiles\jamesdsp-wrapper.dir\libjamesdsp-wrapper\JamesDspWrapper.cpp.o.d -o CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
12 errors generated.
[24/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/EelVmVariable.cpp.o
[25/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JArrayList.cpp.o
[26/99] Building CXX object CMakeFiles/crashlytics-connector.dir/libcrashlytics-connector/Log.cpp.o
[27/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/FFTConvolver.c.o
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/FFTConvolver.c:886:27: warning: expression result unused [-Wunused-value]
  886 |                 conv2->_segmentsLLIRRe[1];
      |                 ~~~~~~~~~~~~~~~~~~~~~~ ~^
1 warning generated.
[28/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c.o
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:30:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/eel_matrix.h:94:38: warning: passing 'int32_t[2]' (aka 'int[2]') to parameter of type 'unsigned int *' converts between pointers to integer types with different sign [-Wpointer-sign]
   94 |         geninv(matIn, rows1, cols1, matOut, size);
      |                                             ^~~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/quadprog.h:25:82: note: passing argument to parameter 'size' here
   25 | void geninv(double *G, unsigned int m1, unsigned int n1, double *Y, unsigned int size[2]);
      |                                                                                  ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:2596:8: warning: assigning to 'const unsigned char *' from 'const char *' converts between pointers to integer types where one is of the unique plain 'char' type and the other is not [-Wpointer-sign]
 2596 |                         end = src + len;
      |                             ^ ~~~~~~~~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:2597:7: warning: assigning to 'const unsigned char *' from 'const char *' converts between pointers to integer types where one is of the unique plain 'char' type and the other is not [-Wpointer-sign]
 2597 |                         in = src;
      |                            ^ ~~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:2633:36: warning: passing 'unsigned char *' to parameter of type 'const char *' converts between pointers to integer types where one is of the unique plain 'char' type and the other is not [-Wpointer-sign]
 2633 |                         *dest = s_str_create_from_c_str(out);
      |                                                         ^~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/s_str.h:61:49: note: passing argument to parameter 'c_str_ptr' here
   61 | s_str s_str_create_from_c_str(const char *const c_str_ptr);
      |                                                 ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:2747:41: warning: passing 'unsigned char *' to parameter of type 'const char *' converts between pointers to integer types where one is of the unique plain 'char' type and the other is not [-Wpointer-sign]
 2747 |                         *dest = s_str_create_from_c_str_0Inc(out, out_len);
      |                                                              ^~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/s_str.h:62:54: note: passing argument to parameter 'c_str_ptr' here
   62 | s_str s_str_create_from_c_str_0Inc(const char *const c_str_ptr, const size_t length);
      |                                                      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/nseel-compiler.c:4367:34: warning: passing 'unsigned char *' to parameter of type 'const char *' converts between pointers to integer types where one is of the unique plain 'char' type and the other is not [-Wpointer-sign]
 4367 |         *dest = s_str_create_from_c_str(base64String);
      |                                         ^~~~~~~~~~~~
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/s_str.h:61:49: note: passing argument to parameter 'c_str_ptr' here
   61 | s_str s_str_create_from_c_str(const char *const c_str_ptr);
      |                                                 ^
6 warnings generated.
ninja: build stopped: subcommand failed.
