name: Build signed app
on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master
    tags:
      - v*

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build app
    runs-on: ubuntu-latest

    strategy:
      matrix:
        flavor: [Rootless, Root]
        
    steps:
      - id: flavor
        uses: ASzc/change-string-case-action@v5
        with:
          string: ${{ matrix.flavor }}
          
      - name: Install packages
        run: |
          sudo apt update
          sudo apt install rename
    
      - name: <PERSON><PERSON> repo
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Validate Gradle Wrapper
        uses: gradle/wrapper-validation-action@v3

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: adopt

      - name: Build release app
        if: startsWith(github.ref, 'refs/tags/')
        uses: gradle/gradle-command-action@v2
        with:
          arguments: assemble${{ matrix.flavor }}FullRelease

      - name: Build preview app
        if: "!startsWith(github.ref, 'refs/tags/')"
        uses: gradle/gradle-command-action@v2
        with:
          arguments: assemble${{ matrix.flavor }}FullPreview
          
      - name: Declare preview build type
        if: "!startsWith(github.ref, 'refs/tags/')"
        run: |
          set -x
          echo "BUILD_TYPE=preview" >> $GITHUB_ENV
          
      - name: Declare release build type
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          set -x
          echo "BUILD_TYPE=release" >> $GITHUB_ENV
          
      - name: Get tag name
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          set -x
          echo "VERSION_TAG=${GITHUB_REF/refs\/tags\//}" >> $GITHUB_ENV
       
      - name: Sign APKs
        uses: ilharp/sign-android-release@v1.0.4
        with:
          buildToolsVersion: 35.0.0
          releaseDir: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}
          signingKey: ${{ secrets.SIGNING_KEYSTORE }}
          keyAlias: ${{ secrets.KEY_ALIAS }}
          keyStorePassword: ${{ secrets.KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}

      - name: Rename build artifacts
        run: |
          set -e
          ls -l app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}
          rename -v 's/-unsigned-signed//g' app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*.apk
          rm -f app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*unsigned*.apk || true
          ls -l app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}

      - name: Upload build artifact (universal)
        uses: actions/upload-artifact@v4
        with:
          name: "${{ matrix.flavor }}JamesDSP-universal-preview_apk"
          path: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*universal-${{ env.BUILD_TYPE }}-signed.apk

      - name: Upload build artifact (x86)
        uses: actions/upload-artifact@v4
        with:
          name: "${{ matrix.flavor }}JamesDSP-x86-preview_apk"
          path: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*x86-${{ env.BUILD_TYPE }}-signed.apk
          
      - name: Upload build artifact (x86_64)
        uses: actions/upload-artifact@v4
        with:
          name: "${{ matrix.flavor }}JamesDSP-x86_64-preview_apk"
          path: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*x86_64-${{ env.BUILD_TYPE }}-signed.apk
          
      - name: Upload build artifact (armeabi-v7a)
        uses: actions/upload-artifact@v4
        with:
          name: "${{ matrix.flavor }}JamesDSP-armeabi-v7a-preview_apk"
          path: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*armeabi-v7a-${{ env.BUILD_TYPE }}-signed.apk
               
      - name: Upload build artifact (arm64-v8a)
        uses: actions/upload-artifact@v4
        with:
          name: "${{ matrix.flavor }}JamesDSP-arm64-v8a-preview_apk"
          path: app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*arm64-v8a-${{ env.BUILD_TYPE }}-signed.apk
       
      - name: Deploy root flavor APKs to the server
        if: ${{ steps.flavor.outputs.lowercase == 'root' && startsWith(github.ref, 'refs/tags/') && github.repository == 'timschneeb/RootlessJamesDSP' }}
        uses: wlixcc/SFTP-Deploy-Action@v1.2.4
        with:
            username: 'tim'
            server: 'srv1.timschneeberger.me'
            ssh_private_key: ${{ secrets.DEPLOY_SSH_PRIVATE_KEY }} 
            local_path: "app/build/outputs/apk/${{ steps.flavor.outputs.lowercase }}Full/${{ env.BUILD_TYPE }}/*universal-${{ env.BUILD_TYPE }}-signed.apk"
            remote_path: '/srv/http/nightly.timschneeberger.me/jamesdsp-rootfull'
            sftpArgs: '-o ConnectTimeout=5'
       
      - name: Changelog
        if: startsWith(github.ref, 'refs/tags/')
        uses: ardalanamini/auto-changelog@v3
        id: changelog
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          release-name: ${{ env.VERSION_TAG }}
          mention-authors: false
          mention-new-contributors: false
          include-compare: true
          
      - name: Read changelog template
        id: changelog_post
        uses: andstor/file-reader-action@v1
        with:
          path: ".github/CHANGELOG_POSTFIX.md"
          
      - name: Create Release
        if: startsWith(github.ref, 'refs/tags/') && github.repository == 'timschneeb/RootlessJamesDSP'
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ env.VERSION_TAG }}
          name: RootlessJamesDSP ${{ env.VERSION_TAG }}
          body: "${{ steps.changelog.outputs.changelog }} &#x20; ${{ steps.changelog_post.outputs.contents }}"
          #files: |
          #   app/build/outputs/apk/rootlessFull/${{ env.BUILD_TYPE }}/*-signed.apk
          draft: true
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
