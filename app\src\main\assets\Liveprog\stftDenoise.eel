desc: STFT Denoiser
//tags: stft

@init
function nesInit(frameLen, kOverlapCount, fs) local(halfLen ta tinc)
(
	halfLen = floor(frameLen / 2) + 1;
	//printf("func nesInit, frameLen = %d, kOverlapCount = %d, fs = %d\n", frameLen, kOverlapCount, fs);
	//printf("lastMemIdx before: %d\n", lastMemIdx);
	this.xt = lastMemIdx;
	this.xu = this.xt + halfLen;
	lastMemIdx = this.xu + halfLen;
	//printf("lastMemIdx after: %d\n", lastMemIdx);
	idx = 0;
	loop(halfLen,
	this.xt[idx] = 0.5;
	this.xu[idx] = 1.0;
	idx += 1);
	this.ax = 0.95;
	this.axc = 0.015;
	this.xih1r = -0.969346583;
	ta = 0.9; // Time const for smoothing SNR estimate = -tinc/log(0.98) from [1]
	tinc = (frameLen / (kOverlapCount / 2.0)) / fs;
	this.pnsaf = exp(-tinc / 0.01); // Lower == attenuate faster, higher == attenuate slower, 0.01 - 0.99
	this.a = exp(-tinc / ta); // SNR smoothing coefficient
);
function linear_value(Start, Step, Input, Space) local(Index X1)
(
	Index = floor((Input - Start) / Step);
	X1 = Start + Step * Index;
	Space[Index] + (Space[Index + 1] - Space[Index]) / (Start + Step * (Index + 1) - X1) * (Input - X1);
);
function exp_half_expint(x) local(lastvalue)
(
	(x < 0.001) ? lastvalue = 23.7069838528591 : (
	(x < 0.5) ? lastvalue = linear_value(0.001, 0.001, x, ExpintTable1) : (
	(x < 1.5) ? lastvalue = linear_value(0.5, 0.004, x, ExpintTable2) : (
	(x < 3.5) ? lastvalue = linear_value(1.5, 0.016, x, ExpintTable3) : (
	(x < 7.0) ? lastvalue = linear_value(3.5, 0.128, x, ExpintTable4) : (
	lastvalue =  1.0)))));
);
function map(x, in_min, in_max, out_min, out_max)
(
	(x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
);
ExpintTable1 = lastMemIdx;
TBL1Len = importFLTFromStr("23.7069838528591,16.7717465379827,13.7009138974986,11.8712632682872,10.6232790432573,9.70251934834318,8.98726537521472,8.41100569778996,7.93392138631819,7.53052520913866,7.18364620775603,6.88123767259336,6.61456518799516,6.37712085117550,6.16394302960600,5.97117400818068,5.79576307359075,5.63526170705868,5.48767891685564,5.35137689664745,5.22499436519682,5.10738930744236,4.99759556912265,4.89478951053104,4.79826407570494,4.70740840392088,4.62169163590064,4.54064993158200,4.46387597293612,4.39101040857512,4.32173482946755,4.25576596214220,4.19285083761842,4.13276274805164,4.07529784368062,4.02027225360223,3.96751963768093,3.91688909532357,3.86824337123107,3.82145730954289,3.77641651673572,3.73301620076127,3.69116015961497,3.65075989712442,3.61173384747092,3.57400669298874,3.53750876226751,3.50217549762152,3.46794698267300,3.43476752219192,3.40258526749631,3.37135188168804,3.34102223981316,3.31155415972174,3.28290815998044,3.25504724168273,3.22793669141795,3.20154390301688,3.17583821599525,3.15079076887801,3.12637436581124,3.10256335506255,3.07933351817774,3.05666196870670,3.03452705953755,3.01290829798753,2.99178626789491,2.97114255803992,2.95095969629606,2.93122108897745,2.91191096490456,2.89301432376079,2.87451688835619,2.85640506045378,2.83866587984864,2.82128698642033,2.80425658490688,2.78756341217248,2.77119670676312,2.75514618056346,2.73940199238583,2.72395472333746,2.70879535382637,2.69391524207852,2.67930610405036,2.66495999463083,2.65086929003624,2.63702667130950,2.62342510884296,2.61005784785037,2.59691839472030,2.58400050418823,2.57129816727008,2.55880559990431,2.54651723225385,2.53442769862326,2.52253182794947,2.51082463482822,2.49930131104066,2.48795721754775,2.47678787692198,2.46578896618871,2.45495631005102,2.44428587447415,2.43377376060707,2.42341619902058,2.41320954424257,2.40315026957248,2.39323496215838,2.38346031832094,2.37382313910991,2.36432032607956,2.35494887727036,2.34570588338525,2.33658852414933,2.32759406484274,2.31871985299707,2.30996331524625,2.30132195432345,2.29279334619611,2.28437513733157,2.27606504208642,2.26786084021292,2.25976037447644,2.25176154837801,2.24386232397665,2.23606071980627,2.22835480888241,2.22074271679421,2.21322261987739,2.20579274346410,2.19845136020609,2.19119678846726,2.18402739078251,2.17694157237955,2.16993777976068,2.16301449934171,2.15617025614521,2.14940361254578,2.14271316706459,2.13609755321129,2.12955543837070,2.12308552273267,2.11668653826277,2.11035724771225,2.10409644366535,2.09790294762245,2.09177560911728,2.08571330486694,2.07971493795310,2.07377943703317,2.06790575558004,2.06209287114933,2.05633978467280,2.05064551977697,2.04500912212581,2.03942965878654,2.03390621761756,2.02843790667766,2.02302385365557,2.01766320531903,2.01235512698272,2.00709880199405,2.00189343123644,1.99673823264902,1.99163244076242,1.98657530624985,1.98156609549293,1.97660409016169,1.97168858680821,1.96681889647336,1.96199434430616,1.95721426919529,1.95247802341227,1.94778497226589,1.94313449376751,1.93852597830669,1.93395882833703,1.92943245807155,1.92494629318749,1.92049977054011,1.91609233788508,1.91172345360935,1.90739258647005,1.90309921534113,1.89884282896757,1.89462292572687,1.89043901339742,1.88629060893379,1.88217723824838,1.87809843599956,1.87405374538572,1.87004271794533,1.86606491336265,1.86211989927891,1.85820725110882,1.85432655186230,1.85047739197102,1.84665936911987,1.84287208808307,1.83911516056472,1.83538820504377,1.83169084662314,1.82802271688299,1.82438345373788,1.82077270129779,1.81719010973286,1.81363533514165,1.81010803942295,1.80660789015094,1.80313456045357,1.79968772889418,1.79626707935613,1.79287230093042,1.78950308780621,1.78615913916411,1.78284015907221,1.77954585638472,1.77627594464320,1.77303014198020,1.76980817102537,1.76660975881389,1.76343463669714,1.76028254025561,1.75715320921390,1.75404638735781,1.75096182245352,1.74789926616858,1.74485847399493,1.74183920517372,1.73884122262194,1.73586429286073,1.73290818594551,1.72997267539765,1.72705753813779,1.72416255442072,1.72128750777178,1.71843218492470,1.71559637576090,1.71277987325024,1.70998247339298,1.70720397516326,1.70444418045368,1.70170289402124,1.69897992343449,1.69627507902183,1.69358817382098,1.69091902352958,1.68826744645690,1.68563326347657,1.68301629798038,1.68041637583312,1.67783332532835,1.67526697714515,1.67271716430584,1.67018372213455,1.66766648821673,1.66516530235953,1.66268000655296,1.66021044493195,1.65775646373917,1.65531791128866,1.65289463793017,1.65048649601433,1.64809333985847,1.64571502571317,1.64335141172954,1.64100235792712,1.63866772616246,1.63634738009837,1.63404118517373,1.63174900857400,1.62947071920224,1.62720618765078,1.62495528617342,1.62271788865819,1.62049387060068,1.61828310907787,1.61608548272249,1.61390087169789,1.61172915767339,1.60957022380010,1.60742395468729,1.60529023637911,1.60316895633181,1.60106000339145,1.59896326777193,1.59687864103356,1.59480601606193,1.59274528704726,1.59069634946413,1.58865910005155,1.58663343679342,1.58461925889943,1.58261646678620,1.58062496205887,1.57864464749297,1.57667542701663,1.57471720569320,1.57276988970406,1.57083338633185,1.56890760394396,1.56699245197631,1.56508784091749,1.56319368229308,1.56130988865036,1.55943637354325,1.55757305151749,1.55571983809611,1.55387664976522,1.55204340395992,1.55022001905059,1.54840641432934,1.54660250999672,1.54480822714870,1.54302348776380,1.54124821469052,1.53948233163495,1.53772576314861,1.53597843461646,1.53424027224518,1.53251120305162,1.53079115485142,1.52908005624787,1.52737783662094,1.52568442611649,1.52399975563566,1.52232375682447,1.52065636206353,1.51899750445801,1.51734711782771,1.51570513669730,1.51407149628676,1.51244613250194,1.51082898192532,1.50921998180684,1.50761907005499,1.50602618522796,1.50444126652494,1.50286425377765,1.50129508744188,1.49973370858926,1.49818005889910,1.49663408065045,1.49509571671416,1.49356491054517,1.49204160617492,1.49052574820377,1.48901728179368,1.48751615266095,1.48602230706900,1.48453569182142,1.48305625425498,1.48158394223283,1.48011870413777,1.47866048886568,1.47720924581896,1.47576492490017,1.47432747650566,1.47289685151945,1.47147300130700,1.47005587770929,1.46864543303681,1.46724162006378,1.46584439202234,1.46445370259693,1.46306950591866,1.46169175655986,1.46032040952865,1.45895542026358,1.45759674462841,1.45624433890691,1.45489815979776,1.45355816440954,1.45222431025577,1.45089655525002,1.44957485770114,1.44825917630847,1.44694947015721,1.44564569871381,1.44434782182145,1.44305579969554,1.44176959291935,1.44048916243965,1.43921446956244,1.43794547594874,1.43668214361042,1.43542443490612,1.43417231253719,1.43292573954373,1.43168467930064,1.43044909551380,1.42921895221617,1.42799421376411,1.42677484483365,1.42556081041680,1.42435207581795,1.42314860665037,1.42195036883262,1.42075732858517,1.41956945242691,1.41838670717186,1.41720905992579,1.41603647808301,1.41486892932307,1.41370638160764,1.41254880317732,1.41139616254859,1.41024842851071,1.40910557012271,1.40796755671045,1.40683435786366,1.40570594343303,1.40458228352738,1.40346334851082,1.40234910900000,1.40123953586132,1.40013460020826,1.39903427339870,1.39793852703225,1.39684733294769,1.39576066322039,1.39467849015976,1.39360078630675,1.39252752443140,1.39145867753039,1.39039421882461,1.38933412175684,1.38827835998936,1.38722690740165,1.38617973808811,1.38513682635578,1.38409814672216,1.38306367391298,1.38203338286003,1.38100724869901,1.37998524676745,1.37896735260260,1.37795354193933,1.37694379070816,1.37593807503322,1.37493637123025,1.37393865580464,1.37294490544952,1.37195509704383,1.37096920765043,1.36998721451422,1.36900909506034,1.36803482689230,1.36706438779023,1.36609775570906,1.36513490877678,1.36417582529274,1.36322048372589,1.36226886271313,1.36132094105761,1.36037669772708,1.35943611185231,1.35849916272539,1.35756582979824,1.35663609268095,1.35570993114029,1.35478732509814,1.35386825463000,1.35295269996345,1.35204064147671,1.35113205969717,1.35022693529993,1.34932524910639,1.34842698208280,1.34753211533891,1.34664063012658,1.34575250783841,1.34486773000637,1.34398627830052,1.34310813452764,1.34223328062999,1.34136169868395,1.34049337089883,1.33962827961555,1.33876640730542,1.33790773656893,1.33705225013452,1.33619993085737,1.33535076171824,1.33450472582227,1.33366180639786,1.33282198679548,1.33198525048657,1.33115158106238,1.33032096223294,1.32949337782587,1.32866881178536,1.32784724817108,1.32702867115713,1.32621306503098,1.32540041419244,1.32459070315263,1.32378391653301,1.32298003906429", ExpintTable1);
ExpintTable2 = ExpintTable1 + TBL1Len;
TBL2Len = importFLTFromStr("1.32298003906429,1.31979331908391,1.31665195173266,1.31355501016230,1.31050159309967,1.30749082395258,1.30452184995356,1.30159384133943,1.29870599056521,1.29585751155034,1.29304763895597,1.29027562749163,1.28754075124992,1.28484230306795,1.28217959391418,1.27955195229960,1.27695872371196,1.27439927007214,1.27187296921152,1.26937921436956,1.26691741371046,1.26448698985825,1.26208737944938,1.25971803270201,1.25737841300144,1.25506799650072,1.25278627173603,1.25053273925598,1.24830691126445,1.24610831127616,1.24393647378469,1.24179094394219,1.23967127725042,1.23757703926269,1.23550780529609,1.23346316015380,1.23144269785686,1.22944602138518,1.22747274242735,1.22552248113892,1.22359486590872,1.22168953313311,1.21980612699763,1.21794429926590,1.21610370907543,1.21428402274009,1.21248491355901,1.21070606163159,1.20894715367850,1.20720788286834,1.20548794864982,1.20378705658918,1.20210491821280,1.20044125085462,1.19879577750832,1.19716822668408,1.19555833226969,1.19396583339590,1.19239047430589,1.19083200422866,1.18929017725613,1.18776475222406,1.18625549259640,1.18476216635304,1.18328454588094,1.18182240786835,1.18037553320216,1.17894370686822,1.17752671785446,1.17612435905686,1.17473642718804,1.17336272268848,1.17200304964020,1.17065721568290,1.16932503193241,1.16800631290141,1.16670087642233,1.16540854357240,1.16412913860073,1.16286248885734,1.16160842472418,1.16036677954797,1.15913738957482,1.15792009388662,1.15671473433917,1.15552115550183,1.15433920459885,1.15316873145222,1.15200958842597,1.15086163037198,1.14972471457711,1.14859870071177,1.14748345077976,1.14637882906938,1.14528470210580,1.14420093860466,1.14312740942674,1.14206398753390,1.14101054794600,1.13996696769894,1.13893312580374,1.13790890320661,1.13689418275001,1.13588884913463,1.13489278888232,1.13390589029994,1.13292804344401,1.13195914008625,1.13099907367996,1.13004773932713,1.12910503374643,1.12817085524184,1.12724510367210,1.12632768042086,1.12541848836747,1.12451743185849,1.12362441667989,1.12273935002980,1.12186214049196,1.12099269800976,1.12013093386083,1.11927676063222,1.11843009219621,1.11759084368651,1.11675893147518,1.11593427314989,1.11511678749180,1.11430639445387,1.11350301513966,1.11270657178263,1.11191698772580,1.11113418740199,1.11035809631432,1.10958864101729,1.10882574909816,1.10806934915877,1.10731937079774,1.10657574459303,1.10583840208494,1.10510727575935,1.10438229903142,1.10366340622955,1.10295053257977,1.10224361419031,1.10154258803668,1.10084739194684,1.10015796458688,1.09947424544683,1.09879617482687,1.09812369382377,1.09745674431760,1.09679526895872,1.09613921115504,1.09548851505953,1.09484312555796,1.09420298825692,1.09356804947202,1.09293825621642,1.09231355618944,1.09169389776556,1.09107922998351,1.09046950253560,1.08986466575731,1.08926467061702,1.08866946870598,1.08807901222840,1.08749325399186,1.08691214739776,1.08633564643205,1.08576370565610,1.08519628019771,1.08463332574239,1.08407479852466,1.08352065531966,1.08297085343478,1.08242535070158,1.08188410546771,1.08134707658914,1.08081422342239,1.08028550581699,1.07976088410807,1.07924031910902,1.07872377210438,1.07821120484276,1.07770257952999,1.07719785882227,1.07669700581960,1.07619998405914,1.07570675750889,1.07521729056127,1.07473154802702,1.07424949512905,1.07377109749649,1.07329632115879,1.07282513253995,1.07235749845286,1.07189338609371,1.07143276303652,1.07097559722775,1.07052185698100,1.07007151097183,1.06962452823263,1.06918087814759,1.06874053044779,1.06830345520629,1.06786962283342,1.06743900407203,1.06701156999293,1.06658729199027,1.06616614177718,1.06574809138130,1.06533311314051,1.06492117969865,1.06451226400139,1.06410633929210,1.06370337910778,1.06330335727517,1.06290624790677,1.06251202539701,1.06212066441850,1.06173213991827,1.06134642711414,1.06096350149110,1.06058333879778,1.06020591504296,1.05983120649210,1.05945918966404,1.05908984132760,1.05872313849835,1.05835905843540,1.05799757863818,1.05763867684338,1.05728233102184,1.05692851937553,1.05657722033459,1.05622841255441,1.05588207491271,1.05553818650672,1.05519672665038,1.05485767487159,1.05452101090952,1.05418671471187,1.05385476643233,1.05352514642792,1.05319783525648,1.05287281367414,1.05255006263285,1.05222956327795,1.05191129694575,1.05159524516118,1.05128138963546", ExpintTable2);
ExpintTable3 = ExpintTable2 + TBL2Len;
TBL3Len = importFLTFromStr("1.05128138963546,1.05004757073798,1.04884747578272,1.04768001892361,1.04654415652096,1.04543888515555,1.04436323975396,1.04331629181808,1.04229714775192,1.04130494727950,1.04033886194815,1.03939809371172,1.03848187358887,1.03758946039162,1.03672013952010,1.03587322181929,1.03504804249416,1.03424396007968,1.03346035546248,1.03269663095118,1.03195220939250,1.03122653333061,1.03051906420717,1.02982928159987,1.02915668249719,1.02850078060752,1.02786110570057,1.02723720297956,1.02662863248220,1.02603496850925,1.02545579907891,1.02489072540588,1.02433936140370,1.02380133320915,1.02327627872763,1.02276384719839,1.02226369877869,1.02177550414576,1.02129894411591,1.02083370927975,1.02037949965277,1.01993602434059,1.01950300121813,1.01908015662196,1.01866722505536,1.01826394890531,1.01787007817095,1.01748537020298,1.01710958945341,1.01674250723528,1.01638390149183,1.01603355657466,1.01569126303060,1.01535681739673,1.01503002200335,1.01471068478442,1.01439861909517,1.01409364353666,1.01379558178686,1.01350426243799,1.01321951883998,1.01294118894961,1.01266911518517,1.01240314428648,1.01214312717987,1.01188891884811,1.01164037820494,1.01139736797407,1.01115975457247,1.01092740799773,1.01070020171944,1.01047801257425,1.01026072066462,1.01004820926107,1.00984036470772,1.00963707633110,1.00943823635201,1.00924373980039,1.00905348443304,1.00886737065403,1.00868530143785,1.00850718225501,1.00833292100015,1.00816242792246,1.00799561555839,1.00783239866655,1.00767269416470,1.00751642106871,1.00736350043358,1.00721385529628,1.00706741062034,1.00692409324231,1.00678383181981,1.00664655678124,1.00651220027702,1.00638069613243,1.00625197980176,1.00612598832403,1.00600266027993,1.00588193575016,1.00576375627497,1.00564806481499,1.00553480571319,1.00542392465797,1.00531536864739,1.00520908595447,1.00510502609346,1.00500313978714,1.00490337893512,1.00480569658299,1.00471004689240,1.00461638511208,1.00452466754956,1.00443485154382,1.00434689543869,1.00426075855700,1.00417640117547,1.00409378450034,1.00401287064364,1.00393362260019,1.00385600422519,1.00377998021249,1.00370551607342,1.00363257811625,1.00356113342618,1.00349114984591", ExpintTable3);
ExpintTable4 = ExpintTable3 + TBL3Len;
TBL4Len = importFLTFromStr("1.00349114984591,1.00298025775654,1.00254654515973,1.00217788293675,1.00186414308886,1.00159684863006,1.00136888908020,1.00117428828765,1.00100801422262,1.00086582260104,1.00074412790278,1.00063989666584,1.00055055896501,1.00047393478821,1.00040817265804,1.00035169834949,1.00030317195537,1.00026145187220,1.00022556453727,1.00019467895624,1.00016808522963,1.00014517642375,1.00012543324453,1.00010841106380,1.00009372892385,1.00008106020780,1.00007012471481,1.00006068192173,1.00005", ExpintTable4);
recipocalExp = ExpintTable4 + TBL4Len;
TBL5Len = importFLTFromStr("0.998365837443270,0.998349440944665,0.998332880205217,0.998316153585295,0.998299259429010,0.998282196064058,0.998264961801559,0.998247554935895,0.998229973744545,0.998212216487921,0.998194281409205,0.998176166734173,0.998157870671034,0.998139391410251,0.998120727124372,0.998101875967856,0.998082836076894,0.998063605569234,0.998044182543999,0.998024565081506,0.998004751243085,0.997984739070891,0.997964526587721,0.997944111796825,0.997923492681712,0.997902667205965,0.997881633313041,0.997860388926081,0.997838931947710,0.997817260259835,0.997795371723454,0.997773264178440,0.997750935443348,0.997728383315200,0.997705605569283,0.997682599958935,0.997659364215332,0.997635896047276,0.997612193140977,0.997588253159837,0.997564073744227,0.997539652511265,0.997514987054593,0.997490074944150,0.997464913725942,0.997439500921815,0.997413834029216,0.997387910520965,0.997361727845013,0.997335283424204,0.997308574656035,0.997281598912411,0.997254353539398,0.997226835856976,0.997199043158791,0.997170972711898,0.997142621756508,0.997113987505731,0.997085067145315,0.997055857833386,0.997026356700183,0.996996560847788,0.996966467349862,0.996936073251371,0.996905375568308,0.996874371287425,0.996843057365946,0.996811430731290,0.996779488280784,0.996747226881379,0.996714643369359,0.996681734550050,0.996648497197525,0.996614928054307,0.996581023831072,0.996546781206341,0.996512196826181,0.996477267303893,0.996441989219704,0.996406359120451,0.996370373519271,0.996334028895273,0.996297321693225,0.996260248323225,0.996222805160377,0.996184988544455,0.996146794779576,0.996108220133863,0.996069260839103,0.996029913090409,0.995990173045872,0.995950036826215,0.995909500514444,0.995868560155488,0.995827211755850,0.995785451283242,0.995743274666224,0.995700677793835,0.995657656515228,0.995614206639295,0.995570323934290,0.995526004127454,0.995481242904630,0.995436035909878,0.995390378745087,0.995344266969584,0.995297696099737,0.995250661608556,0.995203158925295,0.995155183435043,0.995106730478316,0.995057795350647,0.995008373302167,0.994958459537191,0.994908049213791,0.994857137443371,0.994805719290240,0.994753789771179,0.994701343854999,0.994648376462108,0.994594882464064,0.994540856683126,0.994486293891809,0.994431188812421,0.994375536116612,0.994319330424908,0.994262566306247,0.994205238277511,0.994147340803046,0.994088868294196,0.994029815108812,0.993970175550774,0.993909943869498,0.993849114259450,0.993787680859642,0.993725637753138,0.993662978966547,0.993599698469518,0.993535790174225,0.993471247934854,0.993406065547081,0.993340236747551,0.993273755213345,0.993206614561458,0.993138808348252,0.993070330068924,0.993001173156959,0.992931330983584,0.992860796857212,0.992789564022890,0.992717625661738,0.992644974890382,0.992571604760387,0.992497508257687,0.992422678302002,0.992347107746262,0.992270789376020,0.992193715908862,0.992115879993812,0.992037274210739,0.991957891069748,0.991877723010579,0.991796762401992,0.991715001541156,0.991632432653026,0.991549047889721,0.991464839329896,0.991379798978112,0.991293918764195,0.991207190542597,0.991119606091753,0.991031157113430,0.990941835232072,0.990851631994143,0.990760538867465,0.990668547240552,0.990575648421935,0.990481833639492,0.990387094039765,0.990291420687276,0.990194804563841,0.990097236567873,0.989998707513690,0.989899208130810,0.989798729063250,0.989697260868810,0.989594794018363,0.989491318895137,0.989386825793989,0.989281304920683,0.989174746391155,0.989067140230778,0.988958476373625,0.988848744661724,0.988737934844309,0.988626036577072,0.988513039421400,0.988398932843623,0.988283706214243,0.988167348807170,0.988049849798947,0.987931198267977,0.987811383193739,0.987690393456006,0.987568217834056,0.987444845005881,0.987320263547388,0.987194461931607,0.987067428527877,0.986939151601049,0.986809619310666,0.986678819710158,0.986546740746015,0.986413370256972,0.986278695973180,0.986142705515381,0.986005386394072,0.985866726008672,0.985726711646684,0.985585330482851,0.985442569578315,0.985298415879763,0.985152856218582,0.985005877310002,0.984857465752238,0.984707608025631,0.984556290491789,0.984403499392716,0.984249220849948,0.984093440863680,0.983936145311897,0.983777319949497,0.983616950407410,0.983455022191725,0.983291520682803,0.983126431134397,0.982959738672764,0.982791428295780,0.982621484872049,0.982449893140013,0.982276637707063,0.982101703048641,0.981925073507349,0.981746733292052,0.981566666476982,0.981384857000843,0.981201288665907,0.981015945137122,0.980828809941210,0.980639866465766,0.980449097958364,0.980256487525652,0.980062018132455,0.979865672600878,0.979667433609404,0.979467283691999,0.979265205237215,0.979061180487293,0.978855191537268,0.978647220334077,0.978437248675667,0.978225258210104,0.978011230434685,0.977795146695054,0.977576988184316,0.977356735942155,0.977134370853961,0.976909873649948,0.976683224904286,0.976454405034231,0.976223394299260,0.975990172800209,0.975754720478419,0.975517017114879,0.975277042329382,0.975034775579679,0.974790196160644,0.974543283203437,0.974294015674683,0.974042372375647,0.973788331941421,0.973531872840117,0.973272973372064,0.973011611669016,0.972747765693364,0.972481413237360,0.972212531922345,0.971941099197985,0.971667092341524,0.971390488457032,0.971111264474678,0.970829397150000,0.970544863063194,0.970257638618408,0.969967700043051,0.969675023387108,0.969379584522475,0.969081359142297,0.968780322760325,0.968476450710283,0.968169718145251,0.967860100037055,0.967547571175681,0.967232106168697,0.966913679440689,0.966592265232720,0.966267837601797,0.965940370420358,0.965609837375778,0.965276211969886,0.964939467518509,0.964599577151023,0.964256513809936,0.963910250250481,0.963560759040229,0.963208012558731,0.962851982997169,0.962492642358039,0.962129962454853,0.961763914911856,0.961394471163781,0.961021602455616,0.960645279842398,0.960265474189036,0.959882156170155,0.959495296269969,0.959104864782180,0.958710831809905,0.958313167265629,0.957911840871191,0.957506822157795,0.957098080466055,0.956685584946066,0.956269304557511,0.955849208069800,0.955425264062238,0.954997440924231,0.954565706855520,0.954130029866462,0.953690377778329,0.953246718223663,0.952799018646651,0.952347246303552,0.951891368263151,0.951431351407262,0.950967162431263,0.950498767844682,0.950026133971813,0.949549226952385,0.949068012742270,0.948582457114234,0.948092525658735,0.947598183784767,0.947099396720754,0.946596129515482,0.946088347039092,0.945576013984111,0.945059094866543,0.944537554027004,0.944011355631912,0.943480463674729,0.942944841977260,0.942404454191000,0.941859263798546,0.941309234115055,0.940754328289767,0.940194509307585,0.939629739990710,0.939059983000345,0.938485200838449,0.937905355849562,0.937320410222689,0.936730325993250,0.936135065045092,0.935534589112569,0.934928859782692,0.934317838497337,0.933701486555533,0.933079765115815,0.932452635198644,0.931820057688907,0.931181993338479,0.930538402768872,0.929889246473942,0.929234484822691,0.928574078062125,0.927907986320206,0.927236169608874,0.926558587827150,0.925875200764319,0.925185968103196,0.924490849423473,0.923789804205147,0.923082791832038,0.922369771595384,0.921650702697528,0.920925544255693,0.920194255305835,0.919456794806596,0.918713121643342,0.917963194632288,0.917206972524722,0.916444414011310,0.915675477726509,0.914900122253057,0.914118306126572,0.913329987840237,0.912535125849587,0.911733678577387,0.910925604418616,0.910110861745542,0.909289408912896,0.908461204263158,0.907626206131922,0.906784372853388,0.905935662765931,0.905080034217791,0.904217445572859,0.903347855216562,0.902471221561864,0.901587503055359,0.900696658183479,0.899798645478803,0.898893423526469,0.897980950970705,0.897061186521449,0.896134088961094,0.895199617151327,0.894257730040081,0.893308386668599,0.892351546178601,0.891387167819559,0.890415210956083,0.889435635075420,0.888448399795049,0.887453464870399,0.886450790202667,0.885440335846745,0.884422062019262,0.883395929106726,0.882361897673776,0.881319928471551,0.880269982446149,0.879212020747211,0.878146004736599,0.877071895997188,0.875989656341758,0.874899247821995,0.873800632737592,0.872693773645463,0.871578633369046,0.870455175007722,0.869323361946325,0.868183157864756,0.867034526747699,0.865877432894433,0.864711840928735,0.863537715808892,0.862355022837793,0.861163727673128,0.859963796337666,0.858755195229633,0.857537891133171,0.856311851228895,0.855077043104520,0.853833434765586,0.852580994646257,0.851319691620201,0.850049495011548,0.848770374605925,0.847482300661563,0.846185243920477,0.844879175619711,0.843564067502656,0.842239891830424,0.840906621393292,0.839564229522196,0.838212690100291,0.836851977574554,0.835482066967445,0.834102933888610,0.832714554546633,0.831316905760827,0.829909964973059,0.828493710259618,0.827068120343106,0.825633174604359,0.824188853094397,0.822735136546383,0.821272006387615,0.819799444751511,0.818317434489621,0.816825959183631,0.815325003157377,0.813814551488843,0.812294590022170,0.810765105379631,0.809226084973609,0.807677517018541,0.806119390542841,0.804551695400795,0.802974422284413,0.801387562735255,0.799791109156196,0.798185054823152,0.796569393896750,0.794944121433935,0.793309233399514,0.791664726677629,0.790010599083155,0.788346849373017,0.786673477257421,0.784990483410993,0.783297869483821,0.781595638112391,0.779883792930419,0.778162338579568,0.776431280720043,0.774690626041057,0.772940382271177,0.771180558188519,0.769411163630805,0.767632209505277,0.765843707798445,0.764045671585679,0.762238115040638,0.760421053444515,0.758594503195112,0.756758481815727,0.754913007963849,0.753058101439654,0.751193783194299,0.749320075338010,0.747437001147945,0.745544585075845,0.743642852755453,0.741731831009698,0.739811547857640,0.737882032521172,0.735943315431467,0.733995428235169,0.732038403800322,0.730072276222023,0.728097080827814,0.726112854182776,0.724119634094350,0.722117459616863,0.720106371055750,0.718086409971485,0.716057619183193,0.714020042771956,0.711973726083797,0.709918715732340,0.707855059601145,0.705782806845707,0.703702007895117,0.701612714453380,0.699514979500387,0.697408857292536,0.695294403362991,0.693171674521592,0.691040728854389,0.688901625722818,0.686754425762495,0.684599190881649,0.682435984259160,0.680264870342229,0.678085914843651,0.675899184738717,0.673704748261702,0.671502674901983,0.669293035399748,0.667075901741310,0.664851347154025,0.662619446100801,0.660380274274214,0.658133908590215,0.655880427181424,0.653619909390034,0.651352435760291,0.649078088030576,0.646796949125078,0.644509103145054,0.642214635359687,0.639913632196528,0.637606181231542,0.635292371178737,0.632972291879393,0.630646034290887,0.628313690475111,0.625975353586495,0.623631117859627,0.621281078596476,0.618925332153223,0.616563975926701,0.614197108340440,0.611824828830333,0.609447237829922,0.607064436755291,0.604676527989605,0.602283614867259,0.599885801657677,0.597483193548736,0.595075896629840,0.592664017874644,0.590247665123424,0.587826947065117,0.585401973219010,0.582972853916118,0.580539700280222,0.578102624208604,0.575661738352461,0.573217156097021,0.570768991541363,0.568317359477947,0.565862375371857,0.563404155339781,0.560942816128714,0.558478475094406,0.556011250179566,0.553541259891812,0.551068623281397,0.548593459918709,0.546115889871552,0.543636033682223,0.541154012344388,0.538669947279773,0.536183960314674,0.533696173656298,0.531206709868945,0.528715691850036,0.526223242806008,0.523729486228068,0.521234545867840,0.518738545712892,0.516241609962162,0.513743863001308,0.511245429377961,0.508746433776923,0.506247000995300,0.503747255917589,0.501247323490727,0.498747328699119,0.496247396539639,0.493747651996636,0.491248220016942,0.488749225484894,0.486250793197383,0.483753047838945,0.481256113956891,0.478760115936505,0.476265177976300,0.473771424063366,0.471278977948794,0.468787963123211,0.466298502792415,0.463810719853143,0.461324736868956,0.458840676046270,0.456358659210543,0.453878807782608,0.451401242755193,0.448926084669603,0.446453453592602,0.443983469093490,0.441516250221383,0.439051915482715,0.436590582818962,0.434132369584596,0.431677392525285,0.429225767756347,0.426777610741449,0.424333036271591,0.421892158444344,0.419455090643390,0.417021945518334,0.414592834964822,0.412167870104963,0.409747161268052,0.407330817971622,0.404918948902814,0.402511661900068,0.400109063935166,0.397711261095604,0.395318358567311,0.392930460617727,0.390547670579232,0.388170090832936,0.385797822792841,0.383430966890367,0.381069622559260,0.378713888220869,0.376363861269814,0.374019638060036,0.371681313891235,0.369348982995701,0.367022738525540,0.364702672540295,0.362388875994967,0.360081438728436,0.357780449452288,0.355485995740041,0.353198164016780,0.350917039549198,0.348642706436046,0.346375247598983,0.344114744773847,0.341861278502319,0.339614928124012,0.337375771768953,0.335143886350482,0.332919347558549,0.330702229853429,0.328492606459827,0.326290549361395,0.324096129295651,0.321909415749291,0.319730476953902,0.317559379882077,0.315396190243907,0.313240972483884,0.311093789778171,0.308954704032275,0.306823775879092,0.304701064677327,0.302586628510303,0.300480524185128,0.298382807232236,0.296293531905294,0.294212751181462,0.292140516762017,0.290076879073324,0.288021887268155,0.285975589227352,0.283938031561825,0.281909259614887,0.279889317464917,0.277878247928346,0.275876092562957,0.273882891671506,0.271898684305647,0.269923508270159,0.267957400127472,0.266000395202483,0.264052527587660,0.262113830148429,0.260184334528826,0.258264071157430,0.256353069253556,0.254451356833697,0.252558960718232,0.250675906538365,0.248802218743318,0.246937920607745,0.245083034239383,0.243237580586919,0.241401579448077,0.239575049477908,0.237758008197293,0.235950472001638,0.234152456169758,0.232363974872949,0.230585041184244,0.228815667087833,0.227055863488660,0.225305640222171,0.223565006064225,0.221833968741154,0.220112534939960,0.218400710318657,0.216698499516741,0.215005906165783,0.213322932900149,0.211649581367827,0.209985852241367,0.208331745228926,0.206687259085402,0.205052391623665,0.203427139725880,0.201811499354899,0.200205465565737,0.198609032517116,0.197022193483072,0.195444940864627,0.193877266201508,0.192319160183922,0.190770612664375,0.189231612669529,0.187702148412090,0.186182207302739,0.184671775962074,0.183170840232579,0.181679385190618,0.180197395158422,0.178724853716105,0.177261743713666,0.175808047283002,0.174363745849911,0.172928820146086,0.171503250221099,0.170087015454367,0.168680094567096,0.167282465634203,0.165894106096214,0.164514992771122,0.163145101866215,0.161784408989873,0.160432889163312,0.159090516832290,0.157757265878768,0.156433109632514,0.155118020882660,0.153811971889199,0.152514934394426,0.151226879634316,0.149947778349834,0.148677600798190,0.147416316764011,0.146163895570454,0.144920306090233,0.143685516756584,0.142459495574141,0.141242210129738,0.140033627603130,0.138833714777622,0.137642438050627,0.136459763444121,0.135285656615023,0.134120082865476,0.132963007153039,0.131814394100786,0.130674208007312,0.129542412856636,0.128418972328021,0.127303849805680,0.126197008388390,0.125098410899013,0.124008019893900,0.122925797672205,0.121851706285093,0.120785707544843,0.119727763033847,0.118677834113505,0.117635881933011,0.116601867438040,0.115575751379320,0.114557494321102,0.113547056649522,0.112544398580853,0.111549480169650,0.110562261316791,0.109582701777399,0.108610761168670,0.107646398977577,0.106689574568478,0.105740247190606,0.104798375985458,0.103863919994069,0.102936838164183,0.102017089357307,0.101104632355673,0.100199425869071,0.0993014285415949,0.0984105989582650,0.0975268956515539,0.0966502771078014,0.0957807017735237,0.0949181280616180,0.0940625143574607,0.0932138190249016,0.0923720004121536,0.0915370168575786,0.0907088266953705,0.0898873882611357,0.0890726598973713,0.0882645999588430,0.0874631668178609,0.0866683188694572,0.0858800145364631,0.0850982122744879,0.0843228705768009,0.0835539479791152,0.0827914030642761,0.0820351944668539,0.0812852808776418,0.0805416210480605,0.0798041737944695,0.0790728980023861,0.0783477526306139,0.0776286967152799,0.0769156893737830,0.0762086898086531,0.0755076573113231,0.0748125512658138,0.0741233311523327,0.0734399565507883,0.0727623871442196,0.0720905827221434,0.0714245031838188,0.0707641085414302,0.0701093589231909,0.0694602145763665,0.0688166358702202,0.0681785832988809,0.0675460174841343,0.0669188991781392,0.0662971892660683,0.0656808487686766,0.0650698388447958,0.0644641207937582,0.0638636560577495,0.0632684062240915,0.0626783330274565,0.0620933983520138,0.0615135642335092,0.0609387928612785,0.0603690465801968,0.0598042878925623,0.0592444794599191,0.0586895841048158,0.0581395648125044,0.0575943847325787,0.0570540071805528,0.0565183956393816,0.0559875137609244,0.0554613253673510,0.0549397944524930,0.0544228851831396,0.0539105619002806,0.0534027891202957,0.0528995315360918,0.0524007540181898,0.0519064216157604,0.0514164995576110,0.0509309532531239,0.0504497482931467,0.0499728504508362,0.0495002256824560,0.0490318401281292,0.0485676601125465,0.0481076521456312,0.0476517829231610,0.0472000193273483,0.0467523284273797,0.0463086774799141,0.0458690339295425,0.0454333654092086,0.0450016397405910,0.0445738249344488,0.0441498891909306,0.0437298008998474,0.0433135286409106,0.0429010411839366,0.0424923074890162,0.0420872967066524,0.0416859781778661,0.0412883214342692,0.0408942961981081,0.0405038723822762,0.0401170200902980,0.0397337096162831,0.0393539114448531,0.0389775962510403,0.0386047349001602,0.0382352984476571,0.0378692581389244,0.0375065854090996,0.0371472518828356,0.0367912293740468,0.0364384898856335,0.0360890056091823,0.0357427489246450,0.0353996923999960,0.0350598087908684,0.0347230710401695,0.0343894522776766,0.0340589258196138,0.0337314651682084,0.0334070440112307,0.0330856362215147,0.0327672158564616,0.0324517571575268,0.0321392345496898,0.0318296226409086,0.0315228962215583,0.0312190302638548,0.0309179999212636,0.0306197805278948,0.0303243475978838,0.0300316768247595,0.03", recipocalExp);
lastMemIdx = recipocalExp + TBL5Len;
frameLen = 2048;
halfLen = floor(frameLen / 2) + 1;
kOverlapCount = 4;
nes1.nesInit(frameLen, kOverlapCount, sr);
nes2.nesInit(frameLen, kOverlapCount, sr);
stftIndexLeft = lastMemIdx;
stftIndexRight = stftIndexLeft + 50;
memreq = stftCheckMemoryRequirement(stftIndexLeft, frameLen, kOverlapCount, 1);
memreq = stftCheckMemoryRequirement(stftIndexRight, frameLen, kOverlapCount, 1);
stftStructLeft = stftIndexRight + 50;
stftStructRight = stftStructLeft + memreq;
requiredSamples = stftInit(stftIndexLeft, stftStructLeft);
requiredSamples = stftInit(stftIndexRight, stftStructRight);
dcReImSmps = 2;
inBufLeft = stftStructRight + memreq; // Pointer to memory
inBufRight = inBufLeft + frameLen + dcReImSmps; // Pointer to memory
outBufLeft = inBufRight + frameLen + dcReImSmps;
outBufRight = outBufLeft + frameLen + dcReImSmps;

@sample
inBufLeft[bufpos] = spl0;
spl0 = outBufLeft[bufpos];
inBufRight[bufpos] = spl1;
spl1 = outBufRight[bufpos];
bufpos += 1;
bufpos >= requiredSamples ?
(
	arrayLen = stftForward(inBufLeft, stftIndexLeft, stftStructLeft, 1);
	arrayLen = stftForward(inBufRight, stftIndexRight, stftStructRight, 1);
	i = 0;
	ii = 0;
	loop(halfLen,
	//magnitudeLeft = hypot(inBufLeft[i], inBufLeft[i + 1]);
	//magnitudeRight = hypot(inBufRight[i], inBufRight[i + 1]);
	signalPS = (inBufLeft[i] * inBufLeft[i] + inBufLeft[i + 1] * inBufLeft[i + 1]);
	//printf("%1.8ff,", signalPS);
	reCOH = nes1.xih1r * signalPS / nes1.xt[ii];
	(reCOH < -9.9) ? (reCOH = 1.0) : (
		(reCOH > -0.0001) ? (reCOH = 0.0297446) : (
		reCOH = linear_value(-9.9, 0.01, reCOH, recipocalExp))
	);
	//printf("%1.8f, ", reCOH);
	(reCOH > nes1.pnsaf) ? (reCOH = nes1.pnsaf);
	reCOH = (1.0 - reCOH) * signalPS + reCOH * nes1.xt[ii];
	nes1.xt[ii] = nes1.ax * nes1.xt[ii] + nes1.axc * reCOH;
	coh = signalPS / nes1.xt[ii];
	imCOH = nes1.a * nes1.xu[ii] + (1.0 - nes1.a) * max(coh - 1.0, 0.0);
	(imCOH < $EPS || coh < $EPS) ? (g1 = 0.0) : (
		imCOH = imCOH / (1.0 + imCOH);
		g1 = imCOH * exp_half_expint(imCOH * coh);
		(g1 > 1.0) ? (g1 = 1.0);
	);
	nes1.xu[ii] = coh * (g1 * g1);
	inBufLeft[i] = inBufLeft[i] * g1;
	inBufLeft[i + 1] = inBufLeft[i + 1] * g1;
	signalPS = (inBufRight[i] * inBufRight[i] + inBufRight[i + 1] * inBufRight[i + 1]);
	//printf("%1.8ff,", signalPS);
	reCOH = nes2.xih1r * signalPS / nes2.xt[ii];
	(reCOH < -9.9) ? (reCOH = 1.0) : (
		(reCOH > -0.0001) ? (reCOH = 0.0297446) : (
		reCOH = linear_value(-9.9, 0.01, reCOH, recipocalExp))
	);
	//printf("%1.8f, ", reCOH);
	(reCOH > nes2.pnsaf) ? (reCOH = nes2.pnsaf);
	reCOH = (1.0 - reCOH) * signalPS + reCOH * nes2.xt[ii];
	nes2.xt[ii] = nes2.ax * nes2.xt[ii] + nes2.axc * reCOH;
	coh = signalPS / nes2.xt[ii];
	imCOH = nes2.a * nes2.xu[ii] + (1.0 - nes2.a) * max(coh - 1.0, 0.0);
	(imCOH < $EPS || coh < $EPS) ? (g2 = 0.0) : (
		imCOH = imCOH / (1.0 + imCOH);
		g2 = imCOH * exp_half_expint(imCOH * coh);
		(g2 > 1.0) ? (g2 = 1.0);
	);
	nes2.xu[ii] = coh * (g2 * g2);
	inBufRight[i] = inBufRight[i] * g2;
	inBufRight[i + 1] = inBufRight[i + 1] * g2;
	i += 2;
	ii += 1);
  error = stftBackward(inBufLeft, stftIndexLeft, stftStructLeft, 1);
  error = stftBackward(inBufRight, stftIndexRight, stftStructRight, 1);
  idx = 0;
  loop(requiredSamples,
  outBufLeft[idx] = inBufLeft[idx];
  outBufRight[idx] = inBufRight[idx];
  idx+=1);
  bufpos = 0;
);
