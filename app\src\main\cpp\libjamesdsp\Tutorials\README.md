# How to build Android audio effect from scratch?

## Example - JamesDSP Engine frame
Compatibility: Support all Android version from 4.4(Kitkat) to 10.0(Q)

Any signal processing code is not present.

Android audio framework learner can play around current repository and add your own DSP code. I'm pretty sure this repository is a good starting point, you can build your own audio effect engine without download the whole Android source code from Google, while I can't deny AOSP Android code is very useful at reference, but not very useful at program design.

**Description to be continued**

## Is root required?
For testing, yes, you must get your device rooted.

For Android ROM distribution, no, you don't need any root, just build this engine code along other ROM source code.

## License
All codes under this folder is distributed in Apache license.

Main repository GNU license is excluded and not related.