<?xml version="1.0" encoding="utf-8"?>

<!--  Copyright (C) 2013 Alexander "Evisceration" Martinz
      Copyright (C) 2015 The OmniROM Project

 This program is free software: you can redistribute it and/or modify
 it under the terms of the GNU General Public License as published by
 the Free Software Foundation, either version 3 of the License, or
 (at your option) any later version.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License for more details.

 You should have received a copy of the GNU General Public License
 along with this program.  If not, see <http://www.gnu.org/licenses/>.
-->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp">

  <ImageView
      android:id="@+id/drawer_icon"
      android:layout_width="40dp"
      android:layout_height="40dp"
      android:paddingLeft="10dp"
      android:paddingTop="10dp"
      android:paddingRight="10dp"
      android:paddingBottom="10dp"
      android:layout_alignParentLeft="true"
      android:layout_centerVertical="true" />

  <TextView
      android:id="@+id/drawer_title"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:textColor="#111"
      android:textSize="15sp"
      android:background="?android:attr/activatedBackgroundIndicator"
      android:textAppearance="?android:attr/textAppearanceListItemSmall"
      android:minHeight="?android:attr/listPreferredItemHeightSmall"
      android:gravity="center_vertical"
      android:paddingLeft="16dp"
      android:paddingRight="16dp" />

</RelativeLayout>