<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minHeight="700dp">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.google.android.material.textfield.TextInputLayout
        style="?attr/textInputFilledStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center_horizontal"
        android:hint="@string/app_list_search"
        app:startIconDrawable="@drawable/ic_search_24dp">
        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:id="@+id/loader"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:indeterminate="true"
            android:indeterminateBehavior="cycle"
            app:showAnimationBehavior="outward"
            app:hideAnimationBehavior="inward"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:textAppearance="?attr/textAppearanceBody1"
            android:text="@string/app_list_loading" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="true"
        android:background="@drawable/shape_rounded_corners"
        tools:listitem="@layout/item_app_list" />
</LinearLayout>