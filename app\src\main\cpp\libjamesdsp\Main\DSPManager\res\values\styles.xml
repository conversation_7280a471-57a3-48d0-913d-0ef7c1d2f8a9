<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (C) 2012-2013 The CyanogenMod Project
     Copyright (C) 2015 The OmniROM Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<resources xmlns:android="http://schemas.android.com/apk/res/android">

  <style name="AppTheme" parent="@android:style/Theme.Material">
    <item name="android:colorPrimary">@color/primary</item>
    <item name="android:colorPrimaryDark">@color/primary_dark</item>
    <item name="android:colorAccent">@color/accent</item>
    <item name="android:navigationBarColor">@color/primary_dark</item>
    <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
  </style>
  <style name="AppThemeRed" parent="@android:style/Theme.Material">
    <item name="android:windowBackground">@color/backgroundred</item>
    <item name="android:itemBackground">@color/backgroundred</item>
    <item name="android:textColorAlertDialogListItem">@color/primaryred</item>
    <item name="android:colorPrimary">@color/primaryred</item>
    <item name="android:colorPrimaryDark">@color/primaryred_dark</item>
    <item name="android:colorAccent">@color/accentred</item>
    <item name="android:navigationBarColor">@color/primaryred_dark</item>
    <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
  </style>
  <style name="AppThemeIdea" parent="@android:style/Theme.Material">
    <item name="android:windowBackground">@color/backgroundidea</item>
    <item name="android:itemBackground">@color/backgroundidea</item>
    <item name="android:textColorAlertDialogListItem">@color/primaryidea</item>
    <item name="android:colorPrimary">@color/primaryidea</item>
    <item name="android:colorPrimaryDark">@color/primaryidea_dark</item>
    <item name="android:colorAccent">@color/accentidea</item>
    <item name="android:navigationBarColor">@color/primaryidea_dark</item>
    <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
  </style>

  <style name="DrawerArrowStyle" parent="@style/Widget.AppCompat.DrawerArrowToggle">
    <item name="spinBars">true</item>
    <item name="color">@android:color/white</item>
  </style>

</resources>