  Activity android.app  Application android.app  Service android.app  BroadcastReceiver android.content  ContentProvider android.content  Context android.content  ContextWrapper android.content  SharedPreferences android.content  AudioManager 
android.media  MediaSessionManager android.media.session  Binder 
android.os  PowerManager 
android.os  TileService android.service.quicksettings  ContextThemeWrapper android.view  View android.view  	ViewGroup android.view  ArrayAdapter android.widget  BaseAdapter android.widget  ImageButton android.widget  	ImageView android.widget  LinearLayout android.widget  ComponentActivity androidx.activity  AppCompatActivity androidx.appcompat.app  AppCompatDialogFragment androidx.appcompat.app  ComponentActivity androidx.core.app  NotificationCompat androidx.core.app  DialogFragment androidx.fragment.app  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  	ViewModel androidx.lifecycle  MediaRouter androidx.mediarouter.media  Callback &androidx.mediarouter.media.MediaRouter  DialogPreference androidx.preference  ListPreference androidx.preference  "ListPreferenceDialogFragmentCompat androidx.preference  
Preference androidx.preference  PreferenceDialogFragmentCompat androidx.preference  PreferenceFragmentCompat androidx.preference  PreferenceGroup androidx.preference  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  Callback androidx.room.RoomDatabase  CoroutineWorker 
androidx.work  ListenableWorker 
androidx.work  CodeViewAdapter com.amrdeveloper.codeview  BottomSheetDialogFragment 'com.google.android.material.bottomsheet  FloatingActionButton 0com.google.android.material.floatingactionbutton  VisibilityAwareImageButton $com.google.android.material.internal  MainApplication #me.timschneeberger.rootlessjamesdsp  	Companion 3me.timschneeberger.rootlessjamesdsp.MainApplication  AeqSelectorActivity ,me.timschneeberger.rootlessjamesdsp.activity  BaseActivity ,me.timschneeberger.rootlessjamesdsp.activity  BlocklistActivity ,me.timschneeberger.rootlessjamesdsp.activity  EngineLauncherActivity ,me.timschneeberger.rootlessjamesdsp.activity  LiveprogEditorActivity ,me.timschneeberger.rootlessjamesdsp.activity  MainActivity ,me.timschneeberger.rootlessjamesdsp.activity  OnboardingActivity ,me.timschneeberger.rootlessjamesdsp.activity  SettingsActivity ,me.timschneeberger.rootlessjamesdsp.activity  	Companion @me.timschneeberger.rootlessjamesdsp.activity.AeqSelectorActivity  	Companion Cme.timschneeberger.rootlessjamesdsp.activity.LiveprogEditorActivity  	Companion 9me.timschneeberger.rootlessjamesdsp.activity.MainActivity  	Companion ?me.timschneeberger.rootlessjamesdsp.activity.OnboardingActivity  	Companion =me.timschneeberger.rootlessjamesdsp.activity.SettingsActivity  AppBlocklistAdapter +me.timschneeberger.rootlessjamesdsp.adapter  AppsListAdapter +me.timschneeberger.rootlessjamesdsp.adapter  AutoEqResultAdapter +me.timschneeberger.rootlessjamesdsp.adapter  CustomCodeViewAdapter +me.timschneeberger.rootlessjamesdsp.adapter  GraphicEqNodeAdapter +me.timschneeberger.rootlessjamesdsp.adapter  ThemesPreferenceAdapter +me.timschneeberger.rootlessjamesdsp.adapter  AppBlocklistViewHolder ?me.timschneeberger.rootlessjamesdsp.adapter.AppBlocklistAdapter  
ViewHolder ;me.timschneeberger.rootlessjamesdsp.adapter.AppsListAdapter  AutoEqResultViewHolder ?me.timschneeberger.rootlessjamesdsp.adapter.AutoEqResultAdapter  
ViewHolder @me.timschneeberger.rootlessjamesdsp.adapter.GraphicEqNodeAdapter  ThemeViewHolder Cme.timschneeberger.rootlessjamesdsp.adapter.ThemesPreferenceAdapter  AutoEqClient 'me.timschneeberger.rootlessjamesdsp.api  
AutoEqService 'me.timschneeberger.rootlessjamesdsp.api  	Companion 4me.timschneeberger.rootlessjamesdsp.api.AutoEqClient  BackupCreatorJob *me.timschneeberger.rootlessjamesdsp.backup  
BackupManager *me.timschneeberger.rootlessjamesdsp.backup  BackupNotifier *me.timschneeberger.rootlessjamesdsp.backup  BackupRestoreService *me.timschneeberger.rootlessjamesdsp.backup  	Companion ;me.timschneeberger.rootlessjamesdsp.backup.BackupCreatorJob  	Companion 8me.timschneeberger.rootlessjamesdsp.backup.BackupManager  	Companion ?me.timschneeberger.rootlessjamesdsp.backup.BackupRestoreService  ThemingDelegateImpl -me.timschneeberger.rootlessjamesdsp.delegates  SourcePositionListener 1me.timschneeberger.rootlessjamesdsp.editor.plugin  UndoRedoManager 1me.timschneeberger.rootlessjamesdsp.editor.plugin  EditHistory Ame.timschneeberger.rootlessjamesdsp.editor.plugin.UndoRedoManager  EditNode Ame.timschneeberger.rootlessjamesdsp.editor.plugin.UndoRedoManager  TextChangeWatcher Ame.timschneeberger.rootlessjamesdsp.editor.plugin.UndoRedoManager  Constant 1me.timschneeberger.rootlessjamesdsp.editor.syntax  EelLanguage 1me.timschneeberger.rootlessjamesdsp.editor.syntax  Function 1me.timschneeberger.rootlessjamesdsp.editor.syntax  SymbolInputView 1me.timschneeberger.rootlessjamesdsp.editor.widget  AppCompatibilityFragment ,me.timschneeberger.rootlessjamesdsp.fragment  AppsListFragment ,me.timschneeberger.rootlessjamesdsp.fragment  BlocklistFragment ,me.timschneeberger.rootlessjamesdsp.fragment  CompanderDialogFragment ,me.timschneeberger.rootlessjamesdsp.fragment  DeviceProfilesCardFragment ,me.timschneeberger.rootlessjamesdsp.fragment  DspFragment ,me.timschneeberger.rootlessjamesdsp.fragment  EqualizerDialogFragment ,me.timschneeberger.rootlessjamesdsp.fragment  FileLibraryDialogFragment ,me.timschneeberger.rootlessjamesdsp.fragment  GraphicEqualizerFragment ,me.timschneeberger.rootlessjamesdsp.fragment  LibraryLoadErrorFragment ,me.timschneeberger.rootlessjamesdsp.fragment  LimitationsFragment ,me.timschneeberger.rootlessjamesdsp.fragment  OnboardingFragment ,me.timschneeberger.rootlessjamesdsp.fragment  PreferenceGroupFragment ,me.timschneeberger.rootlessjamesdsp.fragment  	Companion Eme.timschneeberger.rootlessjamesdsp.fragment.AppCompatibilityFragment  	Companion =me.timschneeberger.rootlessjamesdsp.fragment.AppsListFragment  	Companion >me.timschneeberger.rootlessjamesdsp.fragment.BlocklistFragment  	Companion Dme.timschneeberger.rootlessjamesdsp.fragment.CompanderDialogFragment  	Companion Gme.timschneeberger.rootlessjamesdsp.fragment.DeviceProfilesCardFragment  	Companion 8me.timschneeberger.rootlessjamesdsp.fragment.DspFragment  	Companion Dme.timschneeberger.rootlessjamesdsp.fragment.EqualizerDialogFragment  	Companion Fme.timschneeberger.rootlessjamesdsp.fragment.FileLibraryDialogFragment  ListItemAdapter Fme.timschneeberger.rootlessjamesdsp.fragment.FileLibraryDialogFragment  	Companion Eme.timschneeberger.rootlessjamesdsp.fragment.GraphicEqualizerFragment  	Companion Eme.timschneeberger.rootlessjamesdsp.fragment.LibraryLoadErrorFragment  	Companion ?me.timschneeberger.rootlessjamesdsp.fragment.OnboardingFragment  	Companion Dme.timschneeberger.rootlessjamesdsp.fragment.PreferenceGroupFragment  SettingsAboutFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsAudioFormatFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsBackupFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsBaseFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsMiscFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsTroubleshootingFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  	Companion Kme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsAboutFragment  	Companion Qme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment  	Companion Lme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsBackupFragment  	Companion Jme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsMiscFragment  	Companion Ume.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment  JamesDspBaseEngine +me.timschneeberger.rootlessjamesdsp.interop  JamesDspLocalEngine +me.timschneeberger.rootlessjamesdsp.interop  JamesDspRemoteEngine +me.timschneeberger.rootlessjamesdsp.interop  JamesDspWrapper +me.timschneeberger.rootlessjamesdsp.interop  PreferenceCache +me.timschneeberger.rootlessjamesdsp.interop  ProcessorMessageHandler +me.timschneeberger.rootlessjamesdsp.interop  	Companion @me.timschneeberger.rootlessjamesdsp.interop.JamesDspRemoteEngine  	Companion ;me.timschneeberger.rootlessjamesdsp.interop.PreferenceCache  
EelVmVariable 5me.timschneeberger.rootlessjamesdsp.interop.structure  EelBaseProperty ,me.timschneeberger.rootlessjamesdsp.liveprog  EelListProperty ,me.timschneeberger.rootlessjamesdsp.liveprog  EelNumberRangeProperty ,me.timschneeberger.rootlessjamesdsp.liveprog  	EelParser ,me.timschneeberger.rootlessjamesdsp.liveprog  IPropertyCompanion ,me.timschneeberger.rootlessjamesdsp.liveprog  	Companion Cme.timschneeberger.rootlessjamesdsp.liveprog.EelNumberRangeProperty  IEffectSession )me.timschneeberger.rootlessjamesdsp.model  
ItemViewModel )me.timschneeberger.rootlessjamesdsp.model  AeqSearchResult -me.timschneeberger.rootlessjamesdsp.model.api  Preset 0me.timschneeberger.rootlessjamesdsp.model.preset  	Companion 7me.timschneeberger.rootlessjamesdsp.model.preset.Preset  AppBlocklistDao .me.timschneeberger.rootlessjamesdsp.model.room  AppBlocklistDatabase .me.timschneeberger.rootlessjamesdsp.model.room  AppBlocklistRepository .me.timschneeberger.rootlessjamesdsp.model.room  AppBlocklistViewModel .me.timschneeberger.rootlessjamesdsp.model.room  
BlockedApp .me.timschneeberger.rootlessjamesdsp.model.room  AppBlocklistDatabaseCallback Cme.timschneeberger.rootlessjamesdsp.model.room.AppBlocklistDatabase  	Companion Cme.timschneeberger.rootlessjamesdsp.model.room.AppBlocklistDatabase  RemoteEffectSession .me.timschneeberger.rootlessjamesdsp.model.root  MutedEffectSession 2me.timschneeberger.rootlessjamesdsp.model.rootless  CompanderPreference .me.timschneeberger.rootlessjamesdsp.preference  DropDownPreference .me.timschneeberger.rootlessjamesdsp.preference  EqualizerPreference .me.timschneeberger.rootlessjamesdsp.preference  FileLibraryPreference .me.timschneeberger.rootlessjamesdsp.preference  GraphicEqualizerPreference .me.timschneeberger.rootlessjamesdsp.preference  MaterialSeekbarPreference .me.timschneeberger.rootlessjamesdsp.preference  SwitchPreferenceGroup .me.timschneeberger.rootlessjamesdsp.preference  ThemesPreference .me.timschneeberger.rootlessjamesdsp.preference  	Companion Dme.timschneeberger.rootlessjamesdsp.preference.FileLibraryPreference  	Companion Dme.timschneeberger.rootlessjamesdsp.preference.SwitchPreferenceGroup  BootCompletedReceiver ,me.timschneeberger.rootlessjamesdsp.receiver  BaseAudioProcessorService +me.timschneeberger.rootlessjamesdsp.service  QuickTileService +me.timschneeberger.rootlessjamesdsp.service  RootAudioProcessorService +me.timschneeberger.rootlessjamesdsp.service  RootlessAudioProcessorService +me.timschneeberger.rootlessjamesdsp.service  	Companion Eme.timschneeberger.rootlessjamesdsp.service.BaseAudioProcessorService  LocalBinder Eme.timschneeberger.rootlessjamesdsp.service.BaseAudioProcessorService  	Companion Eme.timschneeberger.rootlessjamesdsp.service.RootAudioProcessorService  	Companion Ime.timschneeberger.rootlessjamesdsp.service.RootlessAudioProcessorService  DumpManager 0me.timschneeberger.rootlessjamesdsp.session.dump  ISessionInfoDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  ISessionPolicyInfoDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  RootSessionDatabase 0me.timschneeberger.rootlessjamesdsp.session.root  RootSessionDumpManager 0me.timschneeberger.rootlessjamesdsp.session.root  RootlessSessionDatabase 4me.timschneeberger.rootlessjamesdsp.session.rootless  RootlessSessionManager 4me.timschneeberger.rootlessjamesdsp.session.rootless  SessionRecordingPolicyManager 4me.timschneeberger.rootlessjamesdsp.session.rootless  	Companion Lme.timschneeberger.rootlessjamesdsp.session.rootless.RootlessSessionDatabase  BaseSessionDatabase 2me.timschneeberger.rootlessjamesdsp.session.shared  BaseSessionManager 2me.timschneeberger.rootlessjamesdsp.session.shared  EngineUtils )me.timschneeberger.rootlessjamesdsp.utils  MutedAudioEffectFactory )me.timschneeberger.rootlessjamesdsp.utils  ProfileManager )me.timschneeberger.rootlessjamesdsp.utils  RoutingObserver )me.timschneeberger.rootlessjamesdsp.utils  SdkCheck )me.timschneeberger.rootlessjamesdsp.utils  	Companion Ame.timschneeberger.rootlessjamesdsp.utils.MutedAudioEffectFactory  	Companion 8me.timschneeberger.rootlessjamesdsp.utils.ProfileManager  Device 9me.timschneeberger.rootlessjamesdsp.utils.RoutingObserver  DeviceGroup 9me.timschneeberger.rootlessjamesdsp.utils.RoutingObserver  	Companion Eme.timschneeberger.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup  ContextExtensions 4me.timschneeberger.rootlessjamesdsp.utils.extensions  ServiceNotificationHelper 7me.timschneeberger.rootlessjamesdsp.utils.notifications  NonPersistentDatastore 5me.timschneeberger.rootlessjamesdsp.utils.preferences  Preferences 5me.timschneeberger.rootlessjamesdsp.utils.preferences  AbstractPreferences Ame.timschneeberger.rootlessjamesdsp.utils.preferences.Preferences  Cache 1me.timschneeberger.rootlessjamesdsp.utils.storage  Tar 1me.timschneeberger.rootlessjamesdsp.utils.storage  Provider 7me.timschneeberger.rootlessjamesdsp.utils.storage.Cache  	Companion @me.timschneeberger.rootlessjamesdsp.utils.storage.Cache.Provider  Composer 5me.timschneeberger.rootlessjamesdsp.utils.storage.Tar  BaseEqualizerSurface (me.timschneeberger.rootlessjamesdsp.view  Card (me.timschneeberger.rootlessjamesdsp.view  CompanderSurface (me.timschneeberger.rootlessjamesdsp.view  EqualizerSurface (me.timschneeberger.rootlessjamesdsp.view  FloatingToggleButton (me.timschneeberger.rootlessjamesdsp.view  GraphicEqualizerSurface (me.timschneeberger.rootlessjamesdsp.view  NumberInputBox (me.timschneeberger.rootlessjamesdsp.view  ProgressDialog (me.timschneeberger.rootlessjamesdsp.view  	Companion 9me.timschneeberger.rootlessjamesdsp.view.CompanderSurface  	Companion 9me.timschneeberger.rootlessjamesdsp.view.EqualizerSurface  	Companion @me.timschneeberger.rootlessjamesdsp.view.GraphicEqualizerSurface  NotificationListenerService android.service.notification  ActivityResultContract !androidx.activity.result.contract  AlertDialog androidx.appcompat.app  ViewModelProvider androidx.lifecycle  PreferenceDataStore androidx.preference  PreferenceGroupAdapter androidx.preference  DiffUtil androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Locale 	java.util  String kotlin  AppCompatibilityActivity ,me.timschneeberger.rootlessjamesdsp.activity  GraphicEqualizerActivity ,me.timschneeberger.rootlessjamesdsp.activity  LiveprogParamsActivity ,me.timschneeberger.rootlessjamesdsp.activity  	Companion Cme.timschneeberger.rootlessjamesdsp.activity.LiveprogParamsActivity  FakePresetFragment 9me.timschneeberger.rootlessjamesdsp.activity.MainActivity  	Companion Lme.timschneeberger.rootlessjamesdsp.activity.MainActivity.FakePresetFragment  #RoundedRipplePreferenceGroupAdapter +me.timschneeberger.rootlessjamesdsp.adapter  BlockedAppComparator ?me.timschneeberger.rootlessjamesdsp.adapter.AppBlocklistAdapter  OnItemClickListener Cme.timschneeberger.rootlessjamesdsp.adapter.ThemesPreferenceAdapter  UserAgentInterceptor 'me.timschneeberger.rootlessjamesdsp.api  AutoEqSelectorContract ,me.timschneeberger.rootlessjamesdsp.contract  	Companion Cme.timschneeberger.rootlessjamesdsp.contract.AutoEqSelectorContract  ThemingDelegate -me.timschneeberger.rootlessjamesdsp.delegates  	Companion =me.timschneeberger.rootlessjamesdsp.delegates.ThemingDelegate  LiveprogParamsFragment ,me.timschneeberger.rootlessjamesdsp.fragment  Entry Fme.timschneeberger.rootlessjamesdsp.fragment.FileLibraryDialogFragment  	Companion Cme.timschneeberger.rootlessjamesdsp.fragment.LiveprogParamsFragment  SettingsAppearanceFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsDeviceProfilesFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  SettingsFragment 5me.timschneeberger.rootlessjamesdsp.fragment.settings  	Companion Pme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment  	Companion Tme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment  	Companion Fme.timschneeberger.rootlessjamesdsp.fragment.settings.SettingsFragment  DummyCallbacks >me.timschneeberger.rootlessjamesdsp.interop.JamesDspBaseEngine  JamesDspCallbacks ;me.timschneeberger.rootlessjamesdsp.interop.JamesDspWrapper  	Companion <me.timschneeberger.rootlessjamesdsp.liveprog.EelListProperty  AppInfo )me.timschneeberger.rootlessjamesdsp.model  AudioSessionDumpEntry )me.timschneeberger.rootlessjamesdsp.model  ProcessorMessage )me.timschneeberger.rootlessjamesdsp.model  	Companion ?me.timschneeberger.rootlessjamesdsp.model.AudioSessionDumpEntry  AppBlocklistViewModelFactory .me.timschneeberger.rootlessjamesdsp.model.room  SessionRecordingPolicyEntry 2me.timschneeberger.rootlessjamesdsp.model.rootless  AppIconPreference .me.timschneeberger.rootlessjamesdsp.preference  PowerStateReceiver ,me.timschneeberger.rootlessjamesdsp.receiver  SessionReceiver ,me.timschneeberger.rootlessjamesdsp.receiver  	Companion ?me.timschneeberger.rootlessjamesdsp.receiver.PowerStateReceiver  NotificationListenerService +me.timschneeberger.rootlessjamesdsp.service  OnDumpMethodChangeListener <me.timschneeberger.rootlessjamesdsp.session.dump.DumpManager  AudioPolicyServiceDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  AudioServiceDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  IDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  PackageServiceDump 5me.timschneeberger.rootlessjamesdsp.session.dump.data  AudioFlingerServiceDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  AudioPolicyServiceDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  AudioServiceDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  
IDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  ISessionDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  ISessionPolicyDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  PackageServiceDumpProvider 9me.timschneeberger.rootlessjamesdsp.session.dump.provider  	Companion Yme.timschneeberger.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider  	Companion Xme.timschneeberger.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider  	Companion Rme.timschneeberger.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider  	Companion Tme.timschneeberger.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider  AudioFlingerServiceDumpUtils 6me.timschneeberger.rootlessjamesdsp.session.dump.utils  Dataset Sme.timschneeberger.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils  OnSessionChangeListener Fme.timschneeberger.rootlessjamesdsp.session.shared.BaseSessionDatabase  RoutingChangedCallback 9me.timschneeberger.rootlessjamesdsp.utils.RoutingObserver  OnPreferenceChanged Lme.timschneeberger.rootlessjamesdsp.utils.preferences.NonPersistentDatastore  Interceptor okhttp3  SpatialAudioCardFragment ,me.timschneeberger.rootlessjamesdsp.fragment  	Companion Eme.timschneeberger.rootlessjamesdsp.fragment.SpatialAudioCardFragment  WhaleAnimationView (me.timschneeberger.rootlessjamesdsp.view                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    