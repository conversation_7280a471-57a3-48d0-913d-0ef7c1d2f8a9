<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    android:background="?android:attr/selectableItemBackground">
    <LinearLayout
        android:layout_margin="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@android:id/icon"
            android:layout_marginEnd="16dp"
            android:layout_gravity="center_vertical"
            android:layout_height="match_parent"
            android:layout_width="38dp"
            android:contentDescription="@string/app_list_icon_alt" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:id="@android:id/title"
                android:textAppearance="?attr/textAppearanceListItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:maxLines="1"
                android:ellipsize="end" />
            <TextView
                android:id="@android:id/summary"
                android:textAppearance="?attr/textAppearanceListItemSecondary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:ellipsize="end"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>