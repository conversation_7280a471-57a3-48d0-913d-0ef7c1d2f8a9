<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="vertical">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:alpha="0.8"
            app:srcCompat="@drawable/ic_twotone_security_24dp"
            app:tint="?attr/colorOnSurface"
            android:importantForAccessibility="no" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:text="@string/onboarding_adb_shizuku_title"
            android:textAppearance="?attr/textAppearanceHeadline4" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                android:text="@string/onboarding_adb_caption"
                android:textAppearance="?attr/textAppearanceBody1" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:bodyText="@string/onboarding_adb_shizuku_install_instruction"
                app:buttonText="@string/onboarding_adb_shizuku_install_button"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_1_circle_outline"
                app:iconTint="?attr/colorOnSurface" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:bodyText="@string/onboarding_adb_shizuku_open_instruction"
                app:buttonText="@string/onboarding_adb_shizuku_open_button"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_2_circle_outline"
                app:iconTint="?attr/colorOnSurface" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:bodyText="@string/onboarding_adb_shizuku_grant_instruction"
                app:buttonText="@string/onboarding_adb_shizuku_grant_button"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_3_circle_outline"
                app:iconTint="?attr/colorOnSurface" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_4_circle_outline"
                app:iconTint="?attr/colorOnSurface" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step5b"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_5_circle_outline"
                app:iconTint="?attr/colorOnSurface" />


            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step5c_optional"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_twotone_info_24dp"
                app:iconTint="?attr/colorOnSurface" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/step6"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardMargin="8dp"
                app:iconSrc="@drawable/ic_numeric_6_circle_outline"
                app:iconTint="?attr/colorOnSurface" />
        </LinearLayout>

    </ScrollView>

</LinearLayout>
