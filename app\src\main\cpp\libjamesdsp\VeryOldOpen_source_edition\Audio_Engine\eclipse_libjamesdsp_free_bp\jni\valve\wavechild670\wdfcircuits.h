//Autogenerated wave digital filter circuits
//<PERSON>

#include "../Misc.h"

typedef struct strTriodeRemoteCutoff6386
{
    Real VgkLast;
    Real numeratorLast;
    Real fa;
    Real aa;
    Real ab;
} TriodeRemoteCutoff6386;
typedef struct strWDFTubeInterface
{
    Real numParallelInstances;
    Real r0;
    Real a;
    Real Vgk;
    Real Iak;
    Real VakGuess;
    TriodeRemoteCutoff6386 model;
} WDFTubeInterface;
typedef struct strBidirectionalUnitDelayInterface
{
    Real a;
    Real b;
} BidirectionalUnitDelayInterface;

typedef struct strBidirectionalUnitDelay
{
    BidirectionalUnitDelayInterface interface0;
    BidirectionalUnitDelayInterface interface1;
} BidirectionalUnitDelay;
// AUTOGENERATED Wave digital filter 2012-03-14 15:24:51.132756
// Advanced Machine Audio Python WDF Generator
// <PERSON>, 2012
/*Circuit schematic:
Primary  Secondary
Np  :   Ns
----Rin-------Lp---Rp--------------B       B------Rs----Ls------------
|         |            |   |       B       B               |         |
Vin     RinTerm         Rc  Lm      B TXFMR B               Cw      Rload
|         |            |   |       B       B               |         |
-----------------------------------B       B--------------------------
B = winding symbol
*/
typedef struct strTransformerCoupledInputCircuit
{
    //State variables
    Real Lpa;
    Real Lma;
    Real Lsa;
    Real Cwa;

    //R values
    Real primarySeriesConn2_3Gamma1;
    Real seriesConn_3Gamma1;
    Real secondarySeriesConn2_3Gamma1;
    Real primaryInputSeriesConn_3Gamma1;
    Real parallelConn_3Gamma1;
    Real secondaryOutputParallelConn_3Gamma1;
    Real secondarySeriesConn1_3Gamma1;
    Real transformerOneOvern;
    Real inputSourceE;
    Real transformern;
    Real primaryParallelConn2_3Gamma1;
    Real primaryParallelConn1_3Gamma1;
} TransformerCoupledInputCircuit;



// AUTOGENERATED Wave digital filter 2012-03-14 15:24:51.156199
// Advanced Machine Audio Python WDF Generator
// Peter Raffensperger, 2012
/*Circuit schematic:
--------------------------
|       |    |     |     |
|       |    |     R2    R3
Iin      R1   C1    |     |
|       |    |     C2    C3
|       |    |     |     |
--------------------------
*/
typedef struct strLevelTimeConstantCircuit
{
    //State variables
    Real C1a;
    Real C2a;
    Real C3a;

    //R values
    Real serialConn2_3Gamma1;
    Real Rsource;
    Real parallelConn1_3Gamma1;
    Real serialConn3_3Gamma1;
    Real parallelConnInput_3Gamma1;
    Real parallelConn23_3Gamma1;
} LevelTimeConstantCircuit;

#ifndef WDFCIRCUITS_H
#define WDFCIRCUITS_H

// AUTOGENERATED Wave digital filter 2012-03-14 15:24:51.084121
// Advanced Machine Audio Python WDF Generator
// Peter Raffensperger, 2012

/*Circuit schematic: (B = winding symbol)
VplateSource
|
Rplate                      Primary  Secondary
|                            Np  :   Ns
--------Lp---Rp--------------B       B------Rs----Ls------------------------
|   |       B       B               |         |           |
Rc  Lm      B TXFMR B               Cw      Routput   Rsidechain
|   |       B       B               |         |           |
-----------------------------B       B--------------------------------------
|
Vgate --Tube
|
-----------------
|               |
Rcathode        Ccathode
|               |
VcathodeSource   cathodeCapacitorConn
*/
typedef struct strTubeStageCircuit
{
    //State variables
    Real Ccathodea;
    Real Lpa;
    Real Lma;
    Real Lsa;
    Real Cwa;
    Real Vcathode;

    //R values
    Real outputParallelConn_3Gamma1;
    Real primarySeriesConn2_3Gamma1;
    Real cathodeCapSeriesConn_3Gamma1;
    Real tubeSeriesConn1_3Gamma1;
    Real VplateE;
    Real secondarySeriesConn2_3Gamma1;
    Real primaryInputSeriesConn_3Gamma1;
    Real secondaryOutputParallelConn_3Gamma1;
    Real cathodeParallelConn_3Gamma1;
    Real secondarySeriesConn1_3Gamma1;
    Real transformerOneOvern;
    Real transformern;
    Real VcathodeBiasE;
    Real tubeSeriesConn2_3Gamma1;
    Real primaryParallelConn2_3Gamma1;
    Real primaryParallelConn1_3Gamma1;
    Real tubeR;
    //Extra members
    WDFTubeInterface tube;
} TubeStageCircuit;

TriodeRemoteCutoff6386 TriodeRemoteCutoff6386Init();
Real WDFTubeInterfaceGetB(WDFTubeInterface *WDFTI, Real a_, Real r0_, Real Vgate, Real Vk);
void WDFTubeInterface3ArgInit(WDFTubeInterface *WDFTI, TriodeRemoteCutoff6386 model_, Real numParallelInstances);
WDFTubeInterface WDFTubeInterface1ArgInit(WDFTubeInterface *other);
void BidirectionalUnitDelayInterfaceInit(BidirectionalUnitDelayInterface * BUDI);
void TubeStageCircuitInit(TubeStageCircuit *TSC, Real C_Ccathode, Real C_Cw, Real E_VcathodeBias, Real E_Vplate, Real L_Lm, Real L_Lp, Real L_Ls, Real NpOverNs, Real R_Rc, Real R_Routput, Real R_Rp, Real R_Rs, Real R_Rsidechain, Real R_VcathodeBias, Real R_Vplate, Real R_cathodeCapacitorConn, Real sampleRate, WDFTubeInterface *tube_);
Real TubeStageCircuitAdvance(TubeStageCircuit *TSC, Real vgate, BidirectionalUnitDelayInterface *cathodeCapacitorConn);
void TransformerCoupledInputCircuitInit(TransformerCoupledInputCircuit *TCC, Real C_Cw, Real E_inputSource, Real L_Lm, Real L_Lp, Real L_Ls, Real NpOverNs, Real R_Rc, Real R_RinputTermination, Real R_Rload, Real R_Rp, Real R_Rs, Real R_inputSource, Real sampleRate);
Real TransformerCoupledInputCircuitAdvance(TransformerCoupledInputCircuit *TCC, Real vin);
void LevelTimeConstantCircuitInit(LevelTimeConstantCircuit *LTCC, Real C_C1, Real C_C2, Real C_C3, Real R_R1, Real R_R2, Real R_R3, Real sampleRate);
void LevelTimeConstantCircuitUpdateRValues(LevelTimeConstantCircuit *LTCC, Real C_C1, Real C_C2, Real C_C3, Real R_R1, Real R_R2, Real R_R3, Real sampleRate);
Real LevelTimeConstantCircuitAdvance(LevelTimeConstantCircuit *LTCC, Real Iin);
#endif
