<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_dsp" modulePackage="me.timschneeberger.rootlessjamesdsp" filePath="app\src\main\res\layout\fragment_dsp.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/dsp_scrollview"><Targets><Target id="@+id/dsp_scrollview" tag="layout/fragment_dsp_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="193" endOffset="39"/></Target><Target id="@+id/card_container" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="192" endOffset="18"/></Target><Target id="@+id/translation_notice" view="me.timschneeberger.rootlessjamesdsp.view.Card"><Expressions/><location startLine="21" startOffset="8" endLine="33" endOffset="60"/></Target><Target id="@+id/update_notice" view="me.timschneeberger.rootlessjamesdsp.view.Card"><Expressions/><location startLine="35" startOffset="8" endLine="47" endOffset="60"/></Target><Target id="@+id/card_device_profiles" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="54" startOffset="12" endLine="57" endOffset="54"/></Target><Target id="@+id/card_output_control" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="65" startOffset="12" endLine="68" endOffset="54"/></Target><Target id="@+id/card_compressor" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="76" startOffset="12" endLine="79" endOffset="54"/></Target><Target id="@+id/card_bass" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="87" startOffset="12" endLine="90" endOffset="54"/></Target><Target id="@+id/card_eq" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="98" startOffset="12" endLine="101" endOffset="54"/></Target><Target id="@+id/card_geq" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="109" startOffset="12" endLine="112" endOffset="54"/></Target><Target id="@+id/card_ddc" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="120" startOffset="12" endLine="123" endOffset="54"/></Target><Target id="@+id/card_convolver" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="131" startOffset="12" endLine="134" endOffset="54"/></Target><Target id="@+id/card_liveprog" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="142" startOffset="12" endLine="145" endOffset="54"/></Target><Target id="@+id/card_tube" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="153" startOffset="12" endLine="156" endOffset="54"/></Target><Target id="@+id/card_stereowide" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="164" startOffset="12" endLine="167" endOffset="54"/></Target><Target id="@+id/card_crossfeed" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="175" startOffset="12" endLine="178" endOffset="54"/></Target><Target id="@+id/card_reverb" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="187" startOffset="12" endLine="190" endOffset="54"/></Target></Targets></Layout>