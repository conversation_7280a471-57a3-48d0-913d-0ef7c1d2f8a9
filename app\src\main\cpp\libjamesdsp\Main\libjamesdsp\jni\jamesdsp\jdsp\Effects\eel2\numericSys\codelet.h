void DFT1024(float *A, const float *sinTab);
void DFT1048576(float *A, const float *sinTab);
void DFT128(float *A, const float *sinTab);
void DFT131072(float *A, const float *sinTab);
void DFT16(float *A, const float *sinTab);
void DFT16384(float *A, const float *sinTab);
void DFT2(float *A, const float *sinTab);
void DFT2048(float *A, const float *sinTab);
void DFT256(float *A, const float *sinTab);
void DFT262144(float *A, const float *sinTab);
void DFT32(float *A, const float *sinTab);
void DFT32768(float *A, const float *sinTab);
void DFT4(float *A, const float *sinTab);
void DFT4096(float *A, const float *sinTab);
void DFT512(float *A, const float *sinTab);
void DFT524288(float *A, const float *sinTab);
void DFT64(float *A, const float *sinTab);
void DFT65536(float *A, const float *sinTab);
void DFT8(float *A, const float *sinTab);
void DFT8192(float *A, const float *sinTab);
