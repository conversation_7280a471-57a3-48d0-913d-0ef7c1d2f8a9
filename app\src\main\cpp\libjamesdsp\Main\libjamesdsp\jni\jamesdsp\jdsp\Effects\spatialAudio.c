#include "spatialAudio.h"
#include <math.h>
#include <string.h>
#include <stdlib.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Initialize spatial audio processor
void SpatialAudioInit(SpatialAudio *spatial, float sample_rate, int block_size) {
    if (!spatial) return;
    
    memset(spatial, 0, sizeof(SpatialAudio));
    
    spatial->sample_rate = sample_rate;
    spatial->block_size = block_size;
    spatial->mode = SPATIAL_MODE_DISABLED;
    spatial->head_tracking_mode = HEAD_TRACKING_DISABLED;
    spatial->room_type = ROOM_TYPE_NONE;
    spatial->enabled = 0;
    
    // Initialize default parameters
    spatial->stereo_width = 1.0f;
    spatial->room_size = 0.5f;
    spatial->room_damping = 0.5f;
    spatial->distance_attenuation = 0.7f;
    spatial->doppler_factor = 1.0f;
    spatial->crossfeed_strength = 0.3f;
    spatial->listener_height = 1.7f; // Average human height in meters
    
    // Initialize source position (front center)
    spatial->source_position.x = 0.0f;
    spatial->source_position.y = 0.0f;
    spatial->source_position.z = 1.0f;
    spatial->source_position.azimuth = 0.0f;
    spatial->source_position.elevation = 0.0f;
    spatial->source_position.distance = 1.0f;
    
    // Initialize head orientation
    spatial->head_orientation.yaw = 0.0f;
    spatial->head_orientation.pitch = 0.0f;
    spatial->head_orientation.roll = 0.0f;
    
    // Allocate processing buffers
    int buffer_size = block_size * 4; // Extra space for processing
    spatial->temp_left = (float*)calloc(buffer_size, sizeof(float));
    spatial->temp_right = (float*)calloc(buffer_size, sizeof(float));
    spatial->temp_mono = (float*)calloc(buffer_size, sizeof(float));
    
    // Allocate delay lines for distance simulation
    spatial->delay_line_size = (int)(sample_rate * 0.1f); // 100ms max delay
    spatial->delay_line_left = (float*)calloc(spatial->delay_line_size, sizeof(float));
    spatial->delay_line_right = (float*)calloc(spatial->delay_line_size, sizeof(float));
    spatial->delay_write_pos = 0;
    spatial->current_delay_samples = 0.0f;
    
    // Allocate crossfeed delay buffers
    spatial->crossfeed_delay_size = (int)(sample_rate * 0.001f); // 1ms max delay
    spatial->crossfeed_delay_left = (float*)calloc(spatial->crossfeed_delay_size, sizeof(float));
    spatial->crossfeed_delay_right = (float*)calloc(spatial->crossfeed_delay_size, sizeof(float));
    spatial->crossfeed_delay_pos = 0;
    spatial->crossfeed_delay_samples = sample_rate * 0.0003f; // 0.3ms default
    
    // Initialize room simulation buffers
    spatial->room_reverb_size = (int)(sample_rate * 0.5f); // 500ms reverb tail
    spatial->room_reverb_left = (float*)calloc(spatial->room_reverb_size, sizeof(float));
    spatial->room_reverb_right = (float*)calloc(spatial->room_reverb_size, sizeof(float));
    spatial->room_reverb_feedback = 0.3f;
    spatial->room_reverb_mix = 0.2f;
    
    // Initialize head tracking simulation
    spatial->head_tracking_speed = 0.5f;
    spatial->head_tracking_amplitude = 0.1f;
    spatial->head_tracking_phase = 0.0;
    
    // Initialize HRTF buffers (will be allocated when HRTF data is loaded)
    spatial->hrtf_database = NULL;
    spatial->hrtf_count = 0;
    spatial->hrtf_buffer_size = 256; // Default HRTF impulse length
    spatial->hrtf_left_buffer = (float*)calloc(spatial->hrtf_buffer_size, sizeof(float));
    spatial->hrtf_right_buffer = (float*)calloc(spatial->hrtf_buffer_size, sizeof(float));
    
    // Initialize convolution buffers for HRTF processing
    spatial->conv_buffer_size = block_size + spatial->hrtf_buffer_size - 1;
    spatial->overlap_size = spatial->hrtf_buffer_size - 1;
    spatial->conv_buffer_left = (float*)calloc(spatial->conv_buffer_size, sizeof(float));
    spatial->conv_buffer_right = (float*)calloc(spatial->conv_buffer_size, sizeof(float));
    spatial->overlap_buffer_left = (float*)calloc(spatial->overlap_size, sizeof(float));
    spatial->overlap_buffer_right = (float*)calloc(spatial->overlap_size, sizeof(float));
    
    spatial->force_refresh = 1;
    spatial->processing_enabled = 0;
}

// Free spatial audio processor
void SpatialAudioFree(SpatialAudio *spatial) {
    if (!spatial) return;
    
    // Free processing buffers
    if (spatial->temp_left) { free(spatial->temp_left); spatial->temp_left = NULL; }
    if (spatial->temp_right) { free(spatial->temp_right); spatial->temp_right = NULL; }
    if (spatial->temp_mono) { free(spatial->temp_mono); spatial->temp_mono = NULL; }
    
    // Free delay lines
    if (spatial->delay_line_left) { free(spatial->delay_line_left); spatial->delay_line_left = NULL; }
    if (spatial->delay_line_right) { free(spatial->delay_line_right); spatial->delay_line_right = NULL; }
    
    // Free crossfeed buffers
    if (spatial->crossfeed_delay_left) { free(spatial->crossfeed_delay_left); spatial->crossfeed_delay_left = NULL; }
    if (spatial->crossfeed_delay_right) { free(spatial->crossfeed_delay_right); spatial->crossfeed_delay_right = NULL; }
    
    // Free room simulation buffers
    if (spatial->room_reverb_left) { free(spatial->room_reverb_left); spatial->room_reverb_left = NULL; }
    if (spatial->room_reverb_right) { free(spatial->room_reverb_right); spatial->room_reverb_right = NULL; }
    
    // Free HRTF buffers
    if (spatial->hrtf_left_buffer) { free(spatial->hrtf_left_buffer); spatial->hrtf_left_buffer = NULL; }
    if (spatial->hrtf_right_buffer) { free(spatial->hrtf_right_buffer); spatial->hrtf_right_buffer = NULL; }
    
    // Free convolution buffers
    if (spatial->conv_buffer_left) { free(spatial->conv_buffer_left); spatial->conv_buffer_left = NULL; }
    if (spatial->conv_buffer_right) { free(spatial->conv_buffer_right); spatial->conv_buffer_right = NULL; }
    if (spatial->overlap_buffer_left) { free(spatial->overlap_buffer_left); spatial->overlap_buffer_left = NULL; }
    if (spatial->overlap_buffer_right) { free(spatial->overlap_buffer_right); spatial->overlap_buffer_right = NULL; }
    
    // Free HRTF database
    if (spatial->hrtf_database) {
        for (int i = 0; i < spatial->hrtf_count; i++) {
            if (spatial->hrtf_database[i].left_impulse) {
                free(spatial->hrtf_database[i].left_impulse);
            }
            if (spatial->hrtf_database[i].right_impulse) {
                free(spatial->hrtf_database[i].right_impulse);
            }
        }
        free(spatial->hrtf_database);
        spatial->hrtf_database = NULL;
    }
    
    memset(spatial, 0, sizeof(SpatialAudio));
}

// Main processing function
void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !spatial->enabled || !spatial->processing_enabled || samples <= 0) {
        return;
    }
    
    // Update head tracking if enabled
    if (spatial->head_tracking_mode != HEAD_TRACKING_DISABLED) {
        SpatialAudioProcessHeadTracking(spatial);
    }
    
    // Process based on selected mode
    switch (spatial->mode) {
        case SPATIAL_MODE_STEREO_WIDENING:
            SpatialAudioProcessStereoWidening(spatial, left, right, samples);
            break;
            
        case SPATIAL_MODE_BINAURAL:
            SpatialAudioProcessBinaural(spatial, left, right, samples);
            break;
            
        case SPATIAL_MODE_SURROUND:
            SpatialAudioProcessBinaural(spatial, left, right, samples);
            SpatialAudioProcessRoomSimulation(spatial, left, right, samples);
            break;
            
        case SPATIAL_MODE_HRTF:
            SpatialAudioProcessHRTF(spatial, left, right, samples);
            break;
            
        default:
            break;
    }
    
    // Apply crossfeed for headphone listening
    if (spatial->crossfeed_strength > 0.0f) {
        SpatialAudioProcessCrossfeed(spatial, left, right, samples);
    }
}

// Configuration functions
void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode) {
    if (!spatial) return;
    spatial->mode = mode;
    spatial->force_refresh = 1;
}

void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode) {
    if (!spatial) return;
    spatial->head_tracking_mode = mode;
}

void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type) {
    if (!spatial) return;
    spatial->room_type = room_type;
    
    // Adjust room parameters based on type
    switch (room_type) {
        case ROOM_TYPE_SMALL:
            spatial->room_size = 0.3f;
            spatial->room_damping = 0.7f;
            spatial->room_reverb_feedback = 0.2f;
            spatial->room_reverb_mix = 0.1f;
            break;
        case ROOM_TYPE_MEDIUM:
            spatial->room_size = 0.5f;
            spatial->room_damping = 0.5f;
            spatial->room_reverb_feedback = 0.3f;
            spatial->room_reverb_mix = 0.2f;
            break;
        case ROOM_TYPE_LARGE:
            spatial->room_size = 0.7f;
            spatial->room_damping = 0.3f;
            spatial->room_reverb_feedback = 0.4f;
            spatial->room_reverb_mix = 0.3f;
            break;
        case ROOM_TYPE_HALL:
            spatial->room_size = 0.9f;
            spatial->room_damping = 0.2f;
            spatial->room_reverb_feedback = 0.5f;
            spatial->room_reverb_mix = 0.4f;
            break;
        case ROOM_TYPE_OUTDOOR:
            spatial->room_size = 1.0f;
            spatial->room_damping = 0.9f;
            spatial->room_reverb_feedback = 0.1f;
            spatial->room_reverb_mix = 0.05f;
            break;
        default:
            spatial->room_reverb_mix = 0.0f;
            break;
    }
}

void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z) {
    if (!spatial) return;
    
    spatial->source_position.x = x;
    spatial->source_position.y = y;
    spatial->source_position.z = z;
    
    // Convert to polar coordinates
    SpatialAudioCartesianToPolar(x, y, z, 
                                &spatial->source_position.azimuth,
                                &spatial->source_position.elevation,
                                &spatial->source_position.distance);
    
    spatial->force_refresh = 1;
}

void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll) {
    if (!spatial) return;
    
    spatial->head_orientation.yaw = yaw;
    spatial->head_orientation.pitch = pitch;
    spatial->head_orientation.roll = roll;
    
    spatial->force_refresh = 1;
}

void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width) {
    if (!spatial) return;
    spatial->stereo_width = fmaxf(0.0f, fminf(2.0f, width));
}

void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping) {
    if (!spatial) return;
    spatial->room_size = fmaxf(0.0f, fminf(1.0f, size));
    spatial->room_damping = fmaxf(0.0f, fminf(1.0f, damping));
}

void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation) {
    if (!spatial) return;
    spatial->distance_attenuation = fmaxf(0.0f, fminf(1.0f, attenuation));
}

void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength) {
    if (!spatial) return;
    spatial->crossfeed_strength = fmaxf(0.0f, fminf(1.0f, strength));
}

// Utility functions
void SpatialAudioCartesianToPolar(float x, float y, float z, float *azimuth, float *elevation, float *distance) {
    if (!azimuth || !elevation || !distance) return;

    *distance = sqrtf(x*x + y*y + z*z);
    if (*distance < 0.001f) {
        *azimuth = 0.0f;
        *elevation = 0.0f;
        *distance = 0.001f;
        return;
    }

    *azimuth = atan2f(x, z);
    *elevation = asinf(y / *distance);
}

void SpatialAudioPolarToCartesian(float azimuth, float elevation, float distance, float *x, float *y, float *z) {
    if (!x || !y || !z) return;

    float cos_elevation = cosf(elevation);
    *x = distance * cos_elevation * sinf(azimuth);
    *y = distance * sinf(elevation);
    *z = distance * cos_elevation * cosf(azimuth);
}

float SpatialAudioCalculateDelay(float distance, float sample_rate) {
    // Speed of sound is approximately 343 m/s
    return (distance / 343.0f) * sample_rate;
}

float SpatialAudioCalculateAttenuation(float distance, float attenuation_factor) {
    // Inverse square law with adjustable factor
    if (distance < 0.1f) distance = 0.1f;
    return 1.0f / (1.0f + attenuation_factor * distance * distance);
}

// Processing helper functions
void SpatialAudioProcessStereoWidening(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !left || !right) return;

    float width = spatial->stereo_width;
    if (width == 1.0f) return; // No processing needed

    for (int i = 0; i < samples; i++) {
        float mid = (left[i] + right[i]) * 0.5f;
        float side = (left[i] - right[i]) * 0.5f * width;

        left[i] = mid + side;
        right[i] = mid - side;
    }
}

void SpatialAudioProcessBinaural(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !left || !right) return;

    // Calculate effective azimuth considering head orientation
    float effective_azimuth = spatial->source_position.azimuth - spatial->head_orientation.yaw;
    float distance = spatial->source_position.distance;

    // Calculate delay and attenuation for each ear
    float delay_samples = SpatialAudioCalculateDelay(distance, spatial->sample_rate);
    float attenuation = SpatialAudioCalculateAttenuation(distance, spatial->distance_attenuation);

    // Simple binaural panning based on azimuth
    float pan_left = 0.5f * (1.0f - sinf(effective_azimuth));
    float pan_right = 0.5f * (1.0f + sinf(effective_azimuth));

    // Apply panning and attenuation
    for (int i = 0; i < samples; i++) {
        float mono = (left[i] + right[i]) * 0.5f * attenuation;
        left[i] = mono * pan_left;
        right[i] = mono * pan_right;
    }
}

void SpatialAudioProcessHRTF(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !left || !right || !spatial->hrtf_left_buffer || !spatial->hrtf_right_buffer) return;

    // This is a simplified HRTF implementation
    // In a full implementation, this would use proper convolution with HRTF impulse responses

    // For now, fall back to binaural processing
    SpatialAudioProcessBinaural(spatial, left, right, samples);
}

void SpatialAudioProcessRoomSimulation(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !left || !right || spatial->room_reverb_mix <= 0.0f) return;

    // Simple room simulation using delay and feedback
    float feedback = spatial->room_reverb_feedback;
    float mix = spatial->room_reverb_mix;
    int reverb_size = spatial->room_reverb_size;

    for (int i = 0; i < samples; i++) {
        // Calculate reverb indices
        int read_pos = (spatial->delay_write_pos - (int)(reverb_size * spatial->room_size)) % reverb_size;
        if (read_pos < 0) read_pos += reverb_size;

        // Get reverb samples
        float reverb_left = spatial->room_reverb_left[read_pos];
        float reverb_right = spatial->room_reverb_right[read_pos];

        // Apply damping
        reverb_left *= (1.0f - spatial->room_damping);
        reverb_right *= (1.0f - spatial->room_damping);

        // Mix with input
        float output_left = left[i] + reverb_left * mix;
        float output_right = right[i] + reverb_right * mix;

        // Store in reverb buffer with feedback
        spatial->room_reverb_left[spatial->delay_write_pos] = left[i] + reverb_left * feedback;
        spatial->room_reverb_right[spatial->delay_write_pos] = right[i] + reverb_right * feedback;

        // Update output
        left[i] = output_left;
        right[i] = output_right;

        // Advance write position
        spatial->delay_write_pos = (spatial->delay_write_pos + 1) % reverb_size;
    }
}

void SpatialAudioProcessHeadTracking(SpatialAudio *spatial) {
    if (!spatial || spatial->head_tracking_mode == HEAD_TRACKING_DISABLED) return;

    if (spatial->head_tracking_mode == HEAD_TRACKING_SIMULATED) {
        // Simulate head movement with sine waves
        spatial->head_tracking_phase += spatial->head_tracking_speed * 0.01;
        if (spatial->head_tracking_phase > 2.0 * M_PI) {
            spatial->head_tracking_phase -= 2.0 * M_PI;
        }

        spatial->head_orientation.yaw = sinf(spatial->head_tracking_phase) * spatial->head_tracking_amplitude;
        spatial->head_orientation.pitch = cosf(spatial->head_tracking_phase * 0.7) * spatial->head_tracking_amplitude * 0.5f;
    }
}

void SpatialAudioProcessCrossfeed(SpatialAudio *spatial, float *left, float *right, int samples) {
    if (!spatial || !left || !right || spatial->crossfeed_strength <= 0.0f) return;

    float strength = spatial->crossfeed_strength;
    int delay_samples = (int)spatial->crossfeed_delay_samples;

    for (int i = 0; i < samples; i++) {
        // Get delayed samples
        int read_pos = (spatial->crossfeed_delay_pos - delay_samples) % spatial->crossfeed_delay_size;
        if (read_pos < 0) read_pos += spatial->crossfeed_delay_size;

        float delayed_left = spatial->crossfeed_delay_left[read_pos];
        float delayed_right = spatial->crossfeed_delay_right[read_pos];

        // Store current samples
        spatial->crossfeed_delay_left[spatial->crossfeed_delay_pos] = left[i];
        spatial->crossfeed_delay_right[spatial->crossfeed_delay_pos] = right[i];

        // Apply crossfeed
        left[i] = left[i] + delayed_right * strength;
        right[i] = right[i] + delayed_left * strength;

        // Advance delay position
        spatial->crossfeed_delay_pos = (spatial->crossfeed_delay_pos + 1) % spatial->crossfeed_delay_size;
    }
}

// Enable/disable functions
void SpatialAudioEnable(SpatialAudio *spatial) {
    if (!spatial) return;
    spatial->enabled = 1;
    spatial->processing_enabled = (spatial->mode != SPATIAL_MODE_DISABLED);
}

void SpatialAudioDisable(SpatialAudio *spatial) {
    if (!spatial) return;
    spatial->enabled = 0;
    spatial->processing_enabled = 0;
}

int SpatialAudioIsEnabled(SpatialAudio *spatial) {
    return spatial ? spatial->enabled : 0;
}

// JamesDSP integration functions
void SpatialAudioJDSPConstructor(JamesDSPLib *jdsp) {
    if (!jdsp) return;
    SpatialAudioInit(&jdsp->spatialAudio, jdsp->fs, jdsp->blockSize);
    jdsp->spatialAudioEnabled = 0;
}

void SpatialAudioJDSPDestructor(JamesDSPLib *jdsp) {
    if (!jdsp) return;
    SpatialAudioFree(&jdsp->spatialAudio);
    jdsp->spatialAudioEnabled = 0;
}

void SpatialAudioJDSPEnable(JamesDSPLib *jdsp, char enable) {
    if (!jdsp) return;
    if (enable) {
        SpatialAudioEnable(&jdsp->spatialAudio);
        jdsp->spatialAudioEnabled = 1;
    } else {
        SpatialAudioDisable(&jdsp->spatialAudio);
        jdsp->spatialAudioEnabled = 0;
    }
}

void SpatialAudioJDSPDisable(JamesDSPLib *jdsp) {
    if (!jdsp) return;
    SpatialAudioDisable(&jdsp->spatialAudio);
    jdsp->spatialAudioEnabled = 0;
}

void SpatialAudioJDSPProcess(JamesDSPLib *jdsp, size_t n) {
    if (!jdsp || !jdsp->spatialAudioEnabled) return;
    SpatialAudioProcess(&jdsp->spatialAudio, jdsp->tmpBuffer[0], jdsp->tmpBuffer[1], n);
}

void SpatialAudioJDSPSetMode(JamesDSPLib *jdsp, int mode) {
    if (!jdsp) return;
    SpatialAudioSetMode(&jdsp->spatialAudio, (SpatialAudioMode)mode);
}

void SpatialAudioJDSPSetHeadTracking(JamesDSPLib *jdsp, int mode) {
    if (!jdsp) return;
    SpatialAudioSetHeadTracking(&jdsp->spatialAudio, (HeadTrackingMode)mode);
}

void SpatialAudioJDSPSetRoomType(JamesDSPLib *jdsp, int room_type) {
    if (!jdsp) return;
    SpatialAudioSetRoomType(&jdsp->spatialAudio, (RoomType)room_type);
}

void SpatialAudioJDSPSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z) {
    if (!jdsp) return;
    SpatialAudioSetSourcePosition(&jdsp->spatialAudio, x, y, z);
}

void SpatialAudioJDSPSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll) {
    if (!jdsp) return;
    SpatialAudioSetHeadOrientation(&jdsp->spatialAudio, yaw, pitch, roll);
}

void SpatialAudioJDSPSetStereoWidth(JamesDSPLib *jdsp, float width) {
    if (!jdsp) return;
    SpatialAudioSetStereoWidth(&jdsp->spatialAudio, width);
}

void SpatialAudioJDSPSetRoomParameters(JamesDSPLib *jdsp, float size, float damping) {
    if (!jdsp) return;
    SpatialAudioSetRoomParameters(&jdsp->spatialAudio, size, damping);
}

void SpatialAudioJDSPSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation) {
    if (!jdsp) return;
    SpatialAudioSetDistanceAttenuation(&jdsp->spatialAudio, attenuation);
}

void SpatialAudioJDSPSetCrossfeedStrength(JamesDSPLib *jdsp, float strength) {
    if (!jdsp) return;
    SpatialAudioSetCrossfeedStrength(&jdsp->spatialAudio, strength);
}
