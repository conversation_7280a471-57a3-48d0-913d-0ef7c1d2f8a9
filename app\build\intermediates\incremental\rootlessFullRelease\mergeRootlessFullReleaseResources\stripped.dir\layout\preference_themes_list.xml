<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@android:id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:paddingTop="4dp"
        android:textAppearance="?attr/textAppearanceListItemSmall"
        tools:text="App theme" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/themes_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:clipToPadding="false"
        tools:listitem="@layout/preference_theme_item" />

</LinearLayout>
