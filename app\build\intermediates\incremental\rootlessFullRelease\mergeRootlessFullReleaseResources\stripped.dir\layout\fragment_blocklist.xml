<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragment.BlocklistFragment"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/notice"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:layout_marginHorizontal="24dp"
        android:background="?android:attr/selectableItemBackground"
        app:cardBackgroundColor="?attr/colorSecondaryContainer">
        <LinearLayout
            android:layout_margin="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="16dp"
                android:layout_gravity="center_vertical"
                android:alpha="0.8"
                app:srcCompat="@drawable/ic_twotone_info_24dp"
                app:tint="?attr/colorOnSecondaryContainer"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/noticeLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceBody2" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        tools:listitem="@layout/item_blocked_app_list" />

    <LinearLayout
        android:id="@+id/emptyview"
        android:visibility="gone"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_marginBottom="?attr/actionBarSize"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginBottom="8dp"
            app:srcCompat="@drawable/ic_twotone_app_blocking_24dp"
            app:tint="@color/material_on_background_disabled"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="20sp"
            android:textColor="@color/material_on_background_disabled"
            android:gravity="center"
            android:text="@string/blocklist_no_exclusions"/>

    </LinearLayout>

</LinearLayout>