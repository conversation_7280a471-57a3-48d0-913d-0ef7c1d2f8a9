<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/start_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_weight="1">
        <ImageView
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_gravity="center_horizontal"

            android:alpha="0.8"
            app:srcCompat="@drawable/ic_twotone_check_circle_24dp"
            app:tint="?attr/colorOnSurface"
            android:importantForAccessibility="no" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="@string/onboarding_finish_header"
            android:textAppearance="?attr/textAppearanceHeadlineLarge" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="@string/onboarding_finish_caption"
            android:textAppearance="?attr/textAppearanceBody2" />
    </LinearLayout>

</LinearLayout>
