<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string-array name="manange_profiles_menu" translatable="false">
        <item>@string/device_profile_manage_copy</item>
        <item>@string/device_profile_manage_delete</item>
    </string-array>

    <string-array name="backup_maximum" translatable="false">
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array>

    <string-array name="backup_frequency" translatable="false">
        <item>@string/backup_never</item>
        <item>@string/backup_12hour</item>
        <item>@string/backup_24hour</item>
        <item>@string/backup_48hour</item>
        <item>@string/backup_weekly</item>
    </string-array>

    <string-array name="backup_frequency_values" translatable="false">
        <item>0</item>
        <item>12</item>
        <item>24</item>
        <item>48</item>
        <item>168</item>
    </string-array>

    <string-array name="update_dismiss_dialog" translatable="false">
        <item>@string/self_update_notice_dismiss_install</item>
        <item>@string/self_update_notice_dismiss_skip</item>
        <item>@string/self_update_notice_dismiss_snooze</item>
    </string-array>

    <string-array name="session_detection_methods" translatable="false">
        <item>@string/session_detection_method_audioservice</item>
        <item>@string/session_detection_method_audiopolicyservice</item>
    </string-array>

    <string-array name="session_detection_methods_values" translatable="false">
        <item>1</item>
        <item>0</item>
    </string-array>

    <string-array name="theme_modes" translatable="false">
        <item>@string/appearance_theme_mode_default</item>
        <item>@string/appearance_theme_mode_light</item>
        <item>@string/appearance_theme_mode_dark</item>
    </string-array>

    <string-array name="theme_modes_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <string-array name="audio_format_encodings" translatable="false">
        <item>@string/audio_format_encoding_int16</item>
        <item>@string/audio_format_encoding_float</item>
    </string-array>

    <string-array name="audio_format_encodings_values" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <string-array name="reverb_presets" translatable="false">
        <item>@string/reverb_preset_default</item>
        <item>@string/reverb_preset_small_hall1</item>
        <item>@string/reverb_preset_small_hall2</item>
        <!--<item>Medium hall 1</item>-->
        <item>@string/reverb_preset_medium_hall2</item>
        <item>@string/reverb_preset_large_hall1</item>
        <!--<item>Large hall 2</item>-->
        <item>@string/reverb_preset_small_room1</item>
        <item>@string/reverb_preset_small_room2</item>
        <item>@string/reverb_preset_medium_room1</item>
        <!--<item>Medium room 2</item>-->
        <item>@string/reverb_preset_large_room1</item>
        <!--<item>Large room 2</item>-->
        <!--<item>Medium ER 1</item>-->
        <!--<item>Medium ER 2</item>-->
        <item>@string/reverb_preset_plate_high</item>
        <item>@string/reverb_preset_plate_low</item>
        <item>@string/reverb_preset_long_reverb1</item>
        <item>@string/reverb_preset_long_reverb2</item>
    </string-array>

    <string-array name="reverb_presets_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <!--<item>3</item>-->
        <item>4</item>
        <item>5</item>
        <!--<item>6</item>-->
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <!--<item>10</item>-->
        <item>11</item>
        <!--<item>12</item>-->
        <!--<item>13</item>-->
        <!--<item>14</item>-->
        <item>15</item>
        <item>16</item>
        <item>17</item>
        <item>18</item>
    </string-array>

    <string-array name="eq_filter_types" translatable="false">
        <item>@string/eq_filter_type_fir_minimum</item>
        <item>@string/eq_filter_type_iir_4_order</item>
        <item>@string/eq_filter_type_iir_6_order</item>
        <item>@string/eq_filter_type_iir_8_order</item>
        <item>@string/eq_filter_type_iir_10_order</item>
        <item>@string/eq_filter_type_iir_12_order</item>
    </string-array>

    <string-array name="eq_filter_types_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>

    <string-array name="eq_interpolators" translatable="false">
        <item>@string/eq_interpolator_chip</item>
        <item>@string/eq_interpolator_mha</item>
    </string-array>

    <string-array name="eq_interpolators_values" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <string-array name="crossfeed_modes" translatable="false">
        <item>@string/crossfeed_preset_bs2b_weak</item>
        <item>@string/crossfeed_preset_bs2b_strong</item>
        <item>@string/crossfeed_preset_out_of_head</item>
        <item>@string/crossfeed_preset_surround1</item>
        <item>@string/crossfeed_preset_surround2</item>
        <item>@string/crossfeed_preset_realistic_surround</item>
    </string-array>

    <string-array name="crossfeed_modes_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>

    <string-array name="compander_tf_transforms" translatable="false">
        <item>@string/compander_tftransforms_stft</item>
        <item>@string/compander_tftransforms_continuous_wavelet</item>
        <item>@string/compander_tftransforms_undersampling</item>
        <item>@string/compander_tftransforms_time_domain</item>
    </string-array>

    <string-array name="compander_tf_transforms_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>

    <string-array name="convolver_convolution_mode" translatable="false">
        <item>@string/convolver_convolution_mode_original</item>
        <item>@string/convolver_convolution_mode_shrink</item>
        <item>@string/convolver_convolution_mode_minimum_phase_shrink</item>
    </string-array>

    <string-array name="convolver_convolution_mode_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <string-array name="equalizer_preset_modes" translatable="false">
        <item>@string/eq_preset_acoustic</item>
        <item>@string/eq_preset_bass</item>
        <item>@string/eq_preset_beats</item>
        <item>@string/eq_preset_classic</item>
        <item>@string/eq_preset_clear</item>
        <item>@string/eq_preset_deep</item>
        <item>@string/eq_preset_dubstep</item>
        <item>@string/eq_preset_electronic</item>
        <item>@string/eq_preset_flat</item>
        <item>@string/eq_preset_hardstyle</item>
        <item>@string/eq_preset_hiphop</item>
        <item>@string/eq_preset_jazz</item>
        <item>@string/eq_preset_metal</item>
        <item>@string/eq_preset_movie</item>
        <item>@string/eq_preset_pop</item>
        <item>@string/eq_preset_rb</item>
        <item>@string/eq_preset_rock</item>
        <item>@string/eq_preset_vocal</item>
    </string-array>

    <string-array name="equalizer_preset_values">
        <item>5.00;4.50;4.00;3.50;1.50;1.00;1.50;1.50;2.00;3.00;3.50;4.00;3.70;3.00;3.00</item>
        <item>10.00;8.80;8.50;6.50;2.50;1.50;0;0;0;0;0;0;0;0;0</item>
        <item>-5.5;-5.0;-4.5;-4.2;-3.5;-3.0;-1.9;0;0;0;0;0;0;0;0</item>
        <item>-0.3;0.3;-3.5;-9.0;-1.0;0.0;1.8;2.1;0.0;0.0;0.0;4.4;9.0;9.0;9.0</item>
        <item>3.5;5.5;6.5;9.5;8.0;6.5;3.5;2.5;1.3;5.0;7.0;9.0;10.0;11.0;9.0</item>
        <item>12.0;8.0;0.0;-6.7;-12.0;-9.0;-3.5;-3.5;-6.1;0.0;-3.0;-5.0;0.0;1.2;3.0</item>
        <item>12.0;10.0;0.5;-1.0;-3.0;-5.0;-5.0;-4.8;-4.5;-2.5;-1.0;0.0;-2.5;-2.5;0.0</item>
        <item>4.0;4.0;3.5;1.0;0.0;-0.5;-2.0;0.0;2.0;0.0;0.0;1.0;3.0;4.0;4.5</item>
        <item>0;0;0;0;0;0;0;0;0;0;0;0;0;0;0</item>
        <item>6.1;7.0;12.0;6.1;-5.0;-12.0;-2.5;3.0;6.5;0.0;-2.2;-4.5;-6.1;-9.2;-10.0</item>
        <item>4.5;4.3;4.0;2.5;1.5;3.0;-1.0;-1.5;-1.5;1.5;0.0;-1.0;0.0;1.5;3.0</item>
        <item>0.0;0.0;0.0;2.0;4.0;5.9;-5.9;-4.5;-2.5;2.5;1.0;-0.8;-0.8;-0.8;-0.8</item>
        <item>10.5;10.5;7.5;0.0;2.0;5.5;0.0;0.0;0.0;6.1;0.0;0.0;6.1;10.0;12.0</item>
        <item>3.0;3.0;6.1;8.5;9.0;7.0;6.1;6.1;5.0;8.0;3.5;3.5;8.0;10.0;8.0</item>
        <item>0.0;0.0;0.0;0.0;0.0;1.3;2.0;2.5;5.0;-1.5;-2.0;-3.0;-3.0;-3.0;-3.0</item>
        <item>3.0;3.0;7.0;6.1;4.5;1.5;-1.5;-2.0;-1.5;2.0;2.5;3.0;3.5;3.8;4.0</item>
        <item>0.0;0.0;0.0;3.0;3.0;-10.0;-4.0;-1.0;0.8;3.0;3.0;3.0;3.0;3.0;3.0</item>
        <item>-1.5;-2.0;-3.0;-3.0;-0.5;1.5;3.5;3.5;3.5;3.0;2.0;1.5;0.0;0.0;-1.5</item>
    </string-array>

    <string-array name="editor_eel_keywords">
        <item>function</item>
        <item>local</item>
        <item>static</item>
        <item>instance</item>
        <item>globals</item>
        <item>global</item>
        <item>this</item>
        <item>loop</item>
        <item>while</item>
        <item>this</item>
        <item>if</item>
        <item>else</item>
        <item>switch</item>
        <item>case</item>
        <item>for</item>
        <item>in</item>
        <item>do</item>
        <item>break</item>
        <item>continue</item>
        <item>true</item>
        <item>false</item>
        <item>not</item>
        <item>and</item>
        <item>or</item>
        <item>xor</item>
    </string-array>

    <string-array name="editor_eel_functions">
        <item>sin</item>
        <item>cos</item>
        <item>tan</item>
        <item>sqrt</item>
        <item>asin</item>
        <item>acos</item>
        <item>atan</item>
        <item>atan2</item>
        <item>sinh</item>
        <item>cosh</item>
        <item>tanh</item>
        <item>asinh</item>
        <item>acosh</item>
        <item>atanh</item>
        <item>sinf</item>
        <item>cosf</item>
        <item>tanf</item>
        <item>asinf</item>
        <item>acosf</item>
        <item>atanf</item>
        <item>asinhf</item>
        <item>acoshf</item>
        <item>coshf</item>
        <item>sinhf</item>
        <item>sqrtf</item>
        <item>tanhf</item>
        <item>atanhf</item>
        <item>logf</item>
        <item>log10f</item>
        <item>expf</item>
        <item>roundf</item>
        <item>floorf</item>
        <item>ceilf</item>
        <item>log</item>
        <item>log10</item>
        <item>hypot</item>
        <item>pow</item>
        <item>exp</item>
        <item>abs</item>
        <item>sqr</item>
        <item>min</item>
        <item>max</item>
        <item>sign</item>
        <item>rand</item>
        <item>round</item>
        <item>floor</item>
        <item>ceil</item>
        <item>expint</item>
        <item>expintFast</item>
        <item>invsqrt</item>
        <item>invsqrtFast</item>
        <item>circshift</item>
        <item>convolve_c</item>
        <item>maxVec</item>
        <item>minVec</item>
        <item>meanVec</item>
        <item>medianVec</item>
        <item>fft</item>
        <item>ifft</item>
        <item>fft_real</item>
        <item>ifft_real</item>
        <item>fft_permute</item>
        <item>fft_ipermute</item>
        <item>memcpy</item>
        <item>memset</item>
        <item>sleep</item>
        <item>time</item>
        <item>time_precise</item>
        <item>strlen</item>
        <item>base64_encode</item>
        <item>base64_encodeF2F</item>
        <item>base64_decode</item>
        <item>base64_decodeF2F</item>
        <item>strcmp</item>
        <item>match</item>
        <item>matchi</item>
        <item>stricmp</item>
        <item>strncmp</item>
        <item>strnicmp</item>
        <item>printf</item>
        <item>sprintf</item>
        <item>resetStringContainers</item>
        <item>importFLTFromStr</item>
        <item>arburgCheckMemoryRequirement</item>
        <item>arburgTrainModel</item>
        <item>arburgPredictBackward</item>
        <item>arburgPredictForward</item>
        <item>stftCheckMemoryRequirement</item>
        <item>stftInit</item>
        <item>stftGetWindowPower</item>
        <item>stftForward</item>
        <item>stftBackward</item>
        <item>InitPinkNoise</item>
        <item>GeneratePinkNoise</item>
        <item>InitPolyphaseFilterbank</item>
        <item>PolyphaseFilterbankChangeWarpingFactor</item>
        <item>PolyphaseFilterbankGetPhaseCorrector</item>
        <item>PolyphaseFilterbankGetDecimationFactor</item>
        <item>PolyphaseFilterbankAnalysisMono</item>
        <item>PolyphaseFilterbankSynthesisMono</item>
        <item>PolyphaseFilterbankAnalysisStereo</item>
        <item>PolyphaseFilterbankSynthesisStereo</item>
        <item>FIRInit</item>
        <item>FIRProcess</item>
        <item>IIRInit</item>
        <item>IIRProcess</item>
        <item>fractionalDelayLineInit</item>
        <item>fractionalDelayLineClear</item>
        <item>fractionalDelayLineSetDelay</item>
        <item>fractionalDelayLineProcess</item>
        <item>linspace</item>
        <item>rank</item>
        <item>det</item>
        <item>transpose</item>
        <item>cholesky</item>
        <item>inv_chol</item>
        <item>inv</item>
        <item>pinv_svd</item>
        <item>pinv_fast</item>
        <item>mldivide</item>
        <item>mrdivide</item>
        <item>quadprog</item>
        <item>lsqlin</item>
        <item>firls</item>
        <item>eqnerror</item>
        <item>unwrap</item>
        <item>zp2sos</item>
        <item>tf2sos</item>
        <item>roots</item>
        <item>cplxpair</item>
        <item>IIRBandSplitterInit</item>
        <item>IIRBandSplitterClearState</item>
        <item>IIRBandSplitterProcess</item>
        <item>Conv1DInit</item>
        <item>Conv1DProcess</item>
        <item>Conv1DFree</item>
        <item>decodeFLACFromFile</item>
        <item>decodeFLACFromMemory</item>
        <item>decodeWavFromFile</item>
        <item>decodeWavFromMemory</item>
        <item>writeWavToFile</item>
        <item>writeWavToBase64String</item>
        <item>peakFinder</item>
        <item>listSystemVariable</item>
        <item>vectorizeAssignScalar</item>
        <item>vectorizeAdd</item>
        <item>vectorizeMinus</item>
        <item>vectorizeMultiply</item>
        <item>vectorizeDivide</item>
        <item>lerpAt</item>
    </string-array>

    <string-array name="editor_eel_constants">
        <item>$pi</item>
        <item>$e</item>
        <item>$phi</item>
        <item>$eps</item>
        <item>$dbl_max</item>
    </string-array>

    <string-array name="editor_predef_vars">
        <item>srate</item>
        <item>spl0</item>
        <item>spl1</item>
    </string-array>

    <!-- Spatial Audio Mode -->
    <string-array name="spatialaudio_mode_entries">
        <item>Disabled</item>
        <item>Stereo Widening</item>
        <item>Binaural</item>
        <item>Surround</item>
        <item>HRTF</item>
    </string-array>

    <string-array name="spatialaudio_mode_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array>

    <!-- Head Tracking Mode -->
    <string-array name="spatialaudio_head_tracking_entries">
        <item>Disabled</item>
        <item>Simulated</item>
        <item>Sensor (Future)</item>
    </string-array>

    <string-array name="spatialaudio_head_tracking_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <!-- Room Type -->
    <string-array name="spatialaudio_room_type_entries">
        <item>None</item>
        <item>Small Room</item>
        <item>Medium Room</item>
        <item>Large Room</item>
        <item>Concert Hall</item>
        <item>Outdoor</item>
    </string-array>

    <string-array name="spatialaudio_room_type_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>
</resources>