<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="start"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:paddingBottom="8dp"
    android:baselineAligned="false"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView android:textAppearance="?android:attr/textAppearanceListItem"
            android:ellipsize="marquee"
            android:id="@android:id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            tools:text="Title" />

        <TextView android:textAppearance="?android:attr/textAppearanceListItemSecondary"
            android:ellipsize="marquee"
            android:gravity="start"
            android:id="@android:id/summary"
            android:scrollbars="none"
            android:fadingEdge="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:singleLine="true"
            tools:text="Value" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@android:id/icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center"
        tools:srcCompat="@drawable/ic_twotone_edit_24dp" />

</LinearLayout>