{"version": 3, "artifactType": {"type": "OPTIMIZED_PROCESSED_RES", "kind": "Directory"}, "applicationId": "me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp", "variantName": "rootlessFullRelease", "elements": [{"type": "UNIVERSAL", "filters": [], "attributes": [], "versionCode": 51, "versionName": "1.6.14", "outputFile": "resources-rootless-full-universal-release-optimize.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "arm64-v8a"}], "attributes": [], "versionCode": 51, "versionName": "1.6.14", "outputFile": "resources-rootless-full-arm64-v8a-release-optimize.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86"}], "attributes": [], "versionCode": 51, "versionName": "1.6.14", "outputFile": "resources-rootless-full-x86-release-optimize.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86_64"}], "attributes": [], "versionCode": 51, "versionName": "1.6.14", "outputFile": "resources-rootless-full-x86_64-release-optimize.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "armeabi-v7a"}], "attributes": [], "versionCode": 51, "versionName": "1.6.14", "outputFile": "resources-rootless-full-armeabi-v7a-release-optimize.ap_"}], "elementType": "File"}