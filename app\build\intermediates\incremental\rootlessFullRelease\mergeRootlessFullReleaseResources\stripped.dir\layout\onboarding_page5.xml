<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:alpha="0.8"
            app:srcCompat="@drawable/ic_twotone_security_24dp"
            app:tint="?attr/colorOnSurface"
            android:importantForAccessibility="no" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:text="@string/onboarding_perm_title"
            android:textAppearance="?attr/textAppearanceHeadline4" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/onboarding_perm_caption"
            android:textAppearance="?attr/textAppearanceBody1" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/privacy_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:titleText="@string/onboarding_perm_diag"
                app:bodyText="@string/onboarding_perm_diag_caption"
                app:iconCentered="true"
                app:iconSrc="@drawable/ic_twotone_privacy_tip_24dp"
                app:iconTint="?attr/colorOnSurface"
                app:checkboxVisible="true"
                app:cardMargin="8dp" />


            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:titleText="@string/onboarding_perm_microphone_title"
                app:bodyText="@string/onboarding_perm_microphone_caption"
                app:iconCentered="true"
                app:iconSrc="@drawable/ic_twotone_mic_24dp"
                app:iconTint="?attr/colorOnSurface"
                app:cardMargin="8dp" />


            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/onboarding_notification_permission"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:titleText="@string/onboarding_perm_notification_title"
                app:bodyText="@string/onboarding_perm_notification_caption"
                app:iconCentered="true"
                app:iconSrc="@drawable/ic_twotone_notifications_24dp"
                app:iconTint="?attr/colorOnSurface"
                app:cardMargin="8dp" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:titleText="@string/onboarding_perm_cast_title"
                app:bodyText="@string/onboarding_perm_cast_caption"
                app:iconCentered="true"
                app:iconSrc="@drawable/ic_twotone_cast_24dp"
                app:iconTint="?attr/colorOnSurface"
                app:cardMargin="8dp" />

        </LinearLayout>
    </ScrollView>

</LinearLayout>
