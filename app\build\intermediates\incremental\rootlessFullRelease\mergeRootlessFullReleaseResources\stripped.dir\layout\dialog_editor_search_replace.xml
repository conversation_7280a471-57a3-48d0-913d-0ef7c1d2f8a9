<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="10dp">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">
        <com.google.android.material.textfield.TextInputLayout
            style="?attr/textInputOutlinedDenseStyle"
            android:id="@+id/search_edit_frame"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center"
            android:hint="@string/editor_search_keyword"
            app:placeholderText="@string/editor_search_keyword">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/search_edit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/find_prev_action"
            style="?attr/materialIconButtonStyle"
            android:layout_width="48dp"
            android:layout_height="wrap_content"
            android:contentDescription="@string/editor_find_previous_match"
            android:tooltipText="@string/editor_find_previous_match"
            app:icon="@drawable/ic_baseline_keyboard_arrow_up_24dp" />

        <Button
            style="?attr/materialIconButtonStyle"
            android:id="@+id/find_next_action"
            android:layout_width="48dp"
            android:layout_height="wrap_content"
            android:contentDescription="@string/editor_find_next_match"
            android:tooltipText="@string/editor_find_next_match"
            app:icon="@drawable/ic_baseline_keyboard_arrow_down_24dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">
        <com.google.android.material.textfield.TextInputLayout
            style="?attr/textInputOutlinedDenseStyle"
            android:id="@+id/replacement_edit_frame"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:layout_gravity="center"
            android:hint="@string/editor_replacement"
            app:placeholderText="@string/editor_replacement">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/replacement_edit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            style="?attr/materialIconButtonStyle"
            android:id="@+id/replace_action"
            android:layout_width="48dp"
            android:layout_height="wrap_content"
            android:contentDescription="@string/editor_replace_all"
            android:tooltipText="@string/editor_replace_all"
            app:icon="@drawable/ic_baseline_find_replace_24dp" />
    </LinearLayout>
</LinearLayout>