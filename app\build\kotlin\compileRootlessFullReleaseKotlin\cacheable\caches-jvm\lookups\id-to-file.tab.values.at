/ Header Record For PersistentHashMapValueStorageP Oapp/src/full/java/me/timschneeberger/rootlessjamesdsp/flavor/CrashlyticsImpl.ktI Happ/src/main/java/me/timschneeberger/rootlessjamesdsp/MainApplication.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/AeqSelectorActivity.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/AppCompatibilityActivity.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/BaseActivity.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/BlocklistActivity.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/EngineLauncherActivity.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/GraphicEqualizerActivity.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/LiveprogEditorActivity.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/LiveprogParamsActivity.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/MainActivity.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/OnboardingActivity.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/activity/SettingsActivity.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/AppBlocklistAdapter.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/AppsListAdapter.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/AutoEqResultAdapter.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/CustomCodeViewAdapter.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/GraphicEqNodeAdapter.kte dapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/RoundedRipplePreferenceGroupAdapter.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/adapter/ThemesPreferenceAdapter.ktJ Iapp/src/main/java/me/timschneeberger/rootlessjamesdsp/api/AutoEqClient.ktK Japp/src/main/java/me/timschneeberger/rootlessjamesdsp/api/AutoEqService.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/api/UserAgentInterceptor.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/backup/BackupCreatorJob.ktN Mapp/src/main/java/me/timschneeberger/rootlessjamesdsp/backup/BackupManager.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/backup/BackupNotifier.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/backup/BackupRestoreService.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/contract/AutoEqSelectorContract.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/delegates/ThemingDelegate.kt^ ]app/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/plugin/SourcePositionListener.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/plugin/UndoRedoManager.ktP Oapp/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/syntax/Constant.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/syntax/EelLanguage.ktP Oapp/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/syntax/Function.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/editor/widget/SymbolInputView.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/AppCompatibilityFragment.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/AppsListFragment.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/BlocklistFragment.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/CompanderDialogFragment.kt] \app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/DeviceProfilesCardFragment.ktN Mapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/DspFragment.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/EqualizerDialogFragment.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/FileLibraryDialogFragment.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/GraphicEqualizerFragment.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/LibraryLoadErrorFragment.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/LimitationsFragment.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/LiveprogParamsFragment.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/OnboardingFragment.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/PreferenceGroupFragment.kta `app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsAboutFragment.ktf eapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsAppearanceFragment.ktg fapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsAudioFormatFragment.ktb aapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsBackupFragment.kt` _app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsBaseFragment.ktj iapp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsDeviceProfilesFragment.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsFragment.kt` _app/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsMiscFragment.ktk japp/src/main/java/me/timschneeberger/rootlessjamesdsp/fragment/settings/SettingsTroubleshootingFragment.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/BenchmarkManager.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/JamesDspBaseEngine.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/JamesDspLocalEngine.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/JamesDspRemoteEngine.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/JamesDspWrapper.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/JdspImpResToolbox.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/PreferenceCache.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/ProcessorMessageHandler.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/interop/structure/EelVmVariable.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/EelBaseProperty.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/EelListProperty.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/EelNumberRangeProperty.ktL Kapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/EelParser.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/EelPropertyFactory.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/liveprog/IPropertyCompanion.ktG Fapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/AppInfo.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/AudioSessionDumpEntry.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/GraphicEqNode.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/GraphicEqNodeList.ktN Mapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/IEffectSession.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/ItemViewModel.ktP Oapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/ProcessorMessage.ktJ Iapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/Translator.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/api/AeqSearchResult.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/preference/AppTheme.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/preference/AudioEncoding.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/model/preference/SessionUpdateMode.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/preference/ThemeMode.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/preset/Preset.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/room/AppBlocklistDao.ktY Xapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/room/AppBlocklistDatabase.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/room/AppBlocklistRepository.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/room/AppBlocklistViewModel.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/room/BlockedApp.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/root/RemoteEffectSession.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/rootless/MutedEffectSession.ktd capp/src/main/java/me/timschneeberger/rootlessjamesdsp/model/rootless/SessionRecordingPolicyEntry.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/AppIconPreference.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/CompanderPreference.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/DropDownPreference.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/EqualizerPreference.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/FileLibraryPreference.kt_ ^app/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/GraphicEqualizerPreference.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/IconPreference.kt^ ]app/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/MaterialSeekbarPreference.kt] \app/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/MaterialSwitchPreference.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/SwitchPreferenceGroup.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/preference/ThemesPreference.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/receiver/BootCompletedReceiver.ktU Tapp/src/main/java/me/timschneeberger/rootlessjamesdsp/receiver/PowerStateReceiver.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/receiver/SessionReceiver.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/service/BaseAudioProcessorService.kt] \app/src/main/java/me/timschneeberger/rootlessjamesdsp/service/NotificationListenerService.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/service/QuickTileService.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/service/RootAudioProcessorService.kt_ ^app/src/main/java/me/timschneeberger/rootlessjamesdsp/service/RootlessAudioProcessorService.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/DebugDumpFileProvider.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/DumpManager.ktb aapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/AudioPolicyServiceDump.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/AudioServiceDump.ktQ Papp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/IDump.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/ISessionInfoDump.ktb aapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/ISessionPolicyInfoDump.kt^ ]app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/data/PackageServiceDump.kto napp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/AudioFlingerServiceDumpProvider.ktn mapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/AudioPolicyServiceDumpProvider.kth gapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/AudioServiceDumpProvider.kt] \app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/IDumpProvider.ktd capp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/ISessionDumpProvider.ktj iapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/ISessionPolicyDumpProvider.ktj iapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/provider/PackageServiceDumpProvider.kti happ/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/utils/AudioFlingerServiceDumpUtils.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/dump/utils/DumpUtils.ktZ Yapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/root/RootSessionDatabase.kt] \app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/root/RootSessionDumpManager.ktb aapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/rootless/RootlessSessionDatabase.kta `app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/rootless/RootlessSessionManager.kth gapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/rootless/SessionRecordingPolicyManager.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/session/shared/BaseSessionDatabase.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/session/shared/BaseSessionManager.ktI Happ/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/Constants.ktK Japp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/EngineUtils.ktR Qapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/FlavorConditionals.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/MutedAudioEffectFactory.ktN Mapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/ProfileManager.ktF Eapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/Result.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/RoutingObserver.ktH Gapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/SdkCheck.ktX Wapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/ApiExtensions.kta `app/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/AssetManagerExtensions.kt` _app/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/AudioEffectExtensions.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/CompatExtensions.kt\ [app/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/ContextExtensions.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/MiscUtils.kta `app/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/NotificationExtensions.kt_ ^app/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/extensions/PermissionExtensions.kt[ Zapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/notifications/Notifications.ktg fapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/notifications/ServiceNotificationHelper.ktb aapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/preferences/NonPersistentDatastore.ktW Vapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/preferences/Preferences.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/storage/Cache.ktT Sapp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/storage/StorageUtils.ktK Japp/src/main/java/me/timschneeberger/rootlessjamesdsp/utils/storage/Tar.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/BaseEqualizerSurface.ktC Bapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/Card.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/CompanderSurface.ktO Napp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/EqualizerSurface.ktS Rapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/FloatingToggleButton.ktV Uapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/GraphicEqualizerSurface.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/NumberInputBox.ktM Lapp/src/main/java/me/timschneeberger/rootlessjamesdsp/view/ProgressDialog.ktR Qapp/src/rootless/java/me/timschneeberger/rootlessjamesdsp/flavor/RootShellImpl.ktR Qapp/src/rootless/java/me/timschneeberger/rootlessjamesdsp/flavor/UpdateManager.kt