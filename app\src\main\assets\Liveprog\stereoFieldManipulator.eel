desc:Stereo Field Manipulator
//tags: processing stereo
//author: LOSER

slider1:0<-90,90,1>Rotate (°)
slider2:100<0,200,1>Width (%)
slider3:0<-100,100,1>Center (%)
slider4:0<-100,100,1>Left/Right (%)

@init
slider1=0;
slider2=100;
slider3=0;
slider4=0;

rot=slider1*0.017453292;
width = slider2 / 200;
center = min(slider3/100+1,1);
side = (1-slider3/100);
left = -min(slider4/100,0);
left1 = -max(slider4/100-1,-1);
right = max(slider4/100,0);
right1 = min(1+slider4/100,1);

update = 0;

@sample

// Rotation
s0 = sign(spl0);
s1 = sign(spl1);
angle = atan( spl0 / spl1 );
(s0 == 1 && s1 == -1) || (s0 == -1 && s1 == -1) ? angle += 3.141592654;
s0 == -1 && s1 == 1 ? angle += 6.283185307;
spl1 == 0 ? spl0 > 0 ? angle = 1.570796327 : angle = 4.71238898;
spl0 == 0 ? spl1 > 0 ? angle = 0 : angle = 3.141592654;
angle -= rot;
radius = sqrt( sqr(spl0)+sqr(spl1) ) ;
spl0 = sin(angle)*radius;
spl1 = cos(angle)*radius;

// 3 Way Balancer + Enhancer
mono = (spl0 + spl1)/2 * center;
stereo = (spl0 - spl1) * side;
spl0 = (mono + (stereo*left1 - stereo*right )* width) / max(width,1);
spl1 = (mono + (-stereo*right1 + stereo*left )* width) / max(width,1);
