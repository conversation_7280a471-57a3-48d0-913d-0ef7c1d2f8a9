R_DEF: Internal format may change without notice
local
array audio_format_encodings
array audio_format_encodings_values
array backup_frequency
array backup_frequency_values
array backup_maximum
array com.google.firebase.crashlytics.build_ids_arch
array com.google.firebase.crashlytics.build_ids_build_id
array com.google.firebase.crashlytics.build_ids_lib
array compander_tf_transforms
array compander_tf_transforms_values
array convolver_convolution_mode
array convolver_convolution_mode_values
array crossfeed_modes
array crossfeed_modes_values
array editor_eel_constants
array editor_eel_functions
array editor_eel_keywords
array editor_predef_vars
array eq_filter_types
array eq_filter_types_values
array eq_interpolators
array eq_interpolators_values
array equalizer_preset_modes
array equalizer_preset_values
array manange_profiles_menu
array reverb_presets
array reverb_presets_values
array session_detection_methods
array session_detection_methods_values
array theme_modes
array theme_modes_values
array update_dismiss_dialog
attr? bodyText
attr? buttonEnabled
attr? buttonText
attr? cardBackground
attr? cardMargin
attr? checkboxVisible
attr? closeButtonVisible
attr? floatPrecision
attr? helperText
attr? helperTextEnabled
attr? hintText
attr? iconCentered
attr? iconSrc
attr iconTint
attr? isStatic
attr? labelMinWidth
attr? maxValue
attr? minValue
attr? precision
attr? seekBarIncrement
attr? seekBarStyle
attr? showSeekBarValue
attr? step
attr? suffixText
attr? titleText
attr? type
attr? unit
attr? updatesContinuously
attr? value
bool default_android15_screenrecord_restriction_seen
bool default_appearance_nav_hide
bool default_appearance_pure_black
bool default_appearance_show_icons
bool default_audioformat_enhanced_processing
bool default_audioformat_optimization_benchmark
bool default_audioformat_processing
bool default_autostart_prompt_at_boot
bool default_device_profiles_enable
bool default_exclude_app_from_recents
bool default_first_boot
bool default_is_activity_active
bool default_is_app_compat_activity_active
bool default_powered_on
bool default_powersave_suspend
bool default_reset_proc_mode_fix_applied
bool default_session_app_problem_ignore
bool default_session_continuous_polling
bool default_session_exclude_restricted
bool default_session_loss_ignore
bool default_share_crash_reports
color background_amoled
color black
color divider_default
color gold
color greenapple_background
color greenapple_inverseOnSurface
color greenapple_inverseSurface
color greenapple_onBackground
color greenapple_onPrimary
color greenapple_onPrimaryContainer
color greenapple_onSecondary
color greenapple_onSecondaryContainer
color greenapple_onSurface
color greenapple_onSurfaceVariant
color greenapple_onTertiary
color greenapple_onTertiaryContainer
color greenapple_outline
color greenapple_primary
color greenapple_primaryContainer
color greenapple_primaryInverse
color greenapple_secondary
color greenapple_secondaryContainer
color greenapple_surface
color greenapple_surfaceVariant
color greenapple_tertiary
color greenapple_tertiaryContainer
color honey_background
color honey_elevationOverlay
color honey_error
color honey_errorContainer
color honey_inverseOnSurface
color honey_inverseSurface
color honey_onBackground
color honey_onError
color honey_onErrorContainer
color honey_onPrimary
color honey_onPrimaryContainer
color honey_onSecondary
color honey_onSecondaryContainer
color honey_onSurface
color honey_onSurfaceVariant
color honey_onTertiary
color honey_onTertiaryContainer
color honey_outline
color honey_primary
color honey_primaryContainer
color honey_primaryInverse
color honey_secondary
color honey_secondaryContainer
color honey_shadow
color honey_surface
color honey_surfaceTint
color honey_surfaceTintColor
color honey_surfaceVariant
color honey_tertiary
color honey_tertiaryContainer
color md_black_1000
color md_black_1000_12
color md_black_1000_38
color md_black_1000_54
color md_black_1000_6
color md_black_1000_8
color md_black_1000_87
color md_blue_A200
color md_blue_A200_50
color md_blue_A200_75
color md_blue_A400
color md_blue_A400_12
color md_blue_A400_38
color md_blue_A400_75
color md_blue_grey_800
color md_blue_grey_900
color md_grey_100
color md_grey_300
color md_grey_50
color md_grey_50_75
color md_grey_800
color md_grey_900
color md_grey_900_75
color md_white_1000
color md_white_1000_12
color md_white_1000_20
color md_white_1000_50
color md_white_1000_54
color md_white_1000_6
color md_white_1000_70
color md_white_1000_8
color monokia_pro_black
color monokia_pro_error
color monokia_pro_gray
color monokia_pro_green
color monokia_pro_grey
color monokia_pro_orange
color monokia_pro_pink
color monokia_pro_purple
color monokia_pro_sky
color monokia_pro_sky_dim
color monokia_pro_white
color monokia_pro_white_dim
color selector_floating_toggle_foreground_tint
color selector_floating_toggle_tint
color strawberry_background
color strawberry_inverseOnSurface
color strawberry_inverseSurface
color strawberry_onBackground
color strawberry_onPrimary
color strawberry_onPrimaryContainer
color strawberry_onSecondary
color strawberry_onSecondaryContainer
color strawberry_onSurface
color strawberry_onSurfaceVariant
color strawberry_onTertiary
color strawberry_onTertiaryContainer
color strawberry_outline
color strawberry_primary
color strawberry_primaryContainer
color strawberry_primaryInverse
color strawberry_secondary
color strawberry_secondaryContainer
color strawberry_surface
color strawberry_surfaceVariant
color strawberry_tertiary
color strawberry_tertiaryContainer
color surface_amoled
color tealturquoise_background
color tealturquoise_elevationOverlay
color tealturquoise_inverseOnSurface
color tealturquoise_inverseSurface
color tealturquoise_onBackground
color tealturquoise_onPrimary
color tealturquoise_onPrimaryContainer
color tealturquoise_onSecondary
color tealturquoise_onSecondaryContainer
color tealturquoise_onSurface
color tealturquoise_onSurfaceVariant
color tealturquoise_onTertiary
color tealturquoise_onTertiaryContainer
color tealturquoise_outline
color tealturquoise_primary
color tealturquoise_primaryContainer
color tealturquoise_primaryInverse
color tealturquoise_secondary
color tealturquoise_secondaryContainer
color tealturquoise_surface
color tealturquoise_surfaceVariant
color tealturquoise_tertiary
color tealturquoise_tertiaryContainer
color tidalwave_background
color tidalwave_inverseOnSurface
color tidalwave_inverseSurface
color tidalwave_onBackground
color tidalwave_onPrimary
color tidalwave_onPrimaryContainer
color tidalwave_onSecondary
color tidalwave_onSecondaryContainer
color tidalwave_onSurface
color tidalwave_onSurfaceVariant
color tidalwave_onTertiary
color tidalwave_onTertiaryContainer
color tidalwave_outline
color tidalwave_primary
color tidalwave_primaryContainer
color tidalwave_primaryInverse
color tidalwave_secondary
color tidalwave_secondaryContainer
color tidalwave_surface
color tidalwave_surfaceVariant
color tidalwave_tertiary
color tidalwave_tertiaryContainer
color yinyang_background
color yinyang_inverseOnSurface
color yinyang_inverseSurface
color yinyang_onBackground
color yinyang_onPrimary
color yinyang_onPrimaryContainer
color yinyang_onSecondary
color yinyang_onSecondaryContainer
color yinyang_onSurface
color yinyang_onSurfaceVariant
color yinyang_onTertiary
color yinyang_onTertiaryContainer
color yinyang_outline
color yinyang_primary
color yinyang_primaryContainer
color yinyang_primaryInverse
color yinyang_secondary
color yinyang_secondaryContainer
color yinyang_surface
color yinyang_surfaceVariant
color yinyang_tertiary
color yinyang_tertiaryContainer
color yotsuba_background
color yotsuba_error
color yotsuba_errorContainer
color yotsuba_inverseOnSurface
color yotsuba_inverseSurface
color yotsuba_onBackground
color yotsuba_onError
color yotsuba_onErrorContainer
color yotsuba_onPrimary
color yotsuba_onPrimaryContainer
color yotsuba_onSecondary
color yotsuba_onSecondaryContainer
color yotsuba_onSurface
color yotsuba_onSurfaceVariant
color yotsuba_onTertiary
color yotsuba_onTertiaryContainer
color yotsuba_outline
color yotsuba_primary
color yotsuba_primaryContainer
color yotsuba_primaryInverse
color yotsuba_secondary
color yotsuba_secondaryContainer
color yotsuba_shadow
color yotsuba_surface
color yotsuba_surfaceTint
color yotsuba_surfaceTintColor
color yotsuba_surfaceVariant
color yotsuba_tertiary
color yotsuba_tertiaryContainer
dimen corner_radius
drawable ic_baseline_add_24dp
drawable ic_baseline_arrow_collapse_24dp
drawable ic_baseline_chart_timeline_variant_24dp
drawable ic_baseline_code_tags_24dp
drawable ic_baseline_cone_24dp
drawable ic_baseline_cube_outline_24dp
drawable ic_baseline_download_24dp
drawable ic_baseline_equalizer_24dp
drawable ic_baseline_find_replace_24dp
drawable ic_baseline_format_size_24dp
drawable ic_baseline_github_24dp
drawable ic_baseline_google_play_24dp
drawable ic_baseline_image_filter_tilt_shift_24dp
drawable ic_baseline_keyboard_arrow_down_24dp
drawable ic_baseline_keyboard_arrow_up_24dp
drawable ic_baseline_led_outline_24dp
drawable ic_baseline_play_arrow_24dp
drawable ic_baseline_redo_24dp
drawable ic_baseline_refresh_24dp
drawable ic_baseline_sine_wave_24dp
drawable ic_baseline_undo_24dp
drawable ic_baseline_warning_24dp
drawable ic_baseline_waveform_24dp
drawable ic_close_24dp
drawable ic_code_json
drawable ic_function_variant
drawable ic_key
drawable ic_numeric_1_circle_outline
drawable ic_numeric_2_circle_outline
drawable ic_numeric_3_circle_outline
drawable ic_numeric_4_circle_outline
drawable ic_numeric_5_circle_outline
drawable ic_numeric_6_circle_outline
drawable ic_search_24dp
drawable ic_tune_vertical_variant_24dp
drawable ic_twotone_add_24dp
drawable ic_twotone_app_blocking_24dp
drawable ic_twotone_auto_fix_high_24
drawable ic_twotone_bluetooth_24dp
drawable ic_twotone_cable_24dp
drawable ic_twotone_cast_24dp
drawable ic_twotone_check_24dp
drawable ic_twotone_check_circle_24dp
drawable ic_twotone_chevron_right_24dp
drawable ic_twotone_color_lens_24dp
drawable ic_twotone_delete_24dp
drawable ic_twotone_delete_forever_24dp
drawable ic_twotone_device_unknown_24dp
drawable ic_twotone_download_24
drawable ic_twotone_edit_24dp
drawable ic_twotone_error_24dp
drawable ic_twotone_find_in_page_24dp
drawable ic_twotone_folder_special_24dp
drawable ic_twotone_headphones_24dp
drawable ic_twotone_help_24dp
drawable ic_twotone_history_24dp
drawable ic_twotone_info_24dp
drawable ic_twotone_launch_24dp
drawable ic_twotone_mic_24dp
drawable ic_twotone_more_horiz_24
drawable ic_twotone_nodes_24dp
drawable ic_twotone_notifications_24dp
drawable ic_twotone_power_settings_24dp
drawable ic_twotone_privacy_tip_24dp
drawable ic_twotone_remove_24dp
drawable ic_twotone_save_24dp
drawable ic_twotone_search_24dp
drawable ic_twotone_security_24dp
drawable ic_twotone_settings_24dp
drawable ic_twotone_settings_backup_restore_24dp
drawable ic_twotone_settings_input_hdmi_24dp
drawable ic_twotone_speaker_24dp
drawable ic_twotone_translate_24dp
drawable ic_twotone_troubleshoot_24dp
drawable ic_twotone_usb_24dp
drawable ic_twotone_volume_up_24dp
drawable ic_twotone_warning_24dp
drawable ic_welcome_24dp
drawable ripple_rounded
drawable shape_card_divider
drawable shape_rounded_corners
drawable shape_rounded_highlight
drawable shape_rounded_rectangle
font jetbrainsmono
id action_blocklist
id action_presets
id action_reset
id action_revert
id action_settings
id adb_setup
id add
id alertTitle
id appBarLayout
id app_card
id app_name
id architectureNotice
id autoeq
id back_button
id badges
id bar
id blocklist_host
id bottom_nav
id bottom_nav_selected_item
id bottom_nav_unselected_item
id button
id cancel
id card_bass
id card_compressor
id card_container
id card_convolver
id card_crossfeed
id card_ddc
id card_device_profiles
id card_eq
id card_geq
id card_liveprog
id card_output_control
id card_reverb
id card_stereowide
id card_tube
id cards
id center_guideline
id checkbox
id close
id closeButtonLayout
id codeView
id codeViewHorizScroller
id codeViewScroller
id code_container
id code_frame
id code_title
id code_type
id compander_surface
id confirm
id contentPanel
id controls_container
id controls_layout
id cover_container
id delete
id delete_selection
id divider
id docs
id drag_handle
id dspScreen
id dsp_fragment_container
id dsp_scrollview
id duplicate_selection
id edit_card
id edit_card_title
id edit_selection
id edit_string
id empty_view
id empty_view_text
id emptyview
id equalizer_presets
id equalizer_surface
id exclude
id fab
id file_name_text
id filter
id findMenu
id find_next_action
id find_prev_action
id fontSize
id fragment
id freq
id freqInput
id gain
id gainInput
id header
id hint
id horizontalScrollView
id icon
id icon_frame
id input
id inputLayout
id launch_app
id layout_equalizer
id left_menu
id loader
id method_select
id methods_adb_body
id methods_adb_card
id methods_root_body
id methods_root_card
id methods_root_title
id methods_shizuku_body
id methods_shizuku_card
id methods_shizuku_title
id minus
id name
id nav_main
id nav_onboarding
id next_button
id node_count
id node_detail_context_buttons
id node_edit
id node_list
id notice
id noticeLabel
id onboardingFragment
id onboarding_container
id onboarding_fragment_container
id onboarding_notification_permission
id onboarding_page1
id onboarding_page2
id onboarding_page3
id onboarding_page4
id onboarding_page5
id onboarding_page6
id onboarding_page7
id other_perms
id overwrite_selection
id package_name
id params
id parentPanel
id partial_results_card
id plus
id power_toggle
id preference_appicon
id preset_import
id preset_new
id preview_card
id preview_title
id privacy_card
id profile_list
id profile_list_container
id progress
id progress_number
id progress_percent
id recyclerview
id rename_selection
id replace_action
id replacement_edit
id replacement_edit_frame
id resample_selection
id reset
id retry
id root
id rootlessNotice
id scrollIndicatorDown
id scrollView
id search_edit
id search_edit_frame
id search_view
id seekbar
id seekbar_value
id settings
id settingsToolbar
id share_selection
id source_info_layout
id source_position_txt
id start_root
id step1
id step2
id step3
id step4
id step5b
id step5c_optional
id step6
id subtitle
id switchWidget
id symbolInput
id tags
id text
id textSpacerNoButtons
id textSpacerNoTitle
id text_input_layout
id text_redo
id text_run
id text_save
id text_undo
id theme_card
id themes_list
id title
id toolbar
id toolbar_frame
id top_nav
id top_nav_text
id translation_notice
id update_notice
integer default_audioformat_buffersize
integer default_editor_font_size
integer default_snooze_translation_notice
integer default_update_check_skip
integer default_update_check_timeout
layout activity_aeq_selector
layout activity_app_compatibility
layout activity_blocklist
layout activity_dsp_main
layout activity_graphic_eq
layout activity_liveprog_editor
layout activity_liveprog_params
layout activity_onboarding
layout activity_settings
layout content_main
layout dialog_editor_search_replace
layout dialog_filelibrary
layout dialog_progress
layout dialog_textinput
layout fragment_app_compatibility
layout fragment_applist_sheet
layout fragment_blocklist
layout fragment_dsp
layout fragment_graphic_eq
layout fragment_library_load_error
layout item_app_list
layout item_autoeq_profile_list
layout item_blocked_app_list
layout item_editor_autocomplete
layout item_geq_node_list
layout item_preset_list
layout onboarding_controls
layout onboarding_fragment
layout onboarding_page1
layout onboarding_page2
layout onboarding_page3
layout onboarding_page4
layout onboarding_page5
layout onboarding_page6
layout preference_alt
layout preference_appicon
layout preference_compander
layout preference_compander_dialog
layout preference_equalizer
layout preference_equalizer_dialog
layout preference_graphic_equalizer
layout preference_icon
layout preference_materialslider
layout preference_materialswitch
layout preference_switchgroup
layout preference_theme_item
layout preference_themes_list
layout view_card
layout view_number_input_box
menu menu_filelibrary_add_context
menu menu_filelibrary_context
menu menu_liveprog_editor
menu menu_liveprog_params
menu menu_main_bottom
menu menu_main_bottom_left
mipmap ic_dsp_launcher
mipmap ic_dsp_launcher_background
mipmap ic_dsp_launcher_foreground
mipmap ic_dsp_launcher_monochrome
navigation nav_main
navigation nav_onboarding
plurals custom_parameters
plurals nodes
plurals unsupported_apps
raw translators
string about_settings
string about_settings_summary
string action_blocked_apps
string action_fix
string action_import
string action_presets
string action_restore_defaults
string action_retry
string action_revert
string action_settings
string action_stop
string actions
string add
string android_15_screenshare_keyguard_warning
string android_15_screenshare_warning
string android_15_screenshare_warning_title
string app_behavior
string app_compat_exclude
string app_compat_explanation
string app_compat_instruction
string app_compat_instruction_exclude
string app_compat_retry
string app_compat_title
string app_compat_unknown_pkg_name
string app_list_icon_alt
string app_list_loading
string app_list_search
string appearance_app_theme
string appearance_nav_hide
string appearance_navigation_title
string appearance_pure_black_mode
string appearance_section_header
string appearance_show_icons
string appearance_summary
string appearance_theme_mode
string appearance_theme_mode_dark
string appearance_theme_mode_default
string appearance_theme_mode_light
string appearance_title
string assets
string audio_format_buffer_size
string audio_format_buffer_size_unit
string audio_format_buffer_size_warning_low_value
string audio_format_encoding
string audio_format_encoding_float
string audio_format_encoding_int16
string audio_format_enhanced_processing
string audio_format_enhanced_processing_info
string audio_format_enhanced_processing_info_content
string audio_format_enhanced_processing_info_title
string audio_format_enhanced_processing_off
string audio_format_enhanced_processing_on
string audio_format_header
string audio_format_media_apps_need_restart
string audio_format_optimization_benchmark
string audio_format_optimization_benchmark_ongoing
string audio_format_optimization_benchmark_summary
string audio_format_optimization_header
string audio_format_optimization_refresh
string audio_format_processing_header
string audio_format_processing_legacy
string audio_format_processing_legacy_off
string audio_format_processing_legacy_on
string audio_format_section_header
string audio_format_summary
string audio_format_summary_plugin
string audio_format_summary_root
string autoeq_enter_model
string autoeq_no_results
string autoeq_partial_results_warning
string autoeq_search
string autostart_prompt_at_boot
string autostart_prompt_at_boot_off
string autostart_prompt_at_boot_on
string autostart_service_at_boot
string autostart_service_at_boot_off
string autostart_service_at_boot_on
string backup_12hour
string backup_24hour
string backup_48hour
string backup_automatic_backup
string backup_compat_info
string backup_create
string backup_create_completed
string backup_create_error
string backup_create_progress
string backup_create_summary
string backup_frequency
string backup_in_progress
string backup_location
string backup_manual_backup
string backup_maximum
string backup_never
string backup_restore
string backup_restore_completed
string backup_restore_error
string backup_restore_error_format
string backup_restore_error_version_too_new
string backup_restore_mode_clean
string backup_restore_mode_dirty
string backup_restore_mode_title
string backup_restore_progress
string backup_restore_summary
string backup_select_location
string backup_settings
string backup_settings_summary
string backup_weekly
string bass_enable
string bass_max_gain
string blocklist_add_exclusion_alt
string blocklist_delete
string blocklist_delete_title
string blocklist_no_exclusions
string blocklist_unsupported_apps
string blocklist_unsupported_apps_message
string capture_permission_revoked_toast
string close
string com.google.firebase.crashlytics.mapping_file_id
string compander_enable
string compander_granularity
string compander_granularity_extreme
string compander_granularity_high
string compander_granularity_low
string compander_granularity_medium
string compander_granularity_very_low
string compander_tftransforms
string compander_tftransforms_continuous_wavelet
string compander_tftransforms_stft
string compander_tftransforms_time_domain
string compander_tftransforms_undersampling
string compander_timeconstant
string continue_action
string convolver_advanced_editing
string convolver_convolution_mode
string convolver_convolution_mode_minimum_phase_shrink
string convolver_convolution_mode_original
string convolver_convolution_mode_shrink
string convolver_enable
string convolver_impulse
string copy
string credits
string credits_app
string credits_build_info
string credits_dsp
string credits_project_check_update
string credits_project_check_update_summary
string credits_project_page
string credits_project_page_summary
string credits_project_play_page
string credits_project_play_page_summary
string credits_project_translate
string credits_project_translate_summary
string credits_version
string crossfeed_enable
string crossfeed_preset
string crossfeed_preset_bs2b_strong
string crossfeed_preset_bs2b_weak
string crossfeed_preset_out_of_head
string crossfeed_preset_realistic_surround
string crossfeed_preset_surround1
string crossfeed_preset_surround2
string ddc_enable
string ddc_file
string default_appearance_app_theme
string default_appearance_theme_mode
string default_audioformat_encoding
string default_backup_frequency
string default_backup_location
string default_backup_maximum
string default_benchmark_c0
string default_benchmark_c1
string default_network_autoeq_api_url
string default_session_continuous_polling_rate
string default_session_detection_method
string default_web_client_id
string delete
string details
string device_profile_manage_copy
string device_profile_manage_copy_select
string device_profile_manage_copy_select_no_target
string device_profile_manage_delete
string device_profile_manage_paste_select
string device_profile_status
string editor_cannot_redo
string editor_cannot_undo
string editor_docs
string editor_engine_down
string editor_engine_down_title
string editor_find_and_replace
string editor_find_next_match
string editor_find_previous_match
string editor_liveprog_enabled
string editor_open_fail
string editor_replace_all
string editor_replacement
string editor_run
string editor_save
string editor_save_prompt
string editor_save_prompt_title
string editor_script_launched
string editor_search_keyword
string editor_source_position
string editor_text_size
string enhanced_processing_feature_unavailable
string enhanced_processing_feature_unavailable_content
string enhanced_processing_missing_perm
string eq_enable
string eq_filter_type
string eq_filter_type_fir_minimum
string eq_filter_type_iir_10_order
string eq_filter_type_iir_12_order
string eq_filter_type_iir_4_order
string eq_filter_type_iir_6_order
string eq_filter_type_iir_8_order
string eq_interpolator
string eq_interpolator_chip
string eq_interpolator_mha
string eq_preset_acoustic
string eq_preset_bass
string eq_preset_beats
string eq_preset_classic
string eq_preset_clear
string eq_preset_deep
string eq_preset_dubstep
string eq_preset_electronic
string eq_preset_flat
string eq_preset_hardstyle
string eq_preset_hiphop
string eq_preset_jazz
string eq_preset_metal
string eq_preset_movie
string eq_preset_pop
string eq_preset_rb
string eq_preset_rock
string eq_preset_vocal
string error_projection_api_missing
string exclude_app_from_recents
string exclude_app_from_recents_off
string exclude_app_from_recents_on
string filelibrary_access_fail
string filelibrary_context_delete
string filelibrary_context_duplicate
string filelibrary_context_edit
string filelibrary_context_load
string filelibrary_context_new_preset
string filelibrary_context_new_preset_long
string filelibrary_context_overwrite
string filelibrary_context_rename
string filelibrary_context_resample
string filelibrary_context_share
string filelibrary_corrupted
string filelibrary_corrupted_title
string filelibrary_deleted
string filelibrary_file_exists
string filelibrary_file_too_new
string filelibrary_hint_tap_and_hold
string filelibrary_is_backup_not_preset
string filelibrary_new_file_name
string filelibrary_no_file_selected
string filelibrary_no_presets
string filelibrary_preset_created
string filelibrary_preset_load_failed
string filelibrary_preset_loaded
string filelibrary_preset_overwritten
string filelibrary_preset_save_failed
string filelibrary_renamed
string filelibrary_resample_complete
string filelibrary_resample_failed
string filelibrary_unsupported_format
string filelibrary_unsupported_format_title
string gcm_defaultSenderId
string gep_add_node
string geq_api_network_error
string geq_api_network_error_details_code
string geq_autoeq
string geq_cancel
string geq_cancel_spaced
string geq_delete_node
string geq_discard_changes
string geq_discard_changes_title
string geq_done
string geq_done_spaced
string geq_edit_as_string
string geq_edit_hint
string geq_enable
string geq_frequency
string geq_frequeny_range
string geq_gain
string geq_gain_range
string geq_go_back_to_node_list
string geq_next_node
string geq_no_nodes_defined
string geq_node_editor
string geq_node_list
string geq_nodes
string geq_preview
string geq_preview_collapsed
string geq_previous_node
string geq_reset
string geq_reset_confirm
string geq_reset_confirm_title
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string group_bluetooth
string group_hdmi
string group_speaker
string group_unknown
string group_usb
string group_wired_headphones
string install
string intent_import_error_file_uri
string intent_import_fail
string intent_import_irs
string intent_import_liveprog
string intent_import_mode_add
string intent_import_mode_select
string intent_import_preset
string intent_import_select_success
string intent_import_success
string intent_import_vdc
string jamesdsp
string key_about
string key_android15_screenrecord_restriction_seen
string key_appearance
string key_appearance_app_theme
string key_appearance_nav_hide
string key_appearance_pure_black
string key_appearance_show_icons
string key_appearance_theme_mode
string key_audio_format
string key_audioformat_buffersize
string key_audioformat_encoding
string key_audioformat_enhanced_processing
string key_audioformat_enhanced_processing_info
string key_audioformat_optimization_benchmark
string key_audioformat_optimization_refresh
string key_audioformat_processing
string key_autostart_prompt_at_boot
string key_backup
string key_backup_create
string key_backup_frequency
string key_backup_location
string key_backup_maximum
string key_backup_restore
string key_bass_enable
string key_bass_max_gain
string key_benchmark_c0
string key_benchmark_c1
string key_compander_enable
string key_compander_granularity
string key_compander_response
string key_compander_tftransforms
string key_compander_timeconstant
string key_convolver_adv_imp
string key_convolver_enable
string key_convolver_file
string key_convolver_mode
string key_credits_build_info
string key_credits_check_update
string key_credits_google_play
string key_credits_version
string key_crossfeed_enable
string key_crossfeed_mode
string key_ddc_enable
string key_ddc_file
string key_debug_database
string key_device_profiles
string key_device_profiles_enable
string key_device_profiles_info
string key_editor_font_size
string key_eq_bands
string key_eq_enable
string key_eq_filter_type
string key_eq_interpolation
string key_exclude_app_from_recents
string key_first_boot
string key_geq_enable
string key_geq_nodes
string key_is_activity_active
string key_is_app_compat_activity_active
string key_limiter_release
string key_limiter_threshold
string key_liveprog_edit
string key_liveprog_enable
string key_liveprog_file
string key_liveprog_params
string key_misc
string key_misc_permission_auto_start
string key_misc_permission_restart_setup
string key_misc_permission_skip_prompt
string key_network_autoeq_api_url
string key_output_postgain
string key_powered_on
string key_powersave_suspend
string key_profile_active
string key_reset_proc_mode_fix_applied
string key_reverb_enable
string key_reverb_preset
string key_session_app_problem_ignore
string key_session_continuous_polling
string key_session_continuous_polling_rate
string key_session_detection_method
string key_session_exclude_restricted
string key_session_loss_ignore
string key_share_crash_reports
string key_snooze_translation_notice
string key_stereowide_enable
string key_stereowide_mode
string key_translators
string key_troubleshooting
string key_troubleshooting_dump
string key_troubleshooting_notification_access
string key_troubleshooting_repair_assets
string key_troubleshooting_view_limitations
string key_tube_drive
string key_tube_enable
string key_update_check_skip
string key_update_check_timeout
string limit_detect_delay
string limit_detect_delay_title
string limit_hw_accel
string limit_hw_accel_title
string limit_latency
string limit_latency_title
string limit_session_control_conflict
string limit_session_control_conflict_title
string limit_tested_devices
string limit_tested_devices_title
string limit_unsupported_apps
string limit_unsupported_apps_title
string liveprog_additional_params
string liveprog_additional_params_not_supported
string liveprog_edit
string liveprog_edit_header
string liveprog_enable
string liveprog_file
string liveprog_no_script_selected
string liveprog_not_found
string load_fail_arch_card
string load_fail_arch_card_title
string load_fail_card
string load_fail_card_title
string load_fail_header
string load_fail_rootless_card
string load_fail_rootless_card_title
string message_convolver_advimp_invalid
string message_irs_corrupt
string message_liveprog_compile_fail
string message_vdc_corrupt
string misc_permission_auto_start
string misc_permission_header
string misc_permission_restart_setup
string misc_permission_restart_setup_summary
string misc_permission_skip_prompt
string misc_settings
string misc_settings_summary
string network_autoeq_api_url
string network_autoeq_conntest_done
string network_autoeq_conntest_fail
string network_autoeq_conntest_fail_summary
string network_autoeq_conntest_running
string network_invalid_url
string network_services
string never_show_warning_again
string no
string no_activity_found
string notification_channel_app_compat_alert
string notification_channel_backup_complete
string notification_channel_backup_progress
string notification_channel_permission_prompt
string notification_channel_service
string notification_channel_session_loss_alert
string notification_group_backup
string notification_group_service
string notification_idle
string notification_processing
string notification_processing_disabled_title
string notification_processing_legacy
string notification_processing_title
string notification_request_permission
string notification_request_permission_title
string number_box_decrement
string number_box_increment
string onboarding_adb_adb_title
string onboarding_adb_caption
string onboarding_adb_dump_permission_not_granted
string onboarding_adb_manual_step1
string onboarding_adb_manual_step1_button
string onboarding_adb_manual_step2
string onboarding_adb_manual_step3
string onboarding_adb_manual_step4
string onboarding_adb_manual_step5
string onboarding_adb_manual_step5b_required
string onboarding_adb_manual_step5c
string onboarding_adb_not_granted_title
string onboarding_adb_project_media_not_granted
string onboarding_adb_shizuku_grant_button
string onboarding_adb_shizuku_grant_fail_denied
string onboarding_adb_shizuku_grant_fail_denied_title
string onboarding_adb_shizuku_grant_fail_server_dead
string onboarding_adb_shizuku_grant_fail_server_dead_title
string onboarding_adb_shizuku_grant_fail_version
string onboarding_adb_shizuku_grant_fail_version_title
string onboarding_adb_shizuku_grant_instruction
string onboarding_adb_shizuku_install_button
string onboarding_adb_shizuku_install_button_done
string onboarding_adb_shizuku_install_instruction
string onboarding_adb_shizuku_no_dump_perm
string onboarding_adb_shizuku_no_dump_perm_title
string onboarding_adb_shizuku_not_installed
string onboarding_adb_shizuku_not_installed_title
string onboarding_adb_shizuku_open_button
string onboarding_adb_shizuku_open_button_done
string onboarding_adb_shizuku_open_instruction
string onboarding_adb_shizuku_title
string onboarding_back
string onboarding_caption
string onboarding_finish
string onboarding_finish_caption
string onboarding_finish_header
string onboarding_fix_permissions
string onboarding_fix_permissions_title
string onboarding_greeting
string onboarding_limitations_title
string onboarding_limitations_unstable
string onboarding_methods_adb_title
string onboarding_methods_root_adb
string onboarding_methods_root_root
string onboarding_methods_root_shizuku
string onboarding_methods_root_title
string onboarding_methods_rootless_adb
string onboarding_methods_rootless_shizuku
string onboarding_methods_shizuku_title
string onboarding_methods_title
string onboarding_methods_unsupported_append
string onboarding_next
string onboarding_perm_caption
string onboarding_perm_cast_caption
string onboarding_perm_cast_title
string onboarding_perm_diag
string onboarding_perm_diag_caption
string onboarding_perm_microphone_caption
string onboarding_perm_microphone_title
string onboarding_perm_missing
string onboarding_perm_missing_title
string onboarding_perm_notification_caption
string onboarding_perm_notification_title
string onboarding_perm_title
string onboarding_root_enhanced_processing_setup_success
string onboarding_root_shell_fail
string onboarding_root_shell_fail_title
string onboarding_root_shell_fail_unknown
string onboarding_warning
string open
string output_control_header
string output_control_postgain
string output_limiter_release
string output_limiter_threshold
string paste
string permission_allowed
string permission_not_allowed
string power_button_alt
string powersave
string powersave_suspend
string powersave_suspend_off
string powersave_suspend_on
string preparing
string privacy
string privacy_share_crash_reports
string privacy_share_crash_reports_off
string privacy_share_crash_reports_on
string profiles_enable
string profiles_enable_summary_off
string profiles_enable_summary_on
string profiles_info
string profiles_info_content
string profiles_info_title
string profiles_manage_hint
string profiles_section_header
string profiles_summary
string project_id
string redo
string reverb_enable
string reverb_preset_default
string reverb_preset_large_hall1
string reverb_preset_large_room1
string reverb_preset_long_reverb1
string reverb_preset_long_reverb2
string reverb_preset_medium_hall2
string reverb_preset_medium_room1
string reverb_preset_plate_high
string reverb_preset_plate_low
string reverb_preset_small_hall1
string reverb_preset_small_hall2
string reverb_preset_small_room1
string reverb_preset_small_room2
string reverb_room_type
string revert_confirmation
string revert_confirmation_title
string self_update_download_fail
string self_update_finished
string self_update_install_error
string self_update_install_fail
string self_update_no_updates
string self_update_notice
string self_update_notice_dismiss_install
string self_update_notice_dismiss_skip
string self_update_notice_dismiss_snooze
string self_update_notice_summary
string self_update_state_downloading
string self_update_state_installing
string session_app_compat_notification
string session_app_compat_notification_title
string session_app_compat_toast
string session_app_problem_ignore
string session_continuous_polling
string session_continuous_polling_off
string session_continuous_polling_on
string session_continuous_polling_rate
string session_control_loss_notification
string session_control_loss_notification_title
string session_control_loss_toast
string session_detection_header
string session_detection_method
string session_detection_method_audiopolicyservice
string session_detection_method_audioservice
string session_exclude_restricted
string session_exclude_restricted_off
string session_exclude_restricted_on
string session_loss_ignore
string session_loss_ignore_off
string session_loss_ignore_on
string session_loss_ignore_warning
string slider_dialog_format_error
string slider_dialog_step_error
string slider_dialog_title
string stereowide_enable
string stereowide_level
string stereowide_level_narrow
string stereowide_level_none
string stereowide_level_very_narrow
string stereowide_level_very_wide
string stereowide_level_wide
string success
string theme_default
string theme_greenapple
string theme_honey
string theme_monet
string theme_strawberrydaiquiri
string theme_tealturquoise
string theme_tidalwave
string theme_yinyang
string theme_yotsuba
string title_activity_app_compat
string title_activity_blocklist
string title_activity_geq
string title_activity_liveprog_editor
string title_activity_liveprog_params
string title_activity_onboarding
string title_activity_settings
string translation_notice
string translation_notice_summary
string translators
string troubleshooting
string troubleshooting_actions
string troubleshooting_docs
string troubleshooting_dump
string troubleshooting_dump_share_title
string troubleshooting_dump_summary
string troubleshooting_notification_access
string troubleshooting_notification_access_summary
string troubleshooting_repair_assets
string troubleshooting_repair_assets_success
string troubleshooting_repair_assets_summary
string troubleshooting_summary
string troubleshooting_view_limitations
string troubleshooting_view_limitations_summary
string tube_drive
string tube_enable
string tutorial
string undo
string unknown_error
string update
string value_not_set
string version_mismatch_root
string version_mismatch_root_description
string version_mismatch_root_toast
string warning
string yes
style AppTheme.AlertDialogTheme
style Base.Theme.RootlessJamesDSP
style Base.Theme.RootlessJamesDSP.Dialog
style ThemeOverlay.RootlessJamesDSP.Amoled
style Theme.RootlessJamesDSP
style Theme.RootlessJamesDSP.CardView
style Theme.RootlessJamesDSP.Dialog
style Theme.RootlessJamesDSP.GreenApple
style Theme.RootlessJamesDSP.Honey
style Theme.RootlessJamesDSP.Monet
style Theme.RootlessJamesDSP.SearchButton
style Theme.RootlessJamesDSP.StrawberryDaiquiri
style Theme.RootlessJamesDSP.TealTurquoise
style Theme.RootlessJamesDSP.TidalWave
style Theme.RootlessJamesDSP.YinYang
style Theme.RootlessJamesDSP.Yotsuba
style Theme.Transparent
styleable Card titleText bodyText closeButtonVisible buttonText buttonEnabled checkboxVisible iconSrc iconTint iconCentered cardBackground cardMargin
styleable DropDownPreference isStatic
styleable EqualizerPreference android:entries android:entryValues
styleable FileLibraryPreference type
styleable IconPreference iconTint
styleable MaterialSeekbarPreference seekBarIncrement showSeekBarValue updatesContinuously seekBarStyle minValue maxValue unit precision labelMinWidth
styleable NumberInputBox floatPrecision step value android:min android:max suffixText hintText helperText helperTextEnabled
xml app_about_preferences
xml app_appearance_preferences
xml app_audio_format_preferences
xml app_backup_preferences
xml app_device_profiles_preferences
xml app_misc_preferences
xml app_preferences
xml app_troubleshooting_preferences
xml backup_rules
xml data_extraction_rules
xml device_profile_preferences
xml dsp_bass_preferences
xml dsp_compander_preferences
xml dsp_convolver_preferences
xml dsp_crossfeed_preferences
xml dsp_ddc_preferences
xml dsp_equalizer_preferences
xml dsp_graphiceq_preferences
xml dsp_liveprog_preferences
xml dsp_output_control_preferences
xml dsp_reverb_preferences
xml dsp_stereowide_preferences
xml dsp_tube_preferences
xml provider_dump_paths
xml provider_filelibrary_paths
