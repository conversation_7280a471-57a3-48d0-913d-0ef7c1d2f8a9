<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <string-array name="audio_format_encodings" translatable="false">
        <item>@string/audio_format_encoding_int16</item>
        <item>@string/audio_format_encoding_float</item>
    </string-array>
    <string-array name="audio_format_encodings_values" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>
    <string-array name="backup_frequency" translatable="false">
        <item>@string/backup_never</item>
        <item>@string/backup_12hour</item>
        <item>@string/backup_24hour</item>
        <item>@string/backup_48hour</item>
        <item>@string/backup_weekly</item>
    </string-array>
    <string-array name="backup_frequency_values" translatable="false">
        <item>0</item>
        <item>12</item>
        <item>24</item>
        <item>48</item>
        <item>168</item>
    </string-array>
    <string-array name="backup_maximum" translatable="false">
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array>
    <string-array name="com.google.firebase.crashlytics.build_ids_arch" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
</string-array>
    <string-array name="com.google.firebase.crashlytics.build_ids_build_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>84458063500873d88f928022e40ebab571b0eb6f</item>
<item>a565453f1003b63ac305d7933979e88c67bede7a</item>
<item>6b90d7edb171dc8d08e8048e7f0bbf7ff3f6b715</item>
<item>acf6365f1a57d0513d7e78424b14f07c579aa5fc</item>
<item>988a3ef475baabbb23fefbe3a78321b0fdabcf83</item>
<item>ae4f326edc5b2f13cd6cd61ed6df466a92cd3528</item>
<item>f8b50c9c60f31ffe69daa7cf173d0cdf60975cd2</item>
<item>184e1bb99d4837e51ec20604790753aa182d2586</item>
<item>0a3dbcd586db47ff982af5b0d84abecf3dc275eb</item>
<item>c4fdddae11c183d3fc282c4055e0c415498e3be2</item>
<item>6ba77dbb7533b0d39295bf0b298fdc35477d4d0d</item>
<item>bc860efe07caf057f84f5a5507e1a3fee7bfbe88</item>
<item>5e64ab61bdc0fc0292344c878691de20f1f84d61</item>
<item>a996254f592aa4c819ff32f0efa38ba48bf3f1d3</item>
<item>637842a841637a2ab0f6f323713b5aa102083e2d</item>
<item>d809f88a10792119ea09c7df8bfb28063bea405b</item>
<item>1db1b3ec55d1bbbb3bd2601c448b8bfb83a038bb</item>
<item>fc263ea01b1dab970c94f2d5e6053af6a903f3cf</item>
<item>3f6a9e117ea061ef3dd21b33b3b47af883598f70</item>
<item>a764ec2fc98c82741950b8d0f8779b43a11140ec</item>
<item>ede5b131780f8d0c88a9d57aea00889421276385</item>
<item>18b72934460e45ac0fe9e5adcaa65c6b4bd247e1</item>
<item>d36cc13c1f8f3b5959ccb1351282d0c87dbc0e14</item>
<item>4c53e52ef22c3df72390baaf38633461423cfe44</item>
<item>316d533ee882d9420bfcfc20e4f3d1c56c86672f</item>
<item>f9c66dd64815e600b7265e551f847a38dc6fae3d</item>
<item>56a1bea1e58b10dcba9c1ab8a843a20f5e17a736</item>
<item>5d3b215b3145206bcd102b4f29b3cf1224255124</item>
<item>d0160199dfaac60b67577b845a3c3e3969747b22</item>
<item>3e3ced1daa72aff917ebd66b9bfece444cc7230f</item>
<item>949371520ae8c2c473823048a1973b5b31e61ee0</item>
<item>b1c9176c9d5bf2bf6a94cab2632c330a97945753</item>
</string-array>
    <string-array name="com.google.firebase.crashlytics.build_ids_lib" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
</string-array>
    <string-array name="compander_tf_transforms" translatable="false">
        <item>@string/compander_tftransforms_stft</item>
        <item>@string/compander_tftransforms_continuous_wavelet</item>
        <item>@string/compander_tftransforms_undersampling</item>
        <item>@string/compander_tftransforms_time_domain</item>
    </string-array>
    <string-array name="compander_tf_transforms_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>
    <string-array name="convolver_convolution_mode" translatable="false">
        <item>@string/convolver_convolution_mode_original</item>
        <item>@string/convolver_convolution_mode_shrink</item>
        <item>@string/convolver_convolution_mode_minimum_phase_shrink</item>
    </string-array>
    <string-array name="convolver_convolution_mode_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>
    <string-array name="crossfeed_modes" translatable="false">
        <item>@string/crossfeed_preset_bs2b_weak</item>
        <item>@string/crossfeed_preset_bs2b_strong</item>
        <item>@string/crossfeed_preset_out_of_head</item>
        <item>@string/crossfeed_preset_surround1</item>
        <item>@string/crossfeed_preset_surround2</item>
        <item>@string/crossfeed_preset_realistic_surround</item>
    </string-array>
    <string-array name="crossfeed_modes_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>
    <string-array name="editor_eel_constants">
        <item>$pi</item>
        <item>$e</item>
        <item>$phi</item>
        <item>$eps</item>
        <item>$dbl_max</item>
    </string-array>
    <string-array name="editor_eel_functions">
        <item>sin</item>
        <item>cos</item>
        <item>tan</item>
        <item>sqrt</item>
        <item>asin</item>
        <item>acos</item>
        <item>atan</item>
        <item>atan2</item>
        <item>sinh</item>
        <item>cosh</item>
        <item>tanh</item>
        <item>asinh</item>
        <item>acosh</item>
        <item>atanh</item>
        <item>sinf</item>
        <item>cosf</item>
        <item>tanf</item>
        <item>asinf</item>
        <item>acosf</item>
        <item>atanf</item>
        <item>asinhf</item>
        <item>acoshf</item>
        <item>coshf</item>
        <item>sinhf</item>
        <item>sqrtf</item>
        <item>tanhf</item>
        <item>atanhf</item>
        <item>logf</item>
        <item>log10f</item>
        <item>expf</item>
        <item>roundf</item>
        <item>floorf</item>
        <item>ceilf</item>
        <item>log</item>
        <item>log10</item>
        <item>hypot</item>
        <item>pow</item>
        <item>exp</item>
        <item>abs</item>
        <item>sqr</item>
        <item>min</item>
        <item>max</item>
        <item>sign</item>
        <item>rand</item>
        <item>round</item>
        <item>floor</item>
        <item>ceil</item>
        <item>expint</item>
        <item>expintFast</item>
        <item>invsqrt</item>
        <item>invsqrtFast</item>
        <item>circshift</item>
        <item>convolve_c</item>
        <item>maxVec</item>
        <item>minVec</item>
        <item>meanVec</item>
        <item>medianVec</item>
        <item>fft</item>
        <item>ifft</item>
        <item>fft_real</item>
        <item>ifft_real</item>
        <item>fft_permute</item>
        <item>fft_ipermute</item>
        <item>memcpy</item>
        <item>memset</item>
        <item>sleep</item>
        <item>time</item>
        <item>time_precise</item>
        <item>strlen</item>
        <item>base64_encode</item>
        <item>base64_encodeF2F</item>
        <item>base64_decode</item>
        <item>base64_decodeF2F</item>
        <item>strcmp</item>
        <item>match</item>
        <item>matchi</item>
        <item>stricmp</item>
        <item>strncmp</item>
        <item>strnicmp</item>
        <item>printf</item>
        <item>sprintf</item>
        <item>resetStringContainers</item>
        <item>importFLTFromStr</item>
        <item>arburgCheckMemoryRequirement</item>
        <item>arburgTrainModel</item>
        <item>arburgPredictBackward</item>
        <item>arburgPredictForward</item>
        <item>stftCheckMemoryRequirement</item>
        <item>stftInit</item>
        <item>stftGetWindowPower</item>
        <item>stftForward</item>
        <item>stftBackward</item>
        <item>InitPinkNoise</item>
        <item>GeneratePinkNoise</item>
        <item>InitPolyphaseFilterbank</item>
        <item>PolyphaseFilterbankChangeWarpingFactor</item>
        <item>PolyphaseFilterbankGetPhaseCorrector</item>
        <item>PolyphaseFilterbankGetDecimationFactor</item>
        <item>PolyphaseFilterbankAnalysisMono</item>
        <item>PolyphaseFilterbankSynthesisMono</item>
        <item>PolyphaseFilterbankAnalysisStereo</item>
        <item>PolyphaseFilterbankSynthesisStereo</item>
        <item>FIRInit</item>
        <item>FIRProcess</item>
        <item>IIRInit</item>
        <item>IIRProcess</item>
        <item>fractionalDelayLineInit</item>
        <item>fractionalDelayLineClear</item>
        <item>fractionalDelayLineSetDelay</item>
        <item>fractionalDelayLineProcess</item>
        <item>linspace</item>
        <item>rank</item>
        <item>det</item>
        <item>transpose</item>
        <item>cholesky</item>
        <item>inv_chol</item>
        <item>inv</item>
        <item>pinv_svd</item>
        <item>pinv_fast</item>
        <item>mldivide</item>
        <item>mrdivide</item>
        <item>quadprog</item>
        <item>lsqlin</item>
        <item>firls</item>
        <item>eqnerror</item>
        <item>unwrap</item>
        <item>zp2sos</item>
        <item>tf2sos</item>
        <item>roots</item>
        <item>cplxpair</item>
        <item>IIRBandSplitterInit</item>
        <item>IIRBandSplitterClearState</item>
        <item>IIRBandSplitterProcess</item>
        <item>Conv1DInit</item>
        <item>Conv1DProcess</item>
        <item>Conv1DFree</item>
        <item>decodeFLACFromFile</item>
        <item>decodeFLACFromMemory</item>
        <item>decodeWavFromFile</item>
        <item>decodeWavFromMemory</item>
        <item>writeWavToFile</item>
        <item>writeWavToBase64String</item>
        <item>peakFinder</item>
        <item>listSystemVariable</item>
        <item>vectorizeAssignScalar</item>
        <item>vectorizeAdd</item>
        <item>vectorizeMinus</item>
        <item>vectorizeMultiply</item>
        <item>vectorizeDivide</item>
        <item>lerpAt</item>
    </string-array>
    <string-array name="editor_eel_keywords">
        <item>function</item>
        <item>local</item>
        <item>static</item>
        <item>instance</item>
        <item>globals</item>
        <item>global</item>
        <item>this</item>
        <item>loop</item>
        <item>while</item>
        <item>this</item>
        <item>if</item>
        <item>else</item>
        <item>switch</item>
        <item>case</item>
        <item>for</item>
        <item>in</item>
        <item>do</item>
        <item>break</item>
        <item>continue</item>
        <item>true</item>
        <item>false</item>
        <item>not</item>
        <item>and</item>
        <item>or</item>
        <item>xor</item>
    </string-array>
    <string-array name="editor_predef_vars">
        <item>srate</item>
        <item>spl0</item>
        <item>spl1</item>
    </string-array>
    <string-array name="eq_filter_types" translatable="false">
        <item>@string/eq_filter_type_fir_minimum</item>
        <item>@string/eq_filter_type_iir_4_order</item>
        <item>@string/eq_filter_type_iir_6_order</item>
        <item>@string/eq_filter_type_iir_8_order</item>
        <item>@string/eq_filter_type_iir_10_order</item>
        <item>@string/eq_filter_type_iir_12_order</item>
    </string-array>
    <string-array name="eq_filter_types_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>
    <string-array name="eq_interpolators" translatable="false">
        <item>@string/eq_interpolator_chip</item>
        <item>@string/eq_interpolator_mha</item>
    </string-array>
    <string-array name="eq_interpolators_values" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>
    <string-array name="equalizer_preset_modes" translatable="false">
        <item>@string/eq_preset_acoustic</item>
        <item>@string/eq_preset_bass</item>
        <item>@string/eq_preset_beats</item>
        <item>@string/eq_preset_classic</item>
        <item>@string/eq_preset_clear</item>
        <item>@string/eq_preset_deep</item>
        <item>@string/eq_preset_dubstep</item>
        <item>@string/eq_preset_electronic</item>
        <item>@string/eq_preset_flat</item>
        <item>@string/eq_preset_hardstyle</item>
        <item>@string/eq_preset_hiphop</item>
        <item>@string/eq_preset_jazz</item>
        <item>@string/eq_preset_metal</item>
        <item>@string/eq_preset_movie</item>
        <item>@string/eq_preset_pop</item>
        <item>@string/eq_preset_rb</item>
        <item>@string/eq_preset_rock</item>
        <item>@string/eq_preset_vocal</item>
    </string-array>
    <string-array name="equalizer_preset_values">
        <item>5.00;4.50;4.00;3.50;1.50;1.00;1.50;1.50;2.00;3.00;3.50;4.00;3.70;3.00;3.00</item>
        <item>10.00;8.80;8.50;6.50;2.50;1.50;0;0;0;0;0;0;0;0;0</item>
        <item>-5.5;-5.0;-4.5;-4.2;-3.5;-3.0;-1.9;0;0;0;0;0;0;0;0</item>
        <item>-0.3;0.3;-3.5;-9.0;-1.0;0.0;1.8;2.1;0.0;0.0;0.0;4.4;9.0;9.0;9.0</item>
        <item>3.5;5.5;6.5;9.5;8.0;6.5;3.5;2.5;1.3;5.0;7.0;9.0;10.0;11.0;9.0</item>
        <item>12.0;8.0;0.0;-6.7;-12.0;-9.0;-3.5;-3.5;-6.1;0.0;-3.0;-5.0;0.0;1.2;3.0</item>
        <item>12.0;10.0;0.5;-1.0;-3.0;-5.0;-5.0;-4.8;-4.5;-2.5;-1.0;0.0;-2.5;-2.5;0.0</item>
        <item>4.0;4.0;3.5;1.0;0.0;-0.5;-2.0;0.0;2.0;0.0;0.0;1.0;3.0;4.0;4.5</item>
        <item>0;0;0;0;0;0;0;0;0;0;0;0;0;0;0</item>
        <item>6.1;7.0;12.0;6.1;-5.0;-12.0;-2.5;3.0;6.5;0.0;-2.2;-4.5;-6.1;-9.2;-10.0</item>
        <item>4.5;4.3;4.0;2.5;1.5;3.0;-1.0;-1.5;-1.5;1.5;0.0;-1.0;0.0;1.5;3.0</item>
        <item>0.0;0.0;0.0;2.0;4.0;5.9;-5.9;-4.5;-2.5;2.5;1.0;-0.8;-0.8;-0.8;-0.8</item>
        <item>10.5;10.5;7.5;0.0;2.0;5.5;0.0;0.0;0.0;6.1;0.0;0.0;6.1;10.0;12.0</item>
        <item>3.0;3.0;6.1;8.5;9.0;7.0;6.1;6.1;5.0;8.0;3.5;3.5;8.0;10.0;8.0</item>
        <item>0.0;0.0;0.0;0.0;0.0;1.3;2.0;2.5;5.0;-1.5;-2.0;-3.0;-3.0;-3.0;-3.0</item>
        <item>3.0;3.0;7.0;6.1;4.5;1.5;-1.5;-2.0;-1.5;2.0;2.5;3.0;3.5;3.8;4.0</item>
        <item>0.0;0.0;0.0;3.0;3.0;-10.0;-4.0;-1.0;0.8;3.0;3.0;3.0;3.0;3.0;3.0</item>
        <item>-1.5;-2.0;-3.0;-3.0;-0.5;1.5;3.5;3.5;3.5;3.0;2.0;1.5;0.0;0.0;-1.5</item>
    </string-array>
    <string-array name="manange_profiles_menu" translatable="false">
        <item>@string/device_profile_manage_copy</item>
        <item>@string/device_profile_manage_delete</item>
    </string-array>
    <string-array name="reverb_presets" translatable="false">
        <item>@string/reverb_preset_default</item>
        <item>@string/reverb_preset_small_hall1</item>
        <item>@string/reverb_preset_small_hall2</item>
        
        <item>@string/reverb_preset_medium_hall2</item>
        <item>@string/reverb_preset_large_hall1</item>
        
        <item>@string/reverb_preset_small_room1</item>
        <item>@string/reverb_preset_small_room2</item>
        <item>@string/reverb_preset_medium_room1</item>
        
        <item>@string/reverb_preset_large_room1</item>
        
        
        
        <item>@string/reverb_preset_plate_high</item>
        <item>@string/reverb_preset_plate_low</item>
        <item>@string/reverb_preset_long_reverb1</item>
        <item>@string/reverb_preset_long_reverb2</item>
    </string-array>
    <string-array name="reverb_presets_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        
        <item>4</item>
        <item>5</item>
        
        <item>7</item>
        <item>8</item>
        <item>9</item>
        
        <item>11</item>
        
        
        
        <item>15</item>
        <item>16</item>
        <item>17</item>
        <item>18</item>
    </string-array>
    <string-array name="session_detection_methods" translatable="false">
        <item>@string/session_detection_method_audioservice</item>
        <item>@string/session_detection_method_audiopolicyservice</item>
    </string-array>
    <string-array name="session_detection_methods_values" translatable="false">
        <item>1</item>
        <item>0</item>
    </string-array>
    <string-array name="theme_modes" translatable="false">
        <item>@string/appearance_theme_mode_default</item>
        <item>@string/appearance_theme_mode_light</item>
        <item>@string/appearance_theme_mode_dark</item>
    </string-array>
    <string-array name="theme_modes_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>
    <string-array name="update_dismiss_dialog" translatable="false">
        <item>@string/self_update_notice_dismiss_install</item>
        <item>@string/self_update_notice_dismiss_skip</item>
        <item>@string/self_update_notice_dismiss_snooze</item>
    </string-array>
    <attr format="color" name="iconTint"/>
    <bool name="default_android15_screenrecord_restriction_seen" ns1:keep="@bool/default_android15_screenrecord_restriction_seen" translatable="false">false</bool>
    <bool name="default_appearance_nav_hide" translatable="false">false</bool>
    <bool name="default_appearance_pure_black" translatable="false">false</bool>
    <bool name="default_appearance_show_icons" translatable="false">true</bool>
    <bool name="default_audioformat_enhanced_processing" translatable="false">false</bool>
    <bool name="default_audioformat_optimization_benchmark" translatable="false">false</bool>
    <bool name="default_audioformat_processing" translatable="false">true</bool>
    <bool name="default_autostart_prompt_at_boot" translatable="false">true</bool>
    <bool name="default_device_profiles_enable" translatable="false">false</bool>
    <bool name="default_exclude_app_from_recents" translatable="false">false</bool>
    <bool name="default_first_boot" ns1:keep="@bool/default_first_boot" translatable="false">true</bool>
    <bool name="default_is_activity_active" ns1:keep="@bool/default_is_activity_active" translatable="false">false</bool>
    <bool name="default_is_app_compat_activity_active" ns1:keep="@bool/default_is_app_compat_activity_active" translatable="false">false</bool>
    <bool name="default_powered_on" ns1:keep="@bool/default_powered_on" translatable="false">true</bool>
    <bool name="default_powersave_suspend" translatable="false">true</bool>
    <bool name="default_reset_proc_mode_fix_applied" ns1:keep="@bool/default_reset_proc_mode_fix_applied" translatable="false">false</bool>
    <bool name="default_session_app_problem_ignore" translatable="false">false</bool>
    <bool name="default_session_continuous_polling" translatable="false">false</bool>
    <bool name="default_session_exclude_restricted">true</bool>
    <bool name="default_session_loss_ignore" translatable="false">false</bool>
    <bool name="default_share_crash_reports" translatable="false">true</bool>
    <color name="background_amoled">#000000</color>
    <color name="black">#000000</color>
    <color name="divider_default">@color/md_black_1000_12</color>
    <color name="gold">#e6b121</color>
    <color name="greenapple_background">#FBFDF7</color>
    <color name="greenapple_inverseOnSurface">#F0F2EC</color>
    <color name="greenapple_inverseSurface">#2F312E</color>
    <color name="greenapple_onBackground">#1A1C19</color>
    <color name="greenapple_onPrimary">#FFFFFF</color>
    <color name="greenapple_onPrimaryContainer">#002109</color>
    <color name="greenapple_onSecondary">#FFFFFF</color>
    <color name="greenapple_onSecondaryContainer">#002109</color>
    <color name="greenapple_onSurface">#1A1C19</color>
    <color name="greenapple_onSurfaceVariant">#414941</color>
    <color name="greenapple_onTertiary">#FFFFFF</color>
    <color name="greenapple_onTertiaryContainer">#410003</color>
    <color name="greenapple_outline">#717970</color>
    <color name="greenapple_primary">#006D2F</color>
    <color name="greenapple_primaryContainer">#96F8A9</color>
    <color name="greenapple_primaryInverse">#7ADB8F</color>
    <color name="greenapple_secondary">#006D2F</color>
    <color name="greenapple_secondaryContainer">#96F8A9</color>
    <color name="greenapple_surface">#FBFDF7</color>
    <color name="greenapple_surfaceVariant">#DDE5DA</color>
    <color name="greenapple_tertiary">#B91D22</color>
    <color name="greenapple_tertiaryContainer">#FFDAD5</color>
    <color name="honey_background">#FFFBFF</color>
    <color name="honey_elevationOverlay">@color/honey_primary</color>
    <color name="honey_error">#BA1A1A</color>
    <color name="honey_errorContainer">#FFDAD6</color>
    <color name="honey_inverseOnSurface">#FAEFE7</color>
    <color name="honey_inverseSurface">#352F2A</color>
    <color name="honey_onBackground">#1F1B16</color>
    <color name="honey_onError">#FFFFFF</color>
    <color name="honey_onErrorContainer">#410002</color>
    <color name="honey_onPrimary">#FFFFFF</color>
    <color name="honey_onPrimaryContainer">#2B1700</color>
    <color name="honey_onSecondary">#FFFFFF</color>
    <color name="honey_onSecondaryContainer">#321300</color>
    <color name="honey_onSurface">#1F1B16</color>
    <color name="honey_onSurfaceVariant">#50453A</color>
    <color name="honey_onTertiary">#FFFFFF</color>
    <color name="honey_onTertiaryContainer">#3B002F</color>
    <color name="honey_outline">#827568</color>
    <color name="honey_primary">#885200</color>
    <color name="honey_primaryContainer">#FFC996</color>
    <color name="honey_primaryInverse">#FFB869</color>
    <color name="honey_secondary">#994700</color>
    <color name="honey_secondaryContainer">#FFDBC8</color>
    <color name="honey_shadow">#000000</color>
    <color name="honey_surface">#FFFBFF</color>
    <color name="honey_surfaceTint">#885200</color>
    <color name="honey_surfaceTintColor">#885200</color>
    <color name="honey_surfaceVariant">#F1DFD0</color>
    <color name="honey_tertiary">#90427A</color>
    <color name="honey_tertiaryContainer">#FFD8EE</color>
    <color name="md_black_1000">#000000</color>
    <color name="md_black_1000_12">#1F000000</color>
    <color name="md_black_1000_38">#61000000</color>
    <color name="md_black_1000_54">#8A000000</color>
    <color name="md_black_1000_6">#0F000000</color>
    <color name="md_black_1000_8">#14000000</color>
    <color name="md_black_1000_87">#DE000000</color>
    <color name="md_blue_A200">#448AFF</color>
    <color name="md_blue_A200_50">#80448AFF</color>
    <color name="md_blue_A200_75">#BF448AFF</color>
    <color name="md_blue_A400">#2979FF</color>
    <color name="md_blue_A400_12">#1F2979FF</color>
    <color name="md_blue_A400_38">#612979FF</color>
    <color name="md_blue_A400_75">#BF2979FF</color>
    <color name="md_blue_grey_800">#37474F</color>
    <color name="md_blue_grey_900">#263238</color>
    <color name="md_grey_100">#F5F5F5</color>
    <color name="md_grey_300">#E0E0E0</color>
    <color name="md_grey_50">#FAFAFA</color>
    <color name="md_grey_50_75">#BFFAFAFA</color>
    <color name="md_grey_800">#424242</color>
    <color name="md_grey_900">#212121</color>
    <color name="md_grey_900_75">#BF212121</color>
    <color name="md_white_1000">#FFFFFFFF</color>
    <color name="md_white_1000_12">#1FFFFFFF</color>
    <color name="md_white_1000_20">#33FFFFFF</color>
    <color name="md_white_1000_50">#80FFFFFF</color>
    <color name="md_white_1000_54">#8AFFFFFF</color>
    <color name="md_white_1000_6">#0FFFFFFF</color>
    <color name="md_white_1000_70">#B3FFFFFF</color>
    <color name="md_white_1000_8">#14FFFFFF</color>
    <color name="monokia_pro_black">#2d2a2e</color>
    <color name="monokia_pro_error">#A30000</color>
    <color name="monokia_pro_gray">#323033</color>
    <color name="monokia_pro_green">#9ccc6b</color>
    <color name="monokia_pro_grey">#727072</color>
    <color name="monokia_pro_orange">#ffd866</color>
    <color name="monokia_pro_pink">#ff6188</color>
    <color name="monokia_pro_purple">#ab9df2</color>
    <color name="monokia_pro_sky">#78dce8</color>
    <color name="monokia_pro_sky_dim">#D1EEF1</color>
    <color name="monokia_pro_white">#fcfcfa</color>
    <color name="monokia_pro_white_dim">#BEBEBE</color>
    <color name="strawberry_background">#FCFCFC</color>
    <color name="strawberry_inverseOnSurface">#FBEDED</color>
    <color name="strawberry_inverseSurface">#362F2F</color>
    <color name="strawberry_onBackground">#201A1A</color>
    <color name="strawberry_onPrimary">#FFFFFF</color>
    <color name="strawberry_onPrimaryContainer">#40000D</color>
    <color name="strawberry_onSecondary">#FFFFFF</color>
    <color name="strawberry_onSecondaryContainer">#40000D</color>
    <color name="strawberry_onSurface">#201A1A</color>
    <color name="strawberry_onSurfaceVariant">#534344</color>
    <color name="strawberry_onTertiary">#FFFFFF</color>
    <color name="strawberry_onTertiaryContainer">#2A1800</color>
    <color name="strawberry_outline">#857374</color>
    <color name="strawberry_primary">#B61E40</color>
    <color name="strawberry_primaryContainer">#E8727B</color>
    <color name="strawberry_primaryInverse">#FFB2B9</color>
    <color name="strawberry_secondary">#B61E40</color>
    <color name="strawberry_secondaryContainer">#FFDADD</color>
    <color name="strawberry_surface">#FCFCFC</color>
    <color name="strawberry_surfaceVariant">#F4DDDD</color>
    <color name="strawberry_tertiary">#775930</color>
    <color name="strawberry_tertiaryContainer">#FFDDB1</color>
    <color name="surface_amoled">#000001</color>
    <color name="tealturquoise_background">#FAFAFA</color>
    <color name="tealturquoise_elevationOverlay">#BFDFDF</color>
    <color name="tealturquoise_inverseOnSurface">#FAFAFA</color>
    <color name="tealturquoise_inverseSurface">#050505</color>
    <color name="tealturquoise_onBackground">#050505</color>
    <color name="tealturquoise_onPrimary">#FFFFFF</color>
    <color name="tealturquoise_onPrimaryContainer">#FFFFFF</color>
    <color name="tealturquoise_onSecondary">#FFFFFF</color>
    <color name="tealturquoise_onSecondaryContainer">#008080</color>
    <color name="tealturquoise_onSurface">#050505</color>
    <color name="tealturquoise_onSurfaceVariant">#050505</color>
    <color name="tealturquoise_onTertiary">#000000</color>
    <color name="tealturquoise_onTertiaryContainer">#FF7F7F</color>
    <color name="tealturquoise_outline">#6F7977</color>
    <color name="tealturquoise_primary">#008080</color>
    <color name="tealturquoise_primaryContainer">#008080</color>
    <color name="tealturquoise_primaryInverse">#40E0D0</color>
    <color name="tealturquoise_secondary">#008080</color>
    <color name="tealturquoise_secondaryContainer">#BFDFDF</color>
    <color name="tealturquoise_surface">#FAFAFA</color>
    <color name="tealturquoise_surfaceVariant">#DAE5E2</color>
    <color name="tealturquoise_tertiary">#FF7F7F</color>
    <color name="tealturquoise_tertiaryContainer">#2A1616</color>
    <color name="tidalwave_background">#fdfbff</color>
    <color name="tidalwave_inverseOnSurface">#ffe3c4</color>
    <color name="tidalwave_inverseSurface">#020400</color>
    <color name="tidalwave_onBackground">#001c3b</color>
    <color name="tidalwave_onPrimary">#ffffff</color>
    <color name="tidalwave_onPrimaryContainer">#001f28</color>
    <color name="tidalwave_onSecondary">#ffffff</color>
    <color name="tidalwave_onSecondaryContainer">#001f28</color>
    <color name="tidalwave_onSurface">#001c3b</color>
    <color name="tidalwave_onSurfaceVariant">#40484c</color>
    <color name="tidalwave_onTertiary">#001c3b</color>
    <color name="tidalwave_onTertiaryContainer">#78ffd6</color>
    <color name="tidalwave_outline">#70787c</color>
    <color name="tidalwave_primary">#006780</color>
    <color name="tidalwave_primaryContainer">#B4D4DF</color>
    <color name="tidalwave_primaryInverse">#B4ECFF</color>
    <color name="tidalwave_secondary">#006780</color>
    <color name="tidalwave_secondaryContainer">#b8eaff</color>
    <color name="tidalwave_surface">#fdfbff</color>
    <color name="tidalwave_surfaceVariant">#dce4e8</color>
    <color name="tidalwave_tertiary">#92f7bc</color>
    <color name="tidalwave_tertiaryContainer">#c3fada</color>
    <color name="yinyang_background">#FDFDFD</color>
    <color name="yinyang_inverseOnSurface">#F4F4F4</color>
    <color name="yinyang_inverseSurface">#333333</color>
    <color name="yinyang_onBackground">#222222</color>
    <color name="yinyang_onPrimary">#FFFFFF</color>
    <color name="yinyang_onPrimaryContainer">#FFFFFF</color>
    <color name="yinyang_onSecondary">#FFFFFF</color>
    <color name="yinyang_onSecondaryContainer">#0C0C0C</color>
    <color name="yinyang_onSurface">#222222</color>
    <color name="yinyang_onSurfaceVariant">#515151</color>
    <color name="yinyang_onTertiary">#000000</color>
    <color name="yinyang_onTertiaryContainer">#001947</color>
    <color name="yinyang_outline">#838383</color>
    <color name="yinyang_primary">#000000</color>
    <color name="yinyang_primaryContainer">#000000</color>
    <color name="yinyang_primaryInverse">#555555</color>
    <color name="yinyang_secondary">#000000</color>
    <color name="yinyang_secondaryContainer">#AAAAAA</color>
    <color name="yinyang_surface">#FDFDFD</color>
    <color name="yinyang_surfaceVariant">#CCCCCC</color>
    <color name="yinyang_tertiary">#FFFFFF</color>
    <color name="yinyang_tertiaryContainer">#D8E2FF</color>
    <color name="yotsuba_background">#FFFBFF</color>
    <color name="yotsuba_error">#BA1A1A</color>
    <color name="yotsuba_errorContainer">#FFDAD6</color>
    <color name="yotsuba_inverseOnSurface">#FFEDE9</color>
    <color name="yotsuba_inverseSurface">#5E1605</color>
    <color name="yotsuba_onBackground">#3D0700</color>
    <color name="yotsuba_onError">#FFFFFF</color>
    <color name="yotsuba_onErrorContainer">#410002</color>
    <color name="yotsuba_onPrimary">#FFFFFF</color>
    <color name="yotsuba_onPrimaryContainer">#3A0B00</color>
    <color name="yotsuba_onSecondary">#FFFFFF</color>
    <color name="yotsuba_onSecondaryContainer">#3A0B00</color>
    <color name="yotsuba_onSurface">#3D0700</color>
    <color name="yotsuba_onSurfaceVariant">#53433F</color>
    <color name="yotsuba_onTertiary">#FFFFFF</color>
    <color name="yotsuba_onTertiaryContainer">#221B00</color>
    <color name="yotsuba_outline">#85736E</color>
    <color name="yotsuba_primary">#AE3200</color>
    <color name="yotsuba_primaryContainer">#FBC6B6</color>
    <color name="yotsuba_primaryInverse">#FFB59E</color>
    <color name="yotsuba_secondary">#A63B16</color>
    <color name="yotsuba_secondaryContainer">#FFDBD0</color>
    <color name="yotsuba_shadow">#000000</color>
    <color name="yotsuba_surface">#FFFBFF</color>
    <color name="yotsuba_surfaceTint">#AE3200</color>
    <color name="yotsuba_surfaceTintColor">#AE3200</color>
    <color name="yotsuba_surfaceVariant">#F5DED7</color>
    <color name="yotsuba_tertiary">#705D00</color>
    <color name="yotsuba_tertiaryContainer">#FFE173</color>
    <dimen name="corner_radius">12dp</dimen>
    <integer name="default_audioformat_buffersize" translatable="false">8192</integer>
    <integer name="default_editor_font_size" ns1:keep="@integer/default_editor_font_size" translatable="false">13</integer>
    <integer name="default_snooze_translation_notice" ns1:keep="@integer/default_snooze_translation_notice" translatable="false">0</integer>
    <integer name="default_update_check_skip" ns1:keep="@integer/default_update_check_skip" translatable="false">0</integer>
    <integer name="default_update_check_timeout" ns1:keep="@integer/default_update_check_timeout" translatable="false">0</integer>
    <plurals name="custom_parameters">
        <item quantity="one">%d customizable parameter</item>
        <item quantity="other">%d customizable parameters</item>
    </plurals>
    <plurals name="nodes">
        <item quantity="one">%d node</item>
        <item quantity="other">%d nodes</item>
    </plurals>
    <plurals name="unsupported_apps">
        <item quantity="one">%d unsupported app has been automatically excluded. Tap for details.</item>
        <item quantity="other">%d unsupported apps have been automatically excluded. Tap for details.</item>
    </plurals>
    <string name="about_settings">About</string>
    <string name="about_settings_summary">Credits, support, updates</string>
    <string name="action_blocked_apps">Excluded apps</string>
    <string name="action_fix">Fix issue</string>
    <string name="action_import">Import</string>
    <string name="action_presets">Presets</string>
    <string name="action_restore_defaults">Restore defaults</string>
    <string name="action_retry">Retry</string>
    <string name="action_revert">Reset to defaults</string>
    <string name="action_settings">Settings</string>
    <string name="action_stop">Stop</string>
    <string name="actions">Actions</string>
    <string name="add">Add</string>
    <string name="android_15_screenshare_keyguard_warning">Android 15 introduces a restriction that stops screen sharing when the device is locked. To workaround this issue, the \'PROJECT_MEDIA\' permission must be granted using Shizuku or ADB.

Please press \'Continue\' below to re-do the setup wizard.</string>
    <string name="android_15_screenshare_warning">Android 15 introduces a new screen recording restriction that will redact all sensitive information (e.g. in your notifications) while an app is using the screen record permission.
Even though this app only records system audio, it is still affected by this restriction.

To workaround this issue, you can enable \'Disable screen share protections\' in the system developer settings.
Tap on \'Tutorial\' below for detailed instructions.</string>
    <string name="android_15_screenshare_warning_title">Android 15 compatibility notice</string>
    <string name="app_behavior">App behavior</string>
    <string name="app_compat_exclude">Exclude</string>
    <string name="app_compat_explanation">The following app is unsupported because it causes RootlessJamesDSP to lose control over its audio routing:</string>
    <string name="app_compat_instruction">Please check the app\'s settings if you can disable HW-acceleration or other settings that could interfere with RootlessJamesDSP and tap \'Retry\'.</string>
    <string name="app_compat_instruction_exclude">Alternatively, select \'Exclude\' to bypass this issue by excluding the incompatible app from audio processing.</string>
    <string name="app_compat_retry">Retry</string>
    <string name="app_compat_title">Audio processing has been suspended to prevent duplicated audio</string>
    <string name="app_compat_unknown_pkg_name">Unknown package name</string>
    <string name="app_list_icon_alt">App icon</string>
    <string name="app_list_loading">Loading apps…</string>
    <string name="app_list_search">Search</string>
    <string name="appearance_app_theme">App theme</string>
    <string name="appearance_nav_hide">Hide navigation bar on scroll</string>
    <string name="appearance_navigation_title">Navigation</string>
    <string name="appearance_pure_black_mode">Pure black dark mode</string>
    <string name="appearance_section_header">Appearance</string>
    <string name="appearance_show_icons">Show category icons</string>
    <string name="appearance_summary">App theme, pure black dark mode</string>
    <string name="appearance_theme_mode">Dark mode</string>
    <string name="appearance_theme_mode_dark">On</string>
    <string name="appearance_theme_mode_default">Follow system theme</string>
    <string name="appearance_theme_mode_light">Off</string>
    <string name="appearance_title">Theme</string>
    <string name="assets">Assets</string>
    <string name="audio_format_buffer_size">Buffer size</string>
    <string name="audio_format_buffer_size_unit"> samples</string>
    <string name="audio_format_buffer_size_warning_low_value">Warning: Low buffer sizes may cause audio issues such as clipping!</string>
    <string name="audio_format_encoding">Audio encoding</string>
    <string name="audio_format_encoding_float">32-bit float PCM</string>
    <string name="audio_format_encoding_int16">16-bit integer PCM</string>
    <string name="audio_format_enhanced_processing">Enhanced processing</string>
    <string name="audio_format_enhanced_processing_info">Tap to view documentation</string>
    <string name="audio_format_enhanced_processing_info_content">When enhanced processing is enabled, you can selectively exclude certain apps from being processed by JamesDSP. Use the 3-dot menu on the bottom right corner and select \'Excluded apps\' to access this feature.\n\nWhile legacy mode is disabled, JamesDSP relies on media apps to send a system broadcast when they start playback. This is an advantage over legacy mode because each app gets its own JamesDSP effect engine assigned to it, which allows for more fine-grained control. Otherwise, with legacy mode turned on, all apps would share a single processing engine, which Android deprecates and should not be used anymore.\n\nUnfortunately, not all media apps do this correctly and, therefore, would not function correctly with legacy mode disabled. To solve this issue, you can enable enhanced processing. Instead of relying on system broadcasts about new media sessions, JamesDSP will proactively scan for media sessions by itself and allow proper support for uncooperative media apps.</string>
    <string name="audio_format_enhanced_processing_info_title">What is enhanced processing?</string>
    <string name="audio_format_enhanced_processing_off">Enhanced processing disabled</string>
    <string name="audio_format_enhanced_processing_on">Per-app control and enhanced support for apps enabled</string>
    <string name="audio_format_header">Audio processing</string>
    <string name="audio_format_media_apps_need_restart">You need to restart all active media apps after changing this setting to resume audio processing.</string>
    <string name="audio_format_optimization_benchmark">Use benchmarks to optimize performance</string>
    <string name="audio_format_optimization_benchmark_ongoing">Benchmark in progress…</string>
    <string name="audio_format_optimization_benchmark_summary">Increases performance of effects that use convolver modules</string>
    <string name="audio_format_optimization_header">Convolver module optimizations</string>
    <string name="audio_format_optimization_refresh">Refresh benchmarking data</string>
    <string name="audio_format_processing_header">Audio processing options</string>
    <string name="audio_format_processing_legacy">Legacy mode</string>
    <string name="audio_format_processing_legacy_off">Attach to each app individually</string>
    <string name="audio_format_processing_legacy_on">Attach globally to session 0 (deprecated)</string>
    <string name="audio_format_section_header">Preferred audio format</string>
    <string name="audio_format_summary">Preferred audio encoding, buffer size</string>
    <string name="audio_format_summary_plugin">Convolver optimizations</string>
    <string name="audio_format_summary_root">Legacy mode, enhanced processing</string>
    <string name="autoeq_enter_model">Enter your headphone model…</string>
    <string name="autoeq_no_results">No results found</string>
    <string name="autoeq_partial_results_warning">Showing the first %1$d results. Please fine-tune your search to get more relevant results.</string>
    <string name="autoeq_search">AutoEQ search</string>
    <string name="autostart_prompt_at_boot">Prompt for capture permission after boot</string>
    <string name="autostart_prompt_at_boot_off">Don\'t show notification after boot</string>
    <string name="autostart_prompt_at_boot_on">Show notification after boot</string>
    <string name="autostart_service_at_boot">Launch after boot</string>
    <string name="autostart_service_at_boot_off">Don\'t start service automatically after boot</string>
    <string name="autostart_service_at_boot_on">Start service automatically after boot</string>
    <string name="backup_12hour">Every 12 hours</string>
    <string name="backup_24hour">Daily</string>
    <string name="backup_48hour">Every 2 days</string>
    <string name="backup_automatic_backup">Automatic backup</string>
    <string name="backup_compat_info">Backups only include impulse responses, scripts, VDC files, presets, and the current audio configuration for maximum compatibility with other versions.</string>
    <string name="backup_create">Create backup</string>
    <string name="backup_create_completed">Backup created</string>
    <string name="backup_create_error">Backup failed</string>
    <string name="backup_create_progress">Creating backup</string>
    <string name="backup_create_summary">Can be used to restore the current settings later</string>
    <string name="backup_frequency">Backup frequency</string>
    <string name="backup_in_progress">Backup already in progress</string>
    <string name="backup_location">Backup location</string>
    <string name="backup_manual_backup">Manual backup</string>
    <string name="backup_maximum">Maximum backups</string>
    <string name="backup_never">Never</string>
    <string name="backup_restore">Restore backup</string>
    <string name="backup_restore_completed">Restore completed</string>
    <string name="backup_restore_error">Restoring backup failed</string>
    <string name="backup_restore_error_format">Unsupported or corrupted backup file</string>
    <string name="backup_restore_error_version_too_new">The backup has been created with a newer version of this application and cannot be loaded. Please update this app to continue.</string>
    <string name="backup_restore_mode_clean">Clean restore (delete all existing files)</string>
    <string name="backup_restore_mode_dirty">Dirty restore (overwrite only)</string>
    <string name="backup_restore_mode_title">Restore mode</string>
    <string name="backup_restore_progress">Restoring backup</string>
    <string name="backup_restore_summary">Restore settings from backup file</string>
    <string name="backup_select_location">Please create and select a backup directory…</string>
    <string name="backup_settings">Backup and restore</string>
    <string name="backup_settings_summary">Automatic &amp; manual backups</string>
    <string name="backup_weekly">Weekly</string>
    <string name="bass_enable">Dynamic bass boost</string>
    <string name="bass_max_gain">Maximum gain</string>
    <string name="blocklist_add_exclusion_alt">Add application to exclude</string>
    <string name="blocklist_delete">Do you want to remove this app from the exclusion list?</string>
    <string name="blocklist_delete_title">Remove selected app?</string>
    <string name="blocklist_no_exclusions">No excluded apps</string>
    <string name="blocklist_unsupported_apps">Unsupported apps</string>
    <string name="blocklist_unsupported_apps_message"><![CDATA[<p>This app does not support processing apps that block third parties from recording their audio output. To prevent these apps from being muted due to their capture restrictions, they are automatically excluded from audio processing and are not re-routed.</p><p><u>Affected apps:</u><br/>%s</p>]]></string>
    <string name="capture_permission_revoked_toast">JamesDSP has been stopped because the audio capture permission was revoked.</string>
    <string name="close">Close</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="compander_enable">Dynamic range compander</string>
    <string name="compander_granularity">Granularity</string>
    <string name="compander_granularity_extreme">Extreme</string>
    <string name="compander_granularity_high">High</string>
    <string name="compander_granularity_low">Low</string>
    <string name="compander_granularity_medium">Medium</string>
    <string name="compander_granularity_very_low">Very low</string>
    <string name="compander_tftransforms">Time-frequency transform</string>
    <string name="compander_tftransforms_continuous_wavelet">Multiresolution (Continuous wavelet, incomplete dual frame)</string>
    <string name="compander_tftransforms_stft">Uniform (Short-time Fourier)</string>
    <string name="compander_tftransforms_time_domain">Pseudo multiresolution (Time domain, zero latency)</string>
    <string name="compander_tftransforms_undersampling">Pseudo multiresolution (Undersampling frame)</string>
    <string name="compander_timeconstant">Time constant</string>
    <string name="continue_action">Continue</string>
    <string name="convolver_advanced_editing">Advanced waveform editing</string>
    <string name="convolver_convolution_mode">Impulse response optimization</string>
    <string name="convolver_convolution_mode_minimum_phase_shrink">Minimum phase transform and shrink</string>
    <string name="convolver_convolution_mode_original">Original</string>
    <string name="convolver_convolution_mode_shrink">Shrink</string>
    <string name="convolver_enable">Convolver</string>
    <string name="convolver_impulse">Impulse response</string>
    <string name="copy">Copy</string>
    <string name="credits">Credits</string>
    <string name="credits_app">App &amp; rootless implementation developed by</string>
    <string name="credits_build_info">Build info</string>
    <string name="credits_dsp">JamesDSP core algorithm library developed by</string>
    <string name="credits_project_check_update">Check for new updates…</string>
    <string name="credits_project_check_update_summary">Download and install updates if available</string>
    <string name="credits_project_page">Visit project website on GitHub</string>
    <string name="credits_project_page_summary">Report issues, keep yourself updated, and read the source code</string>
    <string name="credits_project_play_page">Visit RootlessJamesDSP on Google Play</string>
    <string name="credits_project_play_page_summary">Search for new updates</string>
    <string name="credits_project_translate">Contribute translations</string>
    <string name="credits_project_translate_summary">Help us to translate this app into your language</string>
    <string name="credits_version">Application version</string>
    <string name="crossfeed_enable">Crossfeed</string>
    <string name="crossfeed_preset">Preset</string>
    <string name="crossfeed_preset_bs2b_strong">BS2B Strong</string>
    <string name="crossfeed_preset_bs2b_weak">BS2B Weak</string>
    <string name="crossfeed_preset_out_of_head">Out of head</string>
    <string name="crossfeed_preset_realistic_surround">Joe0Bloggs Realistic surround</string>
    <string name="crossfeed_preset_surround1">Surround 1</string>
    <string name="crossfeed_preset_surround2">Surround 2</string>
    <string name="ddc_enable">ViPER-DDC</string>
    <string name="ddc_file">DDC file</string>
    <string name="default_appearance_app_theme" translatable="false">DEFAULT</string>
    <string name="default_appearance_theme_mode" translatable="false">0</string>
    <string name="default_audioformat_encoding" translatable="false">1</string>
    <string name="default_backup_frequency" translatable="false">0</string>
    <string name="default_backup_location" translatable="false"/>
    <string name="default_backup_maximum" translatable="false">2</string>
    <string name="default_benchmark_c0" ns1:keep="@string/default_benchmark_c0" translatable="false"/>
    <string name="default_benchmark_c1" ns1:keep="@string/default_benchmark_c0" translatable="false"/>
    <string name="default_network_autoeq_api_url" translatable="false">https://aeq.timschneeberger.me/</string>
    <string name="default_session_continuous_polling_rate" translatable="false">3000</string>
    <string name="default_session_detection_method" translatable="false">1</string>
    <string name="default_web_client_id" translatable="false">1418102932-44cspu1cfn147b8gr84ov1f023lk8msc.apps.googleusercontent.com</string>
    <string name="delete">Delete</string>
    <string name="details">Details</string>
    <string name="device_profile_manage_copy">Copy profile…</string>
    <string name="device_profile_manage_copy_select">Select profile to copy</string>
    <string name="device_profile_manage_copy_select_no_target">No profiles to overwrite available</string>
    <string name="device_profile_manage_delete">Delete profiles…</string>
    <string name="device_profile_manage_paste_select">Select profiles to overwrite</string>
    <string name="device_profile_status">Active device profile</string>
    <string name="editor_cannot_redo">Nothing to redo.</string>
    <string name="editor_cannot_undo">Nothing to undo.</string>
    <string name="editor_docs">Help</string>
    <string name="editor_engine_down">Cannot run script because JamesDSP is disabled. Please turn on JamesDSP to execute scripts.</string>
    <string name="editor_engine_down_title">JamesDSP is not active</string>
    <string name="editor_find_and_replace">Find/replace…</string>
    <string name="editor_find_next_match">Find next match</string>
    <string name="editor_find_previous_match">Find previous match</string>
    <string name="editor_liveprog_enabled">Liveprog has been automatically turned on.</string>
    <string name="editor_open_fail">Script file does not exist or cannot be opened.</string>
    <string name="editor_replace_all">Replace all</string>
    <string name="editor_replacement">Replacement</string>
    <string name="editor_run">Save and run</string>
    <string name="editor_save">Save</string>
    <string name="editor_save_prompt">The current script has unsaved changes. Do you want to save your changes or discard them?</string>
    <string name="editor_save_prompt_title">Save changes?</string>
    <string name="editor_script_launched">Script launched</string>
    <string name="editor_search_keyword">Search</string>
    <string name="editor_source_position">%1$d:%2$d</string>
    <string name="editor_text_size">Text size</string>
    <string name="enhanced_processing_feature_unavailable">Feature unavailable</string>
    <string name="enhanced_processing_feature_unavailable_content">This feature is only available when \'enhanced processing\' is enabled in the settings. Please go to \'Settings > Audio processing > Enhanced processing\' and enable that option to access this menu.\n\nNote: \'Enhanced processing\' can not be used while legacy mode is enabled.</string>
    <string name="enhanced_processing_missing_perm">Enhanced processing has been disabled due to a missing permission. Please re-setup it in settings.</string>
    <string name="eq_enable">Multimodal equalizer</string>
    <string name="eq_filter_type">Filter type</string>
    <string name="eq_filter_type_fir_minimum">FIR Minimum phase</string>
    <string name="eq_filter_type_iir_10_order">IIR 10th order</string>
    <string name="eq_filter_type_iir_12_order">IIR 12th order</string>
    <string name="eq_filter_type_iir_4_order">IIR 4th order</string>
    <string name="eq_filter_type_iir_6_order">IIR 6th order</string>
    <string name="eq_filter_type_iir_8_order">IIR 8th order</string>
    <string name="eq_interpolator">Interpolator</string>
    <string name="eq_interpolator_chip">Piecewise Cubic Hermite Interpolating Polynomial</string>
    <string name="eq_interpolator_mha">Modified Hiroshi Akima spline</string>
    <string name="eq_preset_acoustic">Acoustic</string>
    <string name="eq_preset_bass">Bass</string>
    <string name="eq_preset_beats">Beats</string>
    <string name="eq_preset_classic">Classic</string>
    <string name="eq_preset_clear">Clear</string>
    <string name="eq_preset_deep">Deep</string>
    <string name="eq_preset_dubstep">Dubstep</string>
    <string name="eq_preset_electronic">Electronic</string>
    <string name="eq_preset_flat">Flat</string>
    <string name="eq_preset_hardstyle">Hardstyle</string>
    <string name="eq_preset_hiphop">Hip-Hop</string>
    <string name="eq_preset_jazz">Jazz</string>
    <string name="eq_preset_metal">Metal</string>
    <string name="eq_preset_movie">Movie</string>
    <string name="eq_preset_pop">Pop</string>
    <string name="eq_preset_rb">R&amp;B</string>
    <string name="eq_preset_rock">Rock</string>
    <string name="eq_preset_vocal">Vocal Booster</string>
    <string name="error_projection_api_missing">Your device manufacturer has disabled the internal audio recording APIs. This application cannot work without them.</string>
    <string name="exclude_app_from_recents">Exclude app from recents</string>
    <string name="exclude_app_from_recents_off">Don\'t exclude app from recent tasks list</string>
    <string name="exclude_app_from_recents_on">Exclude app from recent tasks list</string>
    <string name="filelibrary_access_fail">Failed to access directory</string>
    <string name="filelibrary_context_delete">Delete</string>
    <string name="filelibrary_context_duplicate">Duplicate</string>
    <string name="filelibrary_context_edit">Edit</string>
    <string name="filelibrary_context_load">Load</string>
    <string name="filelibrary_context_new_preset">New</string>
    <string name="filelibrary_context_new_preset_long">New preset</string>
    <string name="filelibrary_context_overwrite">Overwrite</string>
    <string name="filelibrary_context_rename">Rename</string>
    <string name="filelibrary_context_resample">Offline resample</string>
    <string name="filelibrary_context_share">Share…</string>
    <string name="filelibrary_corrupted">The selected file contains no useable data or is corrupted.</string>
    <string name="filelibrary_corrupted_title">Unsupported or corrupted file</string>
    <string name="filelibrary_deleted">\'%1$s\' deleted</string>
    <string name="filelibrary_file_exists">File exists already</string>
    <string name="filelibrary_file_too_new">The selected file was created with a newer version of this application and cannot be loaded. Please update this app to continue.</string>
    <string name="filelibrary_hint_tap_and_hold">Tap and hold any item for more options</string>
    <string name="filelibrary_is_backup_not_preset">The selected file is a backup file, not a preset. Please go to the backup settings if you want to restore it.</string>
    <string name="filelibrary_new_file_name">New file name</string>
    <string name="filelibrary_no_file_selected">No file selected</string>
    <string name="filelibrary_no_presets">No presets saved. Tap \'Add\' to create a new one.</string>
    <string name="filelibrary_preset_created">Preset \'%1$s\' created</string>
    <string name="filelibrary_preset_load_failed">File \'%1$s\' is not compatible with this app</string>
    <string name="filelibrary_preset_loaded">Preset \'%1$s\' loaded</string>
    <string name="filelibrary_preset_overwritten">Preset \'%1$s\' overwritten</string>
    <string name="filelibrary_preset_save_failed">Failed to save preset</string>
    <string name="filelibrary_renamed">Renamed to \'%1$s\'</string>
    <string name="filelibrary_resample_complete">Resampled to %1$dHz</string>
    <string name="filelibrary_resample_failed">Resampling failed. Corrupt input file?</string>
    <string name="filelibrary_unsupported_format">The selected file has not have the correct file extension.</string>
    <string name="filelibrary_unsupported_format_title">Unsupported file type</string>
    <string name="gcm_defaultSenderId" translatable="false">1418102932</string>
    <string name="gep_add_node">Add</string>
    <string name="geq_api_network_error">Failed to connect to server. %1$s</string>
    <string name="geq_api_network_error_details_code">Server responded with error code: %1$d</string>
    <string name="geq_autoeq">AutoEQ profiles</string>
    <string name="geq_cancel">Discard</string>
    <string name="geq_cancel_spaced">  Discard </string>
    <string name="geq_delete_node">Delete node</string>
    <string name="geq_discard_changes">The values are not valid or empty. Your changes can\'t be saved.\nDo you want to discard your changes and exit?</string>
    <string name="geq_discard_changes_title">Discard changes and exit?</string>
    <string name="geq_done">Done</string>
    <string name="geq_done_spaced">  Done </string>
    <string name="geq_edit_as_string">Edit as string</string>
    <string name="geq_edit_hint">GraphicEQ nodes</string>
    <string name="geq_enable">Arbitrary response equalizer</string>
    <string name="geq_frequency">Frequency</string>
    <string name="geq_frequeny_range">Value between 1Hz and 24000Hz</string>
    <string name="geq_gain">Gain</string>
    <string name="geq_gain_range">Value between -32dB and 32dB</string>
    <string name="geq_go_back_to_node_list">Go back to node list</string>
    <string name="geq_next_node">Next node</string>
    <string name="geq_no_nodes_defined">No nodes defined</string>
    <string name="geq_node_editor">Node editor</string>
    <string name="geq_node_list">Node list</string>
    <string name="geq_nodes">Magnitude response</string>
    <string name="geq_preview">Preview</string>
    <string name="geq_preview_collapsed">Preview (tap to expand)</string>
    <string name="geq_previous_node">Previous node</string>
    <string name="geq_reset">Reset</string>
    <string name="geq_reset_confirm">Do you want to reset your GraphicEQ settings?</string>
    <string name="geq_reset_confirm_title">Are you sure?</string>
    <string name="google_api_key" translatable="false">AIzaSyAmOKROX4dY-SuRJk5vd046_6zW6kV_UL8</string>
    <string name="google_app_id" translatable="false">1:1418102932:android:e5a20ec31bf9c2b19c4c81</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAmOKROX4dY-SuRJk5vd046_6zW6kV_UL8</string>
    <string name="google_storage_bucket" translatable="false">rootlessjamesdsp.appspot.com</string>
    <string name="group_bluetooth">Bluetooth</string>
    <string name="group_hdmi">HDMI</string>
    <string name="group_speaker">Internal speaker</string>
    <string name="group_unknown">Unknown</string>
    <string name="group_usb">USB</string>
    <string name="group_wired_headphones">Wired headphones</string>
    <string name="install">Install</string>
    <string name="intent_import_error_file_uri">Failed to import file. Unsupported file URI.</string>
    <string name="intent_import_fail">Failed to import file \'%1$s\'</string>
    <string name="intent_import_irs">Import impulse response</string>
    <string name="intent_import_liveprog">Import Liveprog script</string>
    <string name="intent_import_mode_add">Import only</string>
    <string name="intent_import_mode_select">Import and activate</string>
    <string name="intent_import_preset">Import preset</string>
    <string name="intent_import_select_success">File \'%1$s\' imported and activated</string>
    <string name="intent_import_success">File \'%1$s\' imported</string>
    <string name="intent_import_vdc">Import VDC file</string>
    <string name="jamesdsp" translatable="false">JamesDSP</string>
    <string name="key_about" translatable="false">about</string>
    <string name="key_android15_screenrecord_restriction_seen" translatable="false">android15_screenrecord_restriction_seen</string>
    <string name="key_appearance" translatable="false">appearance</string>
    <string name="key_appearance_app_theme" translatable="false">appearance_app_theme</string>
    <string name="key_appearance_nav_hide" translatable="false">appearance_nav_hide</string>
    <string name="key_appearance_pure_black" translatable="false">appearance_pure_black</string>
    <string name="key_appearance_show_icons" translatable="false">appearance_show_icons</string>
    <string name="key_appearance_theme_mode" translatable="false">appearance_theme_mode</string>
    <string name="key_audio_format" translatable="false">audio_format</string>
    <string name="key_audioformat_buffersize" translatable="false">audioformat_buffersize</string>
    <string name="key_audioformat_encoding" translatable="false">audioformat_encoding</string>
    <string name="key_audioformat_enhanced_processing" translatable="false">audioformat_enhanced_processing</string>
    <string name="key_audioformat_enhanced_processing_info" translatable="false">audioformat_enhanced_processing_info</string>
    <string name="key_audioformat_optimization_benchmark" translatable="false">audioformat_optimization_benchmark</string>
    <string name="key_audioformat_optimization_refresh" translatable="false">audioformat_optimization_refresh</string>
    <string name="key_audioformat_processing" translatable="false">audioformat_processing</string>
    <string name="key_autostart_prompt_at_boot" translatable="false">autostart_prompt_at_boot</string>
    <string name="key_backup" translatable="false">backup</string>
    <string name="key_backup_create" translatable="false">backup_create</string>
    <string name="key_backup_frequency" translatable="false">backup_frequency</string>
    <string name="key_backup_location" translatable="false">backup_location</string>
    <string name="key_backup_maximum" translatable="false">backup_maximum</string>
    <string name="key_backup_restore" translatable="false">backup_restore</string>
    <string name="key_bass_enable" translatable="false">bass_enable</string>
    <string name="key_bass_max_gain" translatable="false">bass_max_gain</string>
    <string name="key_benchmark_c0" translatable="false">benchmark_c0</string>
    <string name="key_benchmark_c1" translatable="false">benchmark_c1</string>
    <string name="key_compander_enable" translatable="false">compander_enable</string>
    <string name="key_compander_granularity" translatable="false">compander_granularity</string>
    <string name="key_compander_response" translatable="false">compander_response</string>
    <string name="key_compander_tftransforms" translatable="false">compander_tftransforms</string>
    <string name="key_compander_timeconstant" translatable="false">compander_timeconstant</string>
    <string name="key_convolver_adv_imp" translatable="false">convolver_adv_imp</string>
    <string name="key_convolver_enable" translatable="false">convolver_enable</string>
    <string name="key_convolver_file" translatable="false">convolver_file</string>
    <string name="key_convolver_mode" translatable="false">convolver_mode</string>
    <string name="key_credits_build_info" translatable="false">credits_build_info</string>
    <string name="key_credits_check_update" translatable="false">credits_check_update</string>
    <string name="key_credits_google_play" translatable="false">credits_google_play</string>
    <string name="key_credits_version" translatable="false">credits_version</string>
    <string name="key_crossfeed_enable" translatable="false">bs2b_crossfeed_enable</string>
    <string name="key_crossfeed_mode" translatable="false">bs2b_crossfeed_mode</string>
    <string name="key_ddc_enable" translatable="false">ddc_enable</string>
    <string name="key_ddc_file" translatable="false">ddc_file</string>
    <string name="key_debug_database" translatable="false">debug_database</string>
    <string name="key_device_profiles" translatable="false">device_profiles</string>
    <string name="key_device_profiles_enable" translatable="false">device_profiles_enable</string>
    <string name="key_device_profiles_info" translatable="false">device_profiles_info</string>
    <string name="key_editor_font_size" translatable="false">editor_font_size</string>
    <string name="key_eq_bands" translatable="false">eq_bands</string>
    <string name="key_eq_enable" translatable="false">eq_enable</string>
    <string name="key_eq_filter_type" translatable="false">eq_filter_type</string>
    <string name="key_eq_interpolation" translatable="false">eq_interpolation</string>
    <string name="key_exclude_app_from_recents" translatable="false">exclude_app_from_recents</string>
    <string name="key_first_boot" translatable="false">first_boot</string>
    <string name="key_geq_enable" translatable="false">geq_enable</string>
    <string name="key_geq_nodes" translatable="false">geq_nodes</string>
    <string name="key_is_activity_active" translatable="false">is_activity_active</string>
    <string name="key_is_app_compat_activity_active" translatable="false">is_app_compat_activity_active</string>
    <string name="key_limiter_release" translatable="false">limiter_release</string>
    <string name="key_limiter_threshold" translatable="false">limiter_threshold</string>
    <string name="key_liveprog_edit" translatable="false">liveprog_edit</string>
    <string name="key_liveprog_enable" translatable="false">liveprog_enable</string>
    <string name="key_liveprog_file" translatable="false">liveprog_file</string>
    <string name="key_liveprog_params" translatable="false">liveprog_params</string>
    <string name="key_misc" translatable="false">misc</string>
    <string name="key_misc_permission_auto_start" translatable="false">misc_permission_auto_start</string>
    <string name="key_misc_permission_restart_setup" translatable="false">misc_permission_restart_setup</string>
    <string name="key_misc_permission_skip_prompt" translatable="false">misc_permission_skip_prompt</string>
    <string name="key_network_autoeq_api_url" translatable="false">network_autoeq_api_url</string>
    <string name="key_output_postgain" translatable="false">output_postgain</string>
    <string name="key_powered_on" translatable="false">powered_on</string>
    <string name="key_powersave_suspend" translatable="false">powersave_suspend</string>
    <string name="key_profile_active" translatable="false">profile_active</string>
    <string name="key_reset_proc_mode_fix_applied" translatable="false">reset_proc_mode_fix_applied</string>
    <string name="key_reverb_enable" translatable="false">reverb_enable</string>
    <string name="key_reverb_preset" translatable="false">reverb_preset</string>
    <string name="key_session_app_problem_ignore" translatable="false">session_app_problem_ignore</string>
    <string name="key_session_continuous_polling" translatable="false">session_continuous_polling</string>
    <string name="key_session_continuous_polling_rate" translatable="false">session_continuous_polling_rate</string>
    <string name="key_session_detection_method" translatable="false">session_detection_method</string>
    <string name="key_session_exclude_restricted" translatable="false">session_exclude_restricted</string>
    <string name="key_session_loss_ignore" translatable="false">session_loss_ignore</string>
    <string name="key_share_crash_reports" translatable="false">share_crash_reports</string>
    <string name="key_snooze_translation_notice" translatable="false">snooze_translation_notice</string>
    <string name="key_stereowide_enable" translatable="false">stereowide_enable</string>
    <string name="key_stereowide_mode" translatable="false">stereowide_mode</string>
    <string name="key_translators" translatable="false">translators</string>
    <string name="key_troubleshooting" translatable="false">troubleshooting</string>
    <string name="key_troubleshooting_dump" translatable="false">troubleshooting_dump</string>
    <string name="key_troubleshooting_notification_access" translatable="false">troubleshooting_notification_access</string>
    <string name="key_troubleshooting_repair_assets" translatable="false">troubleshooting_repair_assets</string>
    <string name="key_troubleshooting_view_limitations" translatable="false">troubleshooting_view_limitations</string>
    <string name="key_tube_drive" translatable="false">tube_drive</string>
    <string name="key_tube_enable" translatable="false">tube_enable</string>
    <string name="key_update_check_skip" translatable="false">update_check_skip</string>
    <string name="key_update_check_timeout" translatable="false">update_check_timeout</string>
    <string name="limit_detect_delay"><![CDATA[After an app has opened a new media session to playback audio, you may hear duplicated or cut-off audio for a very short period (about <250ms).]]></string>
    <string name="limit_detect_delay_title">Audio session detection delay</string>
    <string name="limit_hw_accel">HW-accelerated audio playback (fast tracks) may sometimes cause duplicated audio or kill JamesDSP in some circumstances. Please add the affected music player to the excluded apps list to prevent JamesDSP from re-routing it.</string>
    <string name="limit_hw_accel_title">HW-accelerated audio playback issues</string>
    <string name="limit_latency">This app may add some delay to the audio stream. You can add movie/video apps to the exclusion list to prevent AV-desync by not processing them.</string>
    <string name="limit_latency_title">Increased latency</string>
    <string name="limit_session_control_conflict">Audio effect apps using Android\'s built-in Dynamic Processing effect interfere with this app.</string>
    <string name="limit_session_control_conflict_title">Disable other audio effect apps</string>
    <string name="limit_tested_devices">Changes made to Android\'s audio subsystem by your device manufacturer can easily break this app because it uses a few undocumented APIs.\nIt has been tested on these devices:\n• Samsung S20+ (Android 12)\n• AOSP emulator (Android 10–13)\n• Google Pixel 6 Pro (Android 13)</string>
    <string name="limit_tested_devices_title">Tested devices</string>
    <string name="limit_unsupported_apps">Apps blocking internal audio capture remain unprocessed (e.g., Spotify, Google Chrome).</string>
    <string name="limit_unsupported_apps_title">Unsupported media apps</string>
    <string name="liveprog_additional_params">Additional script parameters</string>
    <string name="liveprog_additional_params_not_supported">Selected script has no customizable parameters</string>
    <string name="liveprog_edit">Open active script in built-in editor</string>
    <string name="liveprog_edit_header">Edit selected script</string>
    <string name="liveprog_enable">Live programmable DSP</string>
    <string name="liveprog_file">Liveprog script</string>
    <string name="liveprog_no_script_selected">No script selected</string>
    <string name="liveprog_not_found">Script not found</string>
    <string name="load_fail_arch_card">If you already installed the magisk module, you may have chosen the wrong settings during installation. Please re-install and try to enable Huawei compatibility (even if your phone is not from that manufacturer).</string>
    <string name="load_fail_arch_card_title">Module already installed?</string>
    <string name="load_fail_card">Please install the JamesDSP magisk module and reboot your Android device to use this application. You may also need to reinstall this app after rebooting.</string>
    <string name="load_fail_card_title">Failed to load libjamesdsp.so</string>
    <string name="load_fail_header">Magisk module not installed</string>
    <string name="load_fail_rootless_card">This flavor of JamesDSP was designed to be used with a rooted/modified device. A non-root alternative with some limitations is also available. Tap here for details.</string>
    <string name="load_fail_rootless_card_title">Looking for a rootless alternative?</string>
    <string name="message_convolver_advimp_invalid">Convolver: Advanced waveform editing contains invalid values.</string>
    <string name="message_irs_corrupt">Selected impulse response is corrupt. Please choose another one.</string>
    <string name="message_liveprog_compile_fail">Liveprog execution failed. Selected script is damaged. Please use the script editor for debugging.</string>
    <string name="message_vdc_corrupt">Selected VDC file is corrupt. Please choose another one.</string>
    <string name="misc_permission_auto_start">Auto-start effect engine on boot</string>
    <string name="misc_permission_header">Optional permissions</string>
    <string name="misc_permission_restart_setup">Restart setup wizard</string>
    <string name="misc_permission_restart_setup_summary">Grant missing optional permissions</string>
    <string name="misc_permission_skip_prompt">Skip capture permission prompt</string>
    <string name="misc_settings">Miscellaneous</string>
    <string name="misc_settings_summary">Crash reports, other actions</string>
    <string name="network_autoeq_api_url">AutoEQ API backend URL</string>
    <string name="network_autoeq_conntest_done">Connection test succeeded</string>
    <string name="network_autoeq_conntest_fail">Connection test failed</string>
    <string name="network_autoeq_conntest_fail_summary">Server did not respond correctly. Make sure the software at https://github.com/ThePBone/AutoEqApi is correctly set up on the server.\n\nNetwork error: %1$s\n\nDo you want to restore this setting to the default value?</string>
    <string name="network_autoeq_conntest_running">Testing connection to server…</string>
    <string name="network_invalid_url">Invalid URL format</string>
    <string name="network_services">Network services</string>
    <string name="never_show_warning_again">Never show this warning again</string>
    <string name="no">No</string>
    <string name="no_activity_found">No Activity found to handle action</string>
    <string name="notification_channel_app_compat_alert">Incompatible app alert</string>
    <string name="notification_channel_backup_complete">Complete</string>
    <string name="notification_channel_backup_progress">In progress</string>
    <string name="notification_channel_permission_prompt">Permission prompt request</string>
    <string name="notification_channel_service">Service notification</string>
    <string name="notification_channel_session_loss_alert">Session loss alert</string>
    <string name="notification_group_backup">Backup and restore</string>
    <string name="notification_group_service">Service</string>
    <string name="notification_idle">Audio processing idle</string>
    <string name="notification_processing">Active apps: %1$s</string>
    <string name="notification_processing_disabled_title">Audio processing disabled</string>
    <string name="notification_processing_legacy">Tap to open JamesDSP manager</string>
    <string name="notification_processing_title">Audio processing active</string>
    <string name="notification_request_permission">Tap to grant capture permission and enable JamesDSP\'s audio processing</string>
    <string name="notification_request_permission_title">User action is required to launch JamesDSP</string>
    <string name="number_box_decrement">Decrement</string>
    <string name="number_box_increment">Increment</string>
    <string name="onboarding_adb_adb_title">ADB setup</string>
    <string name="onboarding_adb_caption">This app requires special permissions for dumping your device\'s audio policy. Please follow the instructions below.</string>
    <string name="onboarding_adb_dump_permission_not_granted">Please follow the instructions first and try again. The DUMP permission has not been granted.</string>
    <string name="onboarding_adb_manual_step1">Go to Android\'s hidden development settings and enable USB debugging. You need to unlock these settings by tapping the build number in the system settings several times.</string>
    <string name="onboarding_adb_manual_step1_button">Open development settings</string>
    <string name="onboarding_adb_manual_step2">Open https://app.webadb.com in your computer\'s browser (works best in Chrome) and connect your device using USB to the computer.</string>
    <string name="onboarding_adb_manual_step3">Add and connect the USB device in WebADB.</string>
    <string name="onboarding_adb_manual_step4">Navigate to \'Interactive Shell\' and execute the following command in the shell: \'pm grant %s android.permission.DUMP\'</string>
    <string name="onboarding_adb_manual_step5">Tap \'Next\' in this app to continue.</string>
    <string name="onboarding_adb_manual_step5b_required">Execute the following command to grant permanent permission to project audio: \'appops set %s PROJECT_MEDIA allow\'</string>
    <string name="onboarding_adb_manual_step5c">(Optional) In addition to the previous step, you can also enable auto-start by executing the following command: \'appops set %s SYSTEM_ALERT_WINDOW allow\'</string>
    <string name="onboarding_adb_not_granted_title">Permission not yet granted</string>
    <string name="onboarding_adb_project_media_not_granted">Please follow the instructions first and try again. The PROJECT_MEDIA permission has not been granted.</string>
    <string name="onboarding_adb_shizuku_grant_button">Grant access</string>
    <string name="onboarding_adb_shizuku_grant_fail_denied">Please grant this application access to Shizuku. You may need to open Shizuku\'s permission manager and grant the permission manually if you previously denied it and the popup no longer shows.</string>
    <string name="onboarding_adb_shizuku_grant_fail_denied_title">Permission denied</string>
    <string name="onboarding_adb_shizuku_grant_fail_server_dead">Please start the Shizuku server before requesting permission. Go back to instruction step 2.</string>
    <string name="onboarding_adb_shizuku_grant_fail_server_dead_title">Shizuku server not running</string>
    <string name="onboarding_adb_shizuku_grant_fail_version">This application requires Shizuku version 11 or later. Please update Shizuku to the latest version.</string>
    <string name="onboarding_adb_shizuku_grant_fail_version_title">Unsupported Shizuku version</string>
    <string name="onboarding_adb_shizuku_grant_instruction">Tap the button and grant this app access to Shizuku.</string>
    <string name="onboarding_adb_shizuku_install_button">Install Shizuku</string>
    <string name="onboarding_adb_shizuku_install_button_done">Already installed</string>
    <string name="onboarding_adb_shizuku_install_instruction">Install \'Shizuku\' from Google Play.</string>
    <string name="onboarding_adb_shizuku_no_dump_perm">Please wait a few seconds and try again. If it still does not work, try the alternative method.</string>
    <string name="onboarding_adb_shizuku_no_dump_perm_title">Shizuku failed to grant ADB permissions</string>
    <string name="onboarding_adb_shizuku_not_installed">Shizuku is not yet installed. Please download and install it from Google Play first.</string>
    <string name="onboarding_adb_shizuku_not_installed_title">Shizuku is not yet installed</string>
    <string name="onboarding_adb_shizuku_open_button">Launch Shizuku</string>
    <string name="onboarding_adb_shizuku_open_button_done">Already active</string>
    <string name="onboarding_adb_shizuku_open_instruction">Launch \'Shizuku\' and look for \'<b>Start via Wireless Debugging</b>\'. Tap the \'Pairing\' button and follow the in-app instructions. After pairing Shizuku with your device, start the Shizuku server by pressing the \'Start\' button in their app. If you run into problems while setting up Shizuku, please refer to their setup tutorial linked in their app.</string>
    <string name="onboarding_adb_shizuku_title">Shizuku setup</string>
    <string name="onboarding_back">Back</string>
    <string name="onboarding_caption">This assistant will help you to set-up this app.</string>
    <string name="onboarding_finish">Finish</string>
    <string name="onboarding_finish_caption">If you encounter any issues, please visit the troubleshooting options.</string>
    <string name="onboarding_finish_header">JamesDSP is set-up!</string>
    <string name="onboarding_fix_permissions">One or more permissions were revoked by you or the system.\nIn order to ensure that this app behaves correctly, you need to grant these permissions again.\n\nPlease redo the setup wizard to re-grant these permissions.</string>
    <string name="onboarding_fix_permissions_title">User action required</string>
    <string name="onboarding_greeting">Welcome to RootlessJamesDSP!</string>
    <string name="onboarding_limitations_title">Limitations</string>
    <string name="onboarding_limitations_unstable">Important! Please read the notes carefully before continuing.</string>
    <string name="onboarding_methods_adb_title">ADB</string>
    <string name="onboarding_methods_root_adb">\u25CF For advanced users\n\u25CF Computer and USB cable required\n\u25CFAll Android versions</string>
    <string name="onboarding_methods_root_root">\u25CF Recommended\n\u25CF No external computer required\n\u25CF All Android versions</string>
    <string name="onboarding_methods_root_shizuku">\u25CF Recommended, if root is not set-up\n\u25CF No external computer required\n\u25CF Android 11+ only</string>
    <string name="onboarding_methods_root_title">Root</string>
    <string name="onboarding_methods_rootless_adb">\u25CF For advanced users\n\u25CF Computer and USB cable required\n\u25CF Android 10+ supported</string>
    <string name="onboarding_methods_rootless_shizuku">\u25CF Recommended\n\u25CF No external computer required\n\u25CF Android 11+ only</string>
    <string name="onboarding_methods_shizuku_title">Shizuku</string>
    <string name="onboarding_methods_title">Choose setup method</string>
    <string name="onboarding_methods_unsupported_append">incompatible</string>
    <string name="onboarding_next">Next</string>
    <string name="onboarding_perm_caption">Additional permissions are required to use this application. Please review them and tap \'Next\' to grant them.</string>
    <string name="onboarding_perm_cast_caption">You need to explicitly grant this app permission to record audio content every time it launches.</string>
    <string name="onboarding_perm_cast_title">Cast/recording permission</string>
    <string name="onboarding_perm_diag">Allow sending diagnostic reports</string>
    <string name="onboarding_perm_diag_caption">This app submits crash reports and other diagnostic data, including information about other active applications, to allow me to detect and solve compatibility issues and bugs easier.</string>
    <string name="onboarding_perm_microphone_caption">Required to record internal audio. The hardware microphone is not activated.</string>
    <string name="onboarding_perm_microphone_title">Audio recording permission</string>
    <string name="onboarding_perm_missing">Please grant all requested runtime permissions to continue.\nIf you repeatedly denied the permissions, you need to grant them manually in the system settings.</string>
    <string name="onboarding_perm_missing_title">Runtime permissions not granted</string>
    <string name="onboarding_perm_notification_caption">Required to receive important status updates and information about JamesDSP\'s processing state.</string>
    <string name="onboarding_perm_notification_title">Notification permission</string>
    <string name="onboarding_perm_title">Other permissions</string>
    <string name="onboarding_root_enhanced_processing_setup_success">Enhanced processing is set-up</string>
    <string name="onboarding_root_shell_fail">No root access available. Please use another setup method.</string>
    <string name="onboarding_root_shell_fail_title">No root access</string>
    <string name="onboarding_root_shell_fail_unknown">Failed to grant permission via root. Please use another setup method.</string>
    <string name="onboarding_warning">Currently, this application is unfinished and unstable. Please note that it may not work on some devices as expected since it uses Android APIs not designed for use by third parties.</string>
    <string name="open">Open</string>
    <string name="output_control_header">Output control</string>
    <string name="output_control_postgain">Post gain</string>
    <string name="output_limiter_release">Limiter release</string>
    <string name="output_limiter_threshold">Limiter threshold</string>
    <string name="paste">Paste</string>
    <string name="permission_allowed">Allowed</string>
    <string name="permission_not_allowed">Not allowed</string>
    <string name="power_button_alt">Toggle audio processing</string>
    <string name="powersave">Power-saving</string>
    <string name="powersave_suspend">Suspend audio pipeline while idle</string>
    <string name="powersave_suspend_off">Keep processing even when no content is playing</string>
    <string name="powersave_suspend_on">Pause audio pipeline to save power</string>
    <string name="preparing">Preparing…</string>
    <string name="privacy">Privacy</string>
    <string name="privacy_share_crash_reports">Share application crash reports</string>
    <string name="privacy_share_crash_reports_off">Don\'t send crash reports to the developer</string>
    <string name="privacy_share_crash_reports_on">Send crash reports to the developer</string>
    <string name="profiles_enable">Per-device profiles</string>
    <string name="profiles_enable_summary_off">Per-device profiles disabled</string>
    <string name="profiles_enable_summary_on">Per-device profiles enabled</string>
    <string name="profiles_info">Tap to view documentation</string>
    <string name="profiles_info_content">While this setting is enabled, each audio device is assigned a separate audio configuration. This app will automatically switch between device profiles when the output device changes. \n\nEach new device that is connected starts with a blank profile. You can then configure it or copy settings from another device profile.\n\nNote: Presets are only applied to the active profile while this feature is enabled. Inactive profiles remain untouched.</string>
    <string name="profiles_info_title">What are per-device profiles?</string>
    <string name="profiles_manage_hint">Tap the \'Active device profile\' button on the main screen to copy or delete profiles.</string>
    <string name="profiles_section_header">Device profiles</string>
    <string name="profiles_summary">Per-device profiles, automatic switching</string>
    <string name="project_id" translatable="false">rootlessjamesdsp</string>
    <string name="redo">Redo</string>
    <string name="reverb_enable">Virtual room effect</string>
    <string name="reverb_preset_default">Default</string>
    <string name="reverb_preset_large_hall1">Large hall</string>
    <string name="reverb_preset_large_room1">Large room</string>
    <string name="reverb_preset_long_reverb1">Long reverb 1</string>
    <string name="reverb_preset_long_reverb2">Long reverb 2</string>
    <string name="reverb_preset_medium_hall2">Medium hall</string>
    <string name="reverb_preset_medium_room1">Medium room</string>
    <string name="reverb_preset_plate_high">Plate high</string>
    <string name="reverb_preset_plate_low">Plate low</string>
    <string name="reverb_preset_small_hall1">Small hall 1</string>
    <string name="reverb_preset_small_hall2">Small hall 2</string>
    <string name="reverb_preset_small_room1">Small room 1</string>
    <string name="reverb_preset_small_room2">Small room 2</string>
    <string name="reverb_room_type">Room type</string>
    <string name="revert_confirmation">Do you want to reset your current JamesDSP preset to the default values?</string>
    <string name="revert_confirmation_title">Are you sure?</string>
    <string name="self_update_download_fail">Failed to download update package. Please check your internet connection. Details: %1$s</string>
    <string name="self_update_finished">Update installed. The app is now restarting…</string>
    <string name="self_update_install_error">Update failed</string>
    <string name="self_update_install_fail">Installation failed. %1$s</string>
    <string name="self_update_no_updates">No new updates available at the moment</string>
    <string name="self_update_notice">New update available (%1$s)</string>
    <string name="self_update_notice_dismiss_install">Install now</string>
    <string name="self_update_notice_dismiss_skip">Skip this update</string>
    <string name="self_update_notice_dismiss_snooze">Remind me later</string>
    <string name="self_update_notice_summary">Tap here to download and install the new update.</string>
    <string name="self_update_state_downloading">Downloading…</string>
    <string name="self_update_state_installing">Installing…</string>
    <string name="session_app_compat_notification">Tap this notification to resolve the problem.</string>
    <string name="session_app_compat_notification_title">Audio processing stopped. Another app is interfering with JamesDSP.</string>
    <string name="session_app_compat_toast">JamesDSP stopped due to incompatible media app. Please check the notification to resolve this problem.</string>
    <string name="session_app_problem_ignore">Ignore app compatibility issues</string>
    <string name="session_continuous_polling">Force continuous polling</string>
    <string name="session_continuous_polling_off">Only listen for session events from the system</string>
    <string name="session_continuous_polling_on">Continuously enumerate audio sessions</string>
    <string name="session_continuous_polling_rate">Polling interval (ms)</string>
    <string name="session_control_loss_notification">Please disable all other apps that provide global audio effects before re-enabling JamesDSP.</string>
    <string name="session_control_loss_notification_title">Audio processing stopped. Another app is interfering with JamesDSP.</string>
    <string name="session_control_loss_toast">JamesDSP stopped due to audio session control loss. Please disable other audio effect apps.</string>
    <string name="session_detection_header">Audio session detection</string>
    <string name="session_detection_method">Preferred session detection method</string>
    <string name="session_detection_method_audiopolicyservice">AudioPolicyService dump</string>
    <string name="session_detection_method_audioservice">AudioService dump (recommended)</string>
    <string name="session_exclude_restricted">Exclude unsupported apps</string>
    <string name="session_exclude_restricted_off">Apps blocking audio capture stay muted (default Android behavior)</string>
    <string name="session_exclude_restricted_on">Apps blocking audio capture are excluded and remain unprocessed (recommended)</string>
    <string name="session_loss_ignore">Ignore session control loss</string>
    <string name="session_loss_ignore_off">Disabled, handle normally (recommended)</string>
    <string name="session_loss_ignore_on">Don\'t take any action (may cause duplicated audio)</string>
    <string name="session_loss_ignore_warning">Enabling this setting can cause duplicated audio when another audio effects app takes control of an audio session.\nPlease keep this setting disabled unless you encounter false-positive session loss events.</string>
    <string name="slider_dialog_format_error">Invalid number format. Changes not applied.</string>
    <string name="slider_dialog_step_error">Value must be a multiple of %1$d. Changes not applied.</string>
    <string name="slider_dialog_title">Edit value</string>
    <string name="stereowide_enable">Soundstage wideness</string>
    <string name="stereowide_level">Wideness</string>
    <string name="stereowide_level_narrow">Narrow</string>
    <string name="stereowide_level_none">None</string>
    <string name="stereowide_level_very_narrow">Very narrow</string>
    <string name="stereowide_level_very_wide">Very wide</string>
    <string name="stereowide_level_wide">Wide</string>
    <string name="success">Success</string>
    <string name="theme_default">Default</string>
    <string name="theme_greenapple">Green Apple</string>
    <string name="theme_honey">Honey</string>
    <string name="theme_monet">Dynamic</string>
    <string name="theme_strawberrydaiquiri">Strawberry Daiquiri</string>
    <string name="theme_tealturquoise">Teal &amp; Turquoise</string>
    <string name="theme_tidalwave">Tidal Wave</string>
    <string name="theme_yinyang">Yin &amp; Yang</string>
    <string name="theme_yotsuba">Yotsuba</string>
    <string name="title_activity_app_compat">Compatibility issue</string>
    <string name="title_activity_blocklist">Excluded apps</string>
    <string name="title_activity_geq">Magnitude response</string>
    <string name="title_activity_liveprog_editor">Script editor</string>
    <string name="title_activity_liveprog_params">Customizable script parameters</string>
    <string name="title_activity_onboarding">Onboarding</string>
    <string name="title_activity_settings">Settings</string>
    <string name="translation_notice">Contribute translations</string>
    <string name="translation_notice_summary">Please help us to translate this app into your language! Tap here to visit the project site on Crowdin.</string>
    <string name="translators">Translators</string>
    <string name="troubleshooting">Troubleshooting</string>
    <string name="troubleshooting_actions">Miscellaneous actions</string>
    <string name="troubleshooting_docs">Documentation</string>
    <string name="troubleshooting_dump">Export audio session dump…</string>
    <string name="troubleshooting_dump_share_title">Share audio session dump..</string>
    <string name="troubleshooting_dump_summary">Collect and save troubleshooting data</string>
    <string name="troubleshooting_notification_access">Allow notification access for better session detection</string>
    <string name="troubleshooting_notification_access_summary">Enables more reliable audio session detection</string>
    <string name="troubleshooting_repair_assets">Restore bundled IRS, DDC, and scripts</string>
    <string name="troubleshooting_repair_assets_success">Bundled assets have been restored</string>
    <string name="troubleshooting_repair_assets_summary">Unpack preloaded assets if you lost them</string>
    <string name="troubleshooting_summary">Advanced options, documentation, diagnostics</string>
    <string name="troubleshooting_view_limitations">Technical limitations</string>
    <string name="troubleshooting_view_limitations_summary">Show details about the limitations of this app</string>
    <string name="tube_drive">Preamp</string>
    <string name="tube_enable">Analog modelling</string>
    <string name="tutorial">Tutorial</string>
    <string name="undo">Undo</string>
    <string name="unknown_error">Unknown error</string>
    <string name="update">Update</string>
    <string name="value_not_set">Not set</string>
    <string name="version_mismatch_root">JamesDSP magisk package requires an update</string>
    <string name="version_mismatch_root_description">The JamesDSP magisk package installed on your device is outdated and not supported by this app anymore.\n\nPlease retrieve the latest JamesDSP magisk package ZIP and install it via Magisk.\nNote that the magisk package may replace this app with the official JamesDSP app.\n\nDo you want to visit the download website for the Magisk packages now?</string>
    <string name="version_mismatch_root_toast">Failed to start. The installed JamesDSP magisk package is too old and not supported by this app anymore.</string>
    <string name="warning">Warning</string>
    <string name="yes">Yes</string>
    <style name="AppTheme.AlertDialogTheme" parent="@style/ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="dialogCornerRadius">28dp</item>
        <item name="colorBackgroundFloating">?attr/colorSurface</item>
    </style>
    <style name="Base.Theme.RootlessJamesDSP" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="android:forceDarkAllowed" ns1:ignore="ObsoleteSdkInt" ns1:targetApi="Q">false</item>

        <item name="android:divider">@color/divider_default</item>
        <item name="android:statusBarColor">@android:color/transparent</item>

        
        

        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="alertDialogTheme">@style/AppTheme.AlertDialogTheme</item>
        <item name="materialCardViewStyle">@style/Theme.RootlessJamesDSP.CardView</item>
        <item name="cardCornerRadius">@dimen/corner_radius</item>
    </style>
    <style name="Base.Theme.RootlessJamesDSP.Dialog" parent="Theme.Material3.DayNight.Dialog">
        
        <item name="android:forceDarkAllowed" ns1:ignore="ObsoleteSdkInt" ns1:targetApi="Q">false</item>

        <item name="android:divider">@color/divider_default</item>

        <item name="windowActionBar">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="windowNoTitle">true</item>

        <item name="materialSwitchStyle">@style/Widget.Material3.CompoundButton.MaterialSwitch</item>
        <item name="alertDialogTheme">@style/AppTheme.AlertDialogTheme</item>
        <item name="cardCornerRadius">@dimen/corner_radius</item>
    </style>
    <style name="Theme.RootlessJamesDSP" parent="Base.Theme.RootlessJamesDSP"/>
    <style name="Theme.RootlessJamesDSP.CardView" parent="Widget.Material3.CardView.Elevated"/>
    <style name="Theme.RootlessJamesDSP.Dialog" parent="Base.Theme.RootlessJamesDSP.Dialog"/>
    <style name="Theme.RootlessJamesDSP.GreenApple">
        
        <item name="colorPrimary">@color/greenapple_primary</item>
        <item name="colorOnPrimary">@color/greenapple_onPrimary</item>
        <item name="colorPrimaryContainer">@color/greenapple_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/greenapple_onPrimaryContainer</item>
        <item name="colorSecondary">@color/greenapple_secondary</item>
        <item name="colorOnSecondary">@color/greenapple_onSecondary</item>
        <item name="colorSecondaryContainer">@color/greenapple_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/greenapple_onSecondaryContainer</item>
        <item name="colorTertiary">@color/greenapple_tertiary</item>
        <item name="colorOnTertiary">@color/greenapple_onTertiary</item>
        <item name="colorTertiaryContainer">@color/greenapple_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/greenapple_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/greenapple_background</item>
        <item name="colorOnBackground">@color/greenapple_onBackground</item>
        <item name="colorSurface">@color/greenapple_surface</item>
        <item name="colorOnSurface">@color/greenapple_onSurface</item>
        <item name="colorSurfaceVariant">@color/greenapple_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/greenapple_onSurfaceVariant</item>
        <item name="colorOutline">@color/greenapple_outline</item>
        <item name="colorOnSurfaceInverse">@color/greenapple_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/greenapple_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/greenapple_primaryInverse</item>
    </style>
    <style name="Theme.RootlessJamesDSP.Honey">
        
        <item name="colorPrimary">@color/honey_primary</item>
        <item name="colorOnPrimary">@color/honey_onPrimary</item>
        <item name="colorPrimaryContainer">@color/honey_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/honey_onPrimaryContainer</item>
        <item name="colorSecondary">@color/honey_secondary</item>
        <item name="colorOnSecondary">@color/honey_onSecondary</item>
        <item name="colorSecondaryContainer">@color/honey_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/honey_onSecondaryContainer</item>
        <item name="colorTertiary">@color/honey_tertiary</item>
        <item name="colorOnTertiary">@color/honey_onTertiary</item>
        <item name="colorTertiaryContainer">@color/honey_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/honey_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/honey_background</item>
        <item name="colorOnBackground">@color/honey_onBackground</item>
        <item name="colorSurface">@color/honey_surface</item>
        <item name="colorOnSurface">@color/honey_onSurface</item>
        <item name="colorSurfaceVariant">@color/honey_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/honey_onSurfaceVariant</item>
        <item name="colorOutline">@color/honey_outline</item>
        <item name="colorOnSurfaceInverse">@color/honey_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/honey_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/honey_primaryInverse</item>
        <item name="elevationOverlayColor">@color/honey_elevationOverlay</item>
    </style>
    <style name="Theme.RootlessJamesDSP.Monet"/>
    <style name="Theme.RootlessJamesDSP.SearchButton" parent="Widget.AppCompat.ImageButton">
        <item name="android:minWidth" ns1:ignore="PrivateResource">@dimen/mtrl_min_touch_target_size</item>
        <item name="android:minHeight" ns1:ignore="PrivateResource">@dimen/mtrl_min_touch_target_size</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
    </style>
    <style name="Theme.RootlessJamesDSP.StrawberryDaiquiri">
        
        <item name="colorPrimary">@color/strawberry_primary</item>
        <item name="colorOnPrimary">@color/strawberry_onPrimary</item>
        <item name="colorPrimaryContainer">@color/strawberry_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/strawberry_onPrimaryContainer</item>
        <item name="colorSecondary">@color/strawberry_secondary</item>
        <item name="colorOnSecondary">@color/strawberry_onSecondary</item>
        <item name="colorSecondaryContainer">@color/strawberry_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/strawberry_onSecondaryContainer</item>
        <item name="colorTertiary">@color/strawberry_tertiary</item>
        <item name="colorOnTertiary">@color/strawberry_onTertiary</item>
        <item name="colorTertiaryContainer">@color/strawberry_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/strawberry_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/strawberry_background</item>
        <item name="colorOnBackground">@color/strawberry_onBackground</item>
        <item name="colorSurface">@color/strawberry_surface</item>
        <item name="colorOnSurface">@color/strawberry_onSurface</item>
        <item name="colorSurfaceVariant">@color/strawberry_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/strawberry_onSurfaceVariant</item>
        <item name="colorOutline">@color/strawberry_outline</item>
        <item name="colorOnSurfaceInverse">@color/strawberry_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/strawberry_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/strawberry_primaryInverse</item>
    </style>
    <style name="Theme.RootlessJamesDSP.TealTurquoise">
        
        <item name="colorPrimary">@color/tealturquoise_primary</item>
        <item name="colorOnPrimary">@color/tealturquoise_onPrimary</item>
        <item name="colorPrimaryContainer">@color/tealturquoise_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/tealturquoise_onPrimaryContainer</item>
        <item name="colorSecondary">@color/tealturquoise_secondary</item>
        <item name="colorOnSecondary">@color/tealturquoise_onSecondary</item>
        <item name="colorSecondaryContainer">@color/tealturquoise_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/tealturquoise_onSecondaryContainer</item>
        <item name="colorTertiary">@color/tealturquoise_tertiary</item>
        <item name="colorOnTertiary">@color/tealturquoise_onTertiary</item>
        <item name="colorTertiaryContainer">@color/tealturquoise_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/tealturquoise_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/tealturquoise_background</item>
        <item name="colorOnBackground">@color/tealturquoise_onBackground</item>
        <item name="colorSurface">@color/tealturquoise_surface</item>
        <item name="colorOnSurface">@color/tealturquoise_onSurface</item>
        <item name="colorSurfaceVariant">@color/tealturquoise_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/tealturquoise_onSurfaceVariant</item>
        <item name="colorOutline">@color/tealturquoise_outline</item>
        <item name="colorOnSurfaceInverse">@color/tealturquoise_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/tealturquoise_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/tealturquoise_primaryInverse</item>
        <item name="colorError">@color/tealturquoise_tertiary</item>
        <item name="colorOnError">@color/tealturquoise_onTertiary</item>
        <item name="elevationOverlayColor">@color/tealturquoise_elevationOverlay</item>
    </style>
    <style name="Theme.RootlessJamesDSP.TidalWave">
        
        <item name="colorPrimary">@color/tidalwave_primary</item>
        <item name="colorOnPrimary">@color/tidalwave_onPrimary</item>
        <item name="colorPrimaryContainer">@color/tidalwave_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/tidalwave_onPrimaryContainer</item>
        <item name="colorSecondary">@color/tidalwave_secondary</item>
        <item name="colorOnSecondary">@color/tidalwave_onSecondary</item>
        <item name="colorSecondaryContainer">@color/tidalwave_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/tidalwave_onSecondaryContainer</item>
        <item name="colorTertiary">@color/tidalwave_tertiary</item>
        <item name="colorOnTertiary">@color/tidalwave_onTertiary</item>
        <item name="colorTertiaryContainer">@color/tidalwave_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/tidalwave_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/tidalwave_background</item>
        <item name="colorOnBackground">@color/tidalwave_onBackground</item>
        <item name="colorSurface">@color/tidalwave_surface</item>
        <item name="colorOnSurface">@color/tidalwave_onSurface</item>
        <item name="colorSurfaceVariant">@color/tidalwave_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/tidalwave_onSurfaceVariant</item>
        <item name="colorOutline">@color/tidalwave_outline</item>
        <item name="colorOnSurfaceInverse">@color/tidalwave_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/tidalwave_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/tidalwave_primaryInverse</item>
    </style>
    <style name="Theme.RootlessJamesDSP.YinYang">
        
        <item name="colorPrimary">@color/yinyang_primary</item>
        <item name="colorOnPrimary">@color/yinyang_onPrimary</item>
        <item name="colorPrimaryContainer">@color/yinyang_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/yinyang_onPrimaryContainer</item>
        <item name="colorSecondary">@color/yinyang_secondary</item>
        <item name="colorOnSecondary">@color/yinyang_onSecondary</item>
        <item name="colorSecondaryContainer">@color/yinyang_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/yinyang_onSecondaryContainer</item>
        <item name="colorTertiary">@color/yinyang_tertiary</item>
        <item name="colorOnTertiary">@color/yinyang_onTertiary</item>
        <item name="colorTertiaryContainer">@color/yinyang_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/yinyang_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/yinyang_background</item>
        <item name="colorOnBackground">@color/yinyang_onBackground</item>
        <item name="colorSurface">@color/yinyang_surface</item>
        <item name="colorOnSurface">@color/yinyang_onSurface</item>
        <item name="colorSurfaceVariant">@color/yinyang_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/yinyang_onSurfaceVariant</item>
        <item name="colorOutline">@color/yinyang_outline</item>
        <item name="colorOnSurfaceInverse">@color/yinyang_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/yinyang_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/yinyang_primaryInverse</item>
    </style>
    <style name="Theme.RootlessJamesDSP.Yotsuba">
        
        <item name="colorPrimary">@color/yotsuba_primary</item>
        <item name="colorOnPrimary">@color/yotsuba_onPrimary</item>
        <item name="colorPrimaryContainer">@color/yotsuba_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/yotsuba_onPrimaryContainer</item>
        <item name="colorSecondary">@color/yotsuba_secondary</item>
        <item name="colorOnSecondary">@color/yotsuba_onSecondary</item>
        <item name="colorSecondaryContainer">@color/yotsuba_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/yotsuba_onSecondaryContainer</item>
        <item name="colorTertiary">@color/yotsuba_tertiary</item>
        <item name="colorOnTertiary">@color/yotsuba_onTertiary</item>
        <item name="colorTertiaryContainer">@color/yotsuba_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/yotsuba_onTertiaryContainer</item>
        <item name="android:colorBackground">@color/yotsuba_background</item>
        <item name="colorOnBackground">@color/yotsuba_onBackground</item>
        <item name="colorSurface">@color/yotsuba_surface</item>
        <item name="colorOnSurface">@color/yotsuba_onSurface</item>
        <item name="colorSurfaceVariant">@color/yotsuba_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/yotsuba_onSurfaceVariant</item>
        <item name="colorOutline">@color/yotsuba_outline</item>
        <item name="colorOnSurfaceInverse">@color/yotsuba_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/yotsuba_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/yotsuba_primaryInverse</item>
    </style>
    <style name="Theme.Transparent" parent="Theme.AppCompat">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="ThemeOverlay.RootlessJamesDSP.Amoled" parent=""/>
    <declare-styleable name="Card">
        <attr format="string" name="titleText"/>
        <attr format="string" name="bodyText"/>
        <attr format="boolean" name="closeButtonVisible"/>
        <attr format="string" name="buttonText"/>
        <attr format="boolean" name="buttonEnabled"/>
        <attr format="boolean" name="checkboxVisible"/>
        <attr format="reference" name="iconSrc"/>
        <attr name="iconTint"/>
        <attr format="boolean" name="iconCentered"/>
        <attr format="color" name="cardBackground"/>
        <attr format="dimension" name="cardMargin"/>
    </declare-styleable>
    <declare-styleable name="DropDownPreference">
        <attr format="boolean" name="isStatic"/>
    </declare-styleable>
    <declare-styleable name="EqualizerPreference">
        <attr name="android:entries"/>
        <attr name="android:entryValues"/>
    </declare-styleable>
    <declare-styleable name="FileLibraryPreference">
        <attr format="string" name="type"/>
    </declare-styleable>
    <declare-styleable name="IconPreference">
        <attr name="iconTint"/>
    </declare-styleable>
    <declare-styleable name="MaterialSeekbarPreference">
        <attr name="seekBarIncrement"/>
        <attr name="showSeekBarValue"/>
        <attr name="updatesContinuously"/>
        <attr format="reference" name="seekBarStyle"/>
        <attr format="float" name="minValue"/>
        <attr format="float" name="maxValue"/>
        <attr format="string" name="unit"/>
        <attr format="integer" name="precision"/>
        <attr format="dimension" name="labelMinWidth"/>
    </declare-styleable>
    <declare-styleable name="NumberInputBox">
        <attr format="integer" name="floatPrecision"/>
        <attr format="float" name="step"/>
        <attr format="float" name="value"/>
        <attr format="float" name="android:min"/>
        <attr format="float" name="android:max"/>

        <attr format="string" name="suffixText"/>
        <attr format="string" name="hintText"/>
        <attr format="string" name="helperText"/>
        <attr format="boolean" name="helperTextEnabled"/>
    </declare-styleable>
</resources>