<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".fragment.AppCompatibilityFragment"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingVertical="8dp"
            android:paddingHorizontal="16dp"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:text="@string/app_compat_title" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceBody1"
                android:text="@string/app_compat_explanation" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/app_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="16dp"
                android:background="?android:attr/selectableItemBackground">
                <LinearLayout
                    android:padding="16dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:id="@+id/icon"
                        android:layout_marginEnd="16dp"
                        android:layout_gravity="center_vertical"
                        android:layout_height="match_parent"
                        android:layout_width="38dp"
                        android:contentDescription="@string/app_list_icon_alt"
                        tools:src="@android:drawable/sym_def_app_icon" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">
                        <TextView
                            android:id="@+id/app_name"
                            android:textAppearance="?attr/textAppearanceListItem"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:maxLines="1"
                            android:ellipsize="end"
                            tools:text="My application" />
                        <TextView
                            android:id="@+id/package_name"
                            android:textAppearance="?attr/textAppearanceListItemSecondary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:ellipsize="end"
                            tools:text="com.android.application" />
                    </LinearLayout>

                    <ImageView
                        style="?attr/materialIconButtonStyle"
                        android:id="@+id/launch_app"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:clickable="false"
                        android:focusable="false"
                        android:contentDescription="@string/open"
                        app:srcCompat="@drawable/ic_twotone_launch_24dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textAppearance="?attr/textAppearanceBody1"
                android:text="@string/app_compat_instruction" />

            <TextView
                android:id="@+id/hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textAppearance="?attr/textAppearanceBody1"
                android:visibility="gone" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textStyle="bold"
                android:textAppearance="?attr/textAppearanceBody1"
                android:text="@string/app_compat_instruction_exclude" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:orientation="horizontal">
        <com.google.android.material.button.MaterialButton
            style="?attr/materialButtonOutlinedStyle"
            android:id="@+id/retry"
            android:layout_weight="0.5"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:text="@string/app_compat_retry" />

        <Space
            android:layout_width="16dp"
            android:layout_height="wrap_content" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/exclude"
            android:layout_weight="0.5"
            android:layout_height="wrap_content"
            android:layout_width="0dp"
            android:text="@string/app_compat_exclude" />
    </LinearLayout>

</LinearLayout>