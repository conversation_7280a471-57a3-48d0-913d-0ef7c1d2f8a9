name: <PERSON><PERSON> Contributors Action

on:
  # When you push to the `main` branch
  push:
    branches: [ main ]
  # And optionally, once every 12 hours
  schedule:
    - cron: '0 */12 * * *' # https://crontab.guru/#0_*/12_*_*_*
  # To manually run this workflow
  workflow_dispatch:

jobs:
  crowdin-contributors:

    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Generate Crowdin Contributors data
      uses: andrii-bodnar/action-crowdin-contributors@v1.0.1
      id: contributors
      with:
        contributors_per_line: 6
        max_contributors: 32
        image_size: 64
        min_words_contributed: 1
        include_languages: false
        crowdin_project_link: https://crowdin.com/project/rootlessjamesdsp
      env:
        CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
        CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
        
    - name: Create translator info json
      uses: <PERSON><PERSON><PERSON><PERSON>/write-file-action@v1.2
      with:
        path: app/src/main/res/raw/translators.json
        write-mode: overwrite
        contents: ${{ steps.contributors.outputs.json_report }}

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v6.1.0
      with:
        title: Update Crowdin Contributors table
        body: By [action-crowdin-contributors](https://github.com/andrii-bodnar/action-crowdin-contributors) GitHub action
        commit-message: Update Crowdin Contributors table
        branch: crowdin-contributors/patch
