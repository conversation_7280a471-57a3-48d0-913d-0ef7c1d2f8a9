ninja: Entering directory `D:\Users\Cai_Mouhui\Documents\1145\RootlessJamesDSP\app\.cxx\RelWithDebInfo\2r275p33\x86_64'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/.cxx/RelWithDebInfo/2r275p33/x86_64
[0/2] Re-checking globbed directories...
[1/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/cpthread.c.o
[2/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/inv.c.o
[3/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/samplerate.c.o
[4/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/src_linear.c.o
[5/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o
FAILED: CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=x86_64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djdspimprestoolbox_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/../libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -MD -MT CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o -MF CMakeFiles\jdspimprestoolbox.dir\libjdspimptoolbox\main\JdspImpResToolbox.c.o.d -o CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/main/JdspImpResToolbox.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main/JdspImpResToolbox.c:7:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
1 warning and 12 errors generated.
[6/99] Building C object CMakeFiles/jdspimprestoolbox.dir/libjdspimptoolbox/libsamplerate/src_sinc.c.o
[7/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/libsamplerate/src_linear.c.o
[8/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/libsamplerate/samplerate.c.o
[9/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o
FAILED: CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=x86_64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\spatialAudio.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:4:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:573:2: error: unknown type name 'SpatialAudio'
  573 |         SpatialAudio spatialAudio;
      |         ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:141:6: error: conflicting types for 'SpatialAudioProcess'
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: note: previous declaration is here
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:144:6: error: conflicting types for 'SpatialAudioSetMode'
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: note: previous declaration is here
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:145:6: error: conflicting types for 'SpatialAudioSetHeadTracking'
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: note: previous declaration is here
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:146:6: error: conflicting types for 'SpatialAudioSetRoomType'
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: note: previous declaration is here
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:147:6: error: conflicting types for 'SpatialAudioSetSourcePosition'
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: note: previous declaration is here
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:148:6: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: note: previous declaration is here
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:149:6: error: conflicting types for 'SpatialAudioSetStereoWidth'
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: note: previous declaration is here
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:150:6: error: conflicting types for 'SpatialAudioSetRoomParameters'
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: note: previous declaration is here
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:151:6: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: note: previous declaration is here
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:152:6: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: note: previous declaration is here
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:175:6: error: conflicting types for 'SpatialAudioEnable'
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: note: previous declaration is here
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:176:6: error: conflicting types for 'SpatialAudioDisable'
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: note: previous declaration is here
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:145:6: error: conflicting types for 'SpatialAudioProcess'
  145 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: note: previous declaration is here
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:185:6: error: conflicting types for 'SpatialAudioSetMode'
  185 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: note: previous declaration is here
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:191:6: error: conflicting types for 'SpatialAudioSetHeadTracking'
  191 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: note: previous declaration is here
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:196:6: error: conflicting types for 'SpatialAudioSetRoomType'
  196 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: note: previous declaration is here
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:238:6: error: conflicting types for 'SpatialAudioSetSourcePosition'
  238 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: note: previous declaration is here
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:254:6: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  254 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll) {
      |      ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: note: previous declaration is here
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
fatal error: too many errors emitted, stopping now [-ferror-limit=]
1 warning and 20 errors generated.
[10/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/qr_fact.c.o
[11/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/mldivide.c.o
[12/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/libsamplerate/src_sinc.c.o
[13/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/cpoly.c.o
[14/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/mrdivide.c.o
[15/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/SolveLinearSystem/pinv.c.o
[16/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/quadprog.c.o
[17/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/solvopt.c.o
[18/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/EelVmVariable.cpp.o
[19/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JArrayList.cpp.o
[20/99] Building CXX object CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o
FAILED: CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=x86_64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_wrapper_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjdspimptoolbox/main -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libcrashlytics-connector -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/../libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -MD -MT CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o -MF CMakeFiles\jamesdsp-wrapper.dir\libjamesdsp-wrapper\JamesDspWrapper.cpp.o.d -o CMakeFiles/jamesdsp-wrapper.dir/libjamesdsp-wrapper/JamesDspWrapper.cpp.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp-wrapper/JamesDspWrapper.cpp:15:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
12 errors generated.
[21/99] Building CXX object CMakeFiles/crashlytics-connector.dir/libcrashlytics-connector/Log.cpp.o
[22/99] Building C object CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/eel2/numericSys/codelet.c.o
ninja: build stopped: subcommand failed.
