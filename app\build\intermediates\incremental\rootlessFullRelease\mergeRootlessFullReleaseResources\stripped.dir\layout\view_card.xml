<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="0dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- note: layout_marginEnd is overridden in code -->
            <ImageView
                android:id="@+id/icon"
                android:layout_gravity="center_vertical"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="14dp"
                app:srcCompat="@drawable/ic_numeric_1_circle_outline"
                android:alpha="0.8"
                app:tint="?attr/colorOnSurface"
                tools:ignore="ContentDescription" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="false">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/title"
                        tools:text="Title text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:textAppearance="?attr/textAppearanceTitleMedium" />
                    <TextView
                        android:id="@+id/text"
                        tools:text="Body text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/closeButtonLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginTop="-10dp"
                    android:layout_marginEnd="-10dp"
                    android:orientation="vertical">
                    <Button
                        style="?attr/materialIconButtonStyle"
                        android:id="@+id/close"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:contentDescription="@string/close"
                        android:tooltipText="@string/close"
                        app:icon="@drawable/ic_close_24dp" />
                    <com.google.android.material.checkbox.MaterialCheckBox
                        android:id="@+id/checkbox"
                        android:visibility="gone"
                        android:layout_width="48dp"
                        android:layout_height="48dp" />
                    <Space
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="1"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <Button
            android:id="@+id/button"
            android:minWidth="180dp"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_gravity="end" />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>