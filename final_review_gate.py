# -*- coding: utf-8 -*-
# final_review_gate.py
import sys

if __name__ == "__main__":
    print("""解决问题：C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=aarch64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=armv7-none-linux-androideabi29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\bs2b.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/bs2b.c:2:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\convolver1D.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/convolver1D.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\arbEqConv.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/arbEqConv.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\crossfeed.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/crossfeed.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dbb.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dbb.c:6:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:675:13: error: conflicting types for 'SpatialAudioEnable'
  675 | extern void SpatialAudioEnable(JamesDSPLib *jdsp, char enable);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:175:6: note: previous declaration is here
  175 | void SpatialAudioEnable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:676:13: error: conflicting types for 'SpatialAudioDisable'
  676 | extern void SpatialAudioDisable(JamesDSPLib *jdsp);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:176:6: note: previous declaration is here
  176 | void SpatialAudioDisable(SpatialAudio *spatial);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:677:13: error: conflicting types for 'SpatialAudioProcess'
  677 | extern void SpatialAudioProcess(JamesDSPLib *jdsp, size_t n);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:141:6: note: previous declaration is here
  141 | void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:678:13: error: conflicting types for 'SpatialAudioSetMode'
  678 | extern void SpatialAudioSetMode(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:144:6: note: previous declaration is here
  144 | void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:679:13: error: conflicting types for 'SpatialAudioSetHeadTracking'
  679 | extern void SpatialAudioSetHeadTracking(JamesDSPLib *jdsp, int mode);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:145:6: note: previous declaration is here
  145 | void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:680:13: error: conflicting types for 'SpatialAudioSetRoomType'
  680 | extern void SpatialAudioSetRoomType(JamesDSPLib *jdsp, int room_type);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:146:6: note: previous declaration is here
  146 | void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:681:13: error: conflicting types for 'SpatialAudioSetSourcePosition'
  681 | extern void SpatialAudioSetSourcePosition(JamesDSPLib *jdsp, float x, float y, float z);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:147:6: note: previous declaration is here
  147 | void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:682:13: error: conflicting types for 'SpatialAudioSetHeadOrientation'
  682 | extern void SpatialAudioSetHeadOrientation(JamesDSPLib *jdsp, float yaw, float pitch, float roll);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:148:6: note: previous declaration is here
  148 | void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:683:13: error: conflicting types for 'SpatialAudioSetStereoWidth'
  683 | extern void SpatialAudioSetStereoWidth(JamesDSPLib *jdsp, float width);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:149:6: note: previous declaration is here
  149 | void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:684:13: error: conflicting types for 'SpatialAudioSetRoomParameters'
  684 | extern void SpatialAudioSetRoomParameters(JamesDSPLib *jdsp, float size, float damping);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:150:6: note: previous declaration is here
  150 | void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:685:13: error: conflicting types for 'SpatialAudioSetDistanceAttenuation'
  685 | extern void SpatialAudioSetDistanceAttenuation(JamesDSPLib *jdsp, float attenuation);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:151:6: note: previous declaration is here
  151 | void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=i686-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\dynamic.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/dynamic.c:8:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:686:13: error: conflicting types for 'SpatialAudioSetCrossfeedStrength'
  686 | extern void SpatialAudioSetCrossfeedStrength(JamesDSPLib *jdsp, float strength);
      |             ^
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../Effects/spatialAudio.h:152:6: note: previous declaration is here
  152 | void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);
      |      ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=x86_64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\spatialAudio.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c

In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c:1:
In file included from D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.h:4:
D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:451:24: warning: declaration of 'struct dspsys' will not be visible outside of this function [-Wvisibility]
  451 |         void(*process)(struct dspsys *, size_t);
      |                               ^
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang.exe --target=x86_64-none-linux-android29 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Djamesdsp_EXPORTS -ID:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -std=gnu11 -Wno-incompatible-pointer-types -Wno-implicit-int -Wno-implicit-function-declaration -ffunction-sections -fdata-sections -Ofast -ftree-vectorize -DDEBUG -O2 -g -DNDEBUG -fPIC -O2 -std=gnu11 -MD -MT CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -MF CMakeFiles\jamesdsp.dir\libjamesdsp\Main\libjamesdsp\jni\jamesdsp\jdsp\Effects\spatialAudio.c.o.d -o CMakeFiles/jamesdsp.dir/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c.o -c D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/spatialAudio.c

D:/Users/<USER>/Documents/1145/RootlessJamesDSP/app/src/main/cpp/libjamesdsp/Main/libjamesdsp/jni/jamesdsp/jdsp/Effects/../jdsp_header.h:573:2: error: unknown type name 'SpatialAudio'
  573 |         SpatialAudio spatialAudio;
      |         ^
""", flush=True)
