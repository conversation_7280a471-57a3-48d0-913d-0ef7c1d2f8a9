<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.AeqSelectorActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:titleEnabled="false">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_collapseMode="pin">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/search_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|start"
                    android:paddingStart="0dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="2dp"
                    app:queryHint="@string/autoeq_search"
                    app:goIcon="@drawable/ic_twotone_chevron_right_24dp"
                    app:iconifiedByDefault="true" />
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/empty_view"
            android:visibility="visible"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:alpha="0.4"
                app:srcCompat="@drawable/ic_twotone_search_24dp"
                app:tint="?attr/colorSecondary"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/empty_view_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceTitleLarge"
                android:textColor="?attr/colorSecondary"
                android:gravity="center"
                android:alpha="0.4"
                android:text="@string/autoeq_enter_model"/>

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/progress"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ProgressBar
                android:layout_gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:layout_centerInParent="true"/>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/profile_list_container"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/partial_results_card"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:iconCentered="true"
                app:iconSrc="@drawable/ic_twotone_warning_24dp"
                app:iconTint="?attr/colorSecondary"
                app:cardBackground="?attr/colorSecondaryContainer"
                app:bodyText="@string/autoeq_partial_results_warning"
                app:cardMargin="8dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/profile_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>