<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <me.timschneeberger.rootlessjamesdsp.preference.MaterialSwitchPreference
        android:key="@string/key_spatialaudio_enable"
        android:title="@string/spatialaudio_enable_title"
        android:summary="@string/spatialaudio_enable_summary"
        android:defaultValue="false" />

    <ListPreference
        android:key="@string/key_spatialaudio_mode"
        android:title="@string/spatialaudio_mode_title"
        android:summary="@string/spatialaudio_mode_summary"
        android:entries="@array/spatialaudio_mode_entries"
        android:entryValues="@array/spatialaudio_mode_values"
        android:defaultValue="1"
        android:dependency="@string/key_spatialaudio_enable" />

    <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
        android:key="@string/key_spatialaudio_stereo_width"
        android:title="@string/spatialaudio_stereo_width_title"
        android:summary="@string/spatialaudio_stereo_width_summary"
        android:defaultValue="100"
        app:min="0"
        app:max="200"
        app:showSeekBarValue="true"
        app:units="%"
        android:dependency="@string/key_spatialaudio_enable" />

    <PreferenceCategory
        android:title="@string/spatialaudio_3d_positioning_title"
        android:dependency="@string/key_spatialaudio_enable">

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_azimuth"
            android:title="@string/spatialaudio_azimuth_title"
            android:summary="@string/spatialaudio_azimuth_summary"
            android:defaultValue="0"
            app:min="-180"
            app:max="180"
            app:showSeekBarValue="true"
            app:units="°" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_elevation"
            android:title="@string/spatialaudio_elevation_title"
            android:summary="@string/spatialaudio_elevation_summary"
            android:defaultValue="0"
            app:min="-90"
            app:max="90"
            app:showSeekBarValue="true"
            app:units="°" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_distance"
            android:title="@string/spatialaudio_distance_title"
            android:summary="@string/spatialaudio_distance_summary"
            android:defaultValue="100"
            app:min="10"
            app:max="1000"
            app:showSeekBarValue="true"
            app:units="cm" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/spatialaudio_head_tracking_title"
        android:dependency="@string/key_spatialaudio_enable">

        <ListPreference
            android:key="@string/key_spatialaudio_head_tracking"
            android:title="@string/spatialaudio_head_tracking_mode_title"
            android:summary="@string/spatialaudio_head_tracking_mode_summary"
            android:entries="@array/spatialaudio_head_tracking_entries"
            android:entryValues="@array/spatialaudio_head_tracking_values"
            android:defaultValue="0" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_head_tracking_speed"
            android:title="@string/spatialaudio_head_tracking_speed_title"
            android:summary="@string/spatialaudio_head_tracking_speed_summary"
            android:defaultValue="50"
            app:min="10"
            app:max="100"
            app:showSeekBarValue="true"
            app:units="%"
            android:dependency="@string/key_spatialaudio_head_tracking" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/spatialaudio_room_simulation_title"
        android:dependency="@string/key_spatialaudio_enable">

        <ListPreference
            android:key="@string/key_spatialaudio_room_type"
            android:title="@string/spatialaudio_room_type_title"
            android:summary="@string/spatialaudio_room_type_summary"
            android:entries="@array/spatialaudio_room_type_entries"
            android:entryValues="@array/spatialaudio_room_type_values"
            android:defaultValue="0" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_room_size"
            android:title="@string/spatialaudio_room_size_title"
            android:summary="@string/spatialaudio_room_size_summary"
            android:defaultValue="50"
            app:min="0"
            app:max="100"
            app:showSeekBarValue="true"
            app:units="%"
            android:dependency="@string/key_spatialaudio_room_type" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_room_damping"
            android:title="@string/spatialaudio_room_damping_title"
            android:summary="@string/spatialaudio_room_damping_summary"
            android:defaultValue="50"
            app:min="0"
            app:max="100"
            app:showSeekBarValue="true"
            app:units="%"
            android:dependency="@string/key_spatialaudio_room_type" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/spatialaudio_advanced_title"
        android:dependency="@string/key_spatialaudio_enable">

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_distance_attenuation"
            android:title="@string/spatialaudio_distance_attenuation_title"
            android:summary="@string/spatialaudio_distance_attenuation_summary"
            android:defaultValue="70"
            app:min="0"
            app:max="100"
            app:showSeekBarValue="true"
            app:units="%" />

        <me.timschneeberger.rootlessjamesdsp.preference.MaterialSeekbarPreference
            android:key="@string/key_spatialaudio_crossfeed_strength"
            android:title="@string/spatialaudio_crossfeed_strength_title"
            android:summary="@string/spatialaudio_crossfeed_strength_summary"
            android:defaultValue="30"
            app:min="0"
            app:max="100"
            app:showSeekBarValue="true"
            app:units="%" />

        <Preference
            android:key="@string/key_spatialaudio_presets"
            android:title="@string/spatialaudio_presets_title"
            android:summary="@string/spatialaudio_presets_summary" />

        <Preference
            android:key="@string/key_spatialaudio_reset"
            android:title="@string/spatialaudio_reset_title"
            android:summary="@string/spatialaudio_reset_summary" />

    </PreferenceCategory>

</PreferenceScreen>
