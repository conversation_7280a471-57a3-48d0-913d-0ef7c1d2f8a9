<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <me.timschneeberger.rootlessjamesdsp.view.EqualizerSurface
        android:id="@+id/equalizer_surface"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/equalizer_presets"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </HorizontalScrollView>

</LinearLayout>