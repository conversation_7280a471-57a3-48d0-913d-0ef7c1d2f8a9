// Generated by view binder compiler. Do not edit!
package me.timschneeberger.rootlessjamesdsp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.FragmentContainerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.timschneeberger.rootlessjamesdsp.R;
import me.timschneeberger.rootlessjamesdsp.view.Card;

public final class FragmentDspBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final FragmentContainerView cardBass;

  @NonNull
  public final FragmentContainerView cardCompressor;

  @NonNull
  public final LinearLayout cardContainer;

  @NonNull
  public final FragmentContainerView cardConvolver;

  @NonNull
  public final FragmentContainerView cardCrossfeed;

  @NonNull
  public final FragmentContainerView cardDdc;

  @NonNull
  public final FragmentContainerView cardDeviceProfiles;

  @NonNull
  public final FragmentContainerView cardEq;

  @NonNull
  public final FragmentContainerView cardGeq;

  @NonNull
  public final FragmentContainerView cardLiveprog;

  @NonNull
  public final FragmentContainerView cardOutputControl;

  @NonNull
  public final FragmentContainerView cardReverb;

  @NonNull
  public final FragmentContainerView cardStereowide;

  @NonNull
  public final FragmentContainerView cardTube;

  @NonNull
  public final NestedScrollView dspScrollview;

  @NonNull
  public final Card translationNotice;

  @NonNull
  public final Card updateNotice;

  private FragmentDspBinding(@NonNull NestedScrollView rootView,
      @NonNull FragmentContainerView cardBass, @NonNull FragmentContainerView cardCompressor,
      @NonNull LinearLayout cardContainer, @NonNull FragmentContainerView cardConvolver,
      @NonNull FragmentContainerView cardCrossfeed, @NonNull FragmentContainerView cardDdc,
      @NonNull FragmentContainerView cardDeviceProfiles, @NonNull FragmentContainerView cardEq,
      @NonNull FragmentContainerView cardGeq, @NonNull FragmentContainerView cardLiveprog,
      @NonNull FragmentContainerView cardOutputControl, @NonNull FragmentContainerView cardReverb,
      @NonNull FragmentContainerView cardStereowide, @NonNull FragmentContainerView cardTube,
      @NonNull NestedScrollView dspScrollview, @NonNull Card translationNotice,
      @NonNull Card updateNotice) {
    this.rootView = rootView;
    this.cardBass = cardBass;
    this.cardCompressor = cardCompressor;
    this.cardContainer = cardContainer;
    this.cardConvolver = cardConvolver;
    this.cardCrossfeed = cardCrossfeed;
    this.cardDdc = cardDdc;
    this.cardDeviceProfiles = cardDeviceProfiles;
    this.cardEq = cardEq;
    this.cardGeq = cardGeq;
    this.cardLiveprog = cardLiveprog;
    this.cardOutputControl = cardOutputControl;
    this.cardReverb = cardReverb;
    this.cardStereowide = cardStereowide;
    this.cardTube = cardTube;
    this.dspScrollview = dspScrollview;
    this.translationNotice = translationNotice;
    this.updateNotice = updateNotice;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDspBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDspBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_dsp, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDspBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_bass;
      FragmentContainerView cardBass = ViewBindings.findChildViewById(rootView, id);
      if (cardBass == null) {
        break missingId;
      }

      id = R.id.card_compressor;
      FragmentContainerView cardCompressor = ViewBindings.findChildViewById(rootView, id);
      if (cardCompressor == null) {
        break missingId;
      }

      id = R.id.card_container;
      LinearLayout cardContainer = ViewBindings.findChildViewById(rootView, id);
      if (cardContainer == null) {
        break missingId;
      }

      id = R.id.card_convolver;
      FragmentContainerView cardConvolver = ViewBindings.findChildViewById(rootView, id);
      if (cardConvolver == null) {
        break missingId;
      }

      id = R.id.card_crossfeed;
      FragmentContainerView cardCrossfeed = ViewBindings.findChildViewById(rootView, id);
      if (cardCrossfeed == null) {
        break missingId;
      }

      id = R.id.card_ddc;
      FragmentContainerView cardDdc = ViewBindings.findChildViewById(rootView, id);
      if (cardDdc == null) {
        break missingId;
      }

      id = R.id.card_device_profiles;
      FragmentContainerView cardDeviceProfiles = ViewBindings.findChildViewById(rootView, id);
      if (cardDeviceProfiles == null) {
        break missingId;
      }

      id = R.id.card_eq;
      FragmentContainerView cardEq = ViewBindings.findChildViewById(rootView, id);
      if (cardEq == null) {
        break missingId;
      }

      id = R.id.card_geq;
      FragmentContainerView cardGeq = ViewBindings.findChildViewById(rootView, id);
      if (cardGeq == null) {
        break missingId;
      }

      id = R.id.card_liveprog;
      FragmentContainerView cardLiveprog = ViewBindings.findChildViewById(rootView, id);
      if (cardLiveprog == null) {
        break missingId;
      }

      id = R.id.card_output_control;
      FragmentContainerView cardOutputControl = ViewBindings.findChildViewById(rootView, id);
      if (cardOutputControl == null) {
        break missingId;
      }

      id = R.id.card_reverb;
      FragmentContainerView cardReverb = ViewBindings.findChildViewById(rootView, id);
      if (cardReverb == null) {
        break missingId;
      }

      id = R.id.card_stereowide;
      FragmentContainerView cardStereowide = ViewBindings.findChildViewById(rootView, id);
      if (cardStereowide == null) {
        break missingId;
      }

      id = R.id.card_tube;
      FragmentContainerView cardTube = ViewBindings.findChildViewById(rootView, id);
      if (cardTube == null) {
        break missingId;
      }

      NestedScrollView dspScrollview = (NestedScrollView) rootView;

      id = R.id.translation_notice;
      Card translationNotice = ViewBindings.findChildViewById(rootView, id);
      if (translationNotice == null) {
        break missingId;
      }

      id = R.id.update_notice;
      Card updateNotice = ViewBindings.findChildViewById(rootView, id);
      if (updateNotice == null) {
        break missingId;
      }

      return new FragmentDspBinding((NestedScrollView) rootView, cardBass, cardCompressor,
          cardContainer, cardConvolver, cardCrossfeed, cardDdc, cardDeviceProfiles, cardEq, cardGeq,
          cardLiveprog, cardOutputControl, cardReverb, cardStereowide, cardTube, dspScrollview,
          translationNotice, updateNotice);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
