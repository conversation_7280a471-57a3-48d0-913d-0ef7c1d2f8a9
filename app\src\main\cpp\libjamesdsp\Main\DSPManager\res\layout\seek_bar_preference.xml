<?xml version="1.0" encoding="UTF-8"?>
<!--
/*
** Copyright 2013, The ChameleonOS Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@android:id/widget_frame"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="8dp"
    android:paddingTop="5dp"
    android:paddingRight="10dp"
    android:paddingBottom="5dp">

  <TextView
      android:id="@android:id/title"
      android:layout_alignParentLeft="true"
      android:layout_alignParentTop="true"
      android:layout_width="fill_parent"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceMedium" />

  <TextView
      android:id="@android:id/summary"
      android:layout_alignParentLeft="true"
      android:layout_width="fill_parent"
      android:layout_height="wrap_content"
      android:layout_below="@android:id/title"
      android:textAppearance="?android:attr/textAppearanceSmall"
      android:textColor="?android:attr/textColorSecondary" />

  <RelativeLayout
      android:layout_width="fill_parent"
      android:layout_height="wrap_content"
      android:layout_below="@android:id/summary">

    <TextView
        android:id="@+id/seekBarPrefUnitsRight"
        android:layout_centerInParent="true"
        android:layout_alignParentRight="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceSmall" />

    <TextView
        android:id="@+id/seekBarPrefValue"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toLeftOf="@id/seekBarPrefUnitsRight"
        android:gravity="right"
        android:textAppearance="?android:attr/textAppearanceSmall" />

    <TextView
        android:id="@+id/seekBarPrefUnitsLeft"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@id/seekBarPrefValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceSmall" />

    <LinearLayout
        android:id="@+id/seekBarPrefBarContainer"
        android:layout_centerInParent="true"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_toLeftOf="@id/seekBarPrefUnitsLeft" />
  </RelativeLayout>
</RelativeLayout>