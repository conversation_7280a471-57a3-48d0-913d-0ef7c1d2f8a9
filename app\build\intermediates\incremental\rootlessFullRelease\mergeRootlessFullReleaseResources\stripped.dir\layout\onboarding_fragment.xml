<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/onboarding_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/onboarding_page1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page1" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page2" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page3"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page3"
            android:id="@+id/method_select" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page4"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page4"
            android:id="@+id/adb_setup"/>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page5"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page5"
            android:id="@+id/other_perms" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page6"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page6" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/onboarding_page7"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/controls_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <include layout="@layout/onboarding_page6" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/controls_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <include layout="@layout/onboarding_controls" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
