<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="?attr/dialogPreferredPadding"
    android:paddingStart="?attr/dialogPreferredPadding"
    android:paddingEnd="?attr/dialogPreferredPadding">

    <com.google.android.material.textfield.TextInputLayout
        style="?attr/textInputFilledStyle"
        android:id="@+id/text_input_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="4dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@android:id/text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </com.google.android.material.textfield.TextInputLayout>
</FrameLayout>
