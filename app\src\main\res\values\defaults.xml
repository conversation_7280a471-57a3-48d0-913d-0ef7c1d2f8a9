<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- This file contains default values for shared preferences.
         Key names always have a 'default_' prefix and should otherwise match keys.xml.
         DSP-related configuration keys should not be put here and are handled differently. -->

    <!-- Internal -->
    <bool name="default_first_boot" translatable="false" tools:keep="@bool/default_first_boot">true</bool>
    <integer name="default_snooze_translation_notice" translatable="false" tools:keep="@integer/default_snooze_translation_notice">0</integer>
    <bool name="default_android15_screenrecord_restriction_seen" translatable="false" tools:keep="@bool/default_android15_screenrecord_restriction_seen">false</bool>
    <integer name="default_update_check_timeout" translatable="false" tools:keep="@integer/default_update_check_timeout">0</integer>
    <integer name="default_update_check_skip" translatable="false" tools:keep="@integer/default_update_check_skip">0</integer>
    <bool name="default_reset_proc_mode_fix_applied" translatable="false" tools:keep="@bool/default_reset_proc_mode_fix_applied">false</bool>
    <bool name="default_is_activity_active" translatable="false" tools:keep="@bool/default_is_activity_active">false</bool>
    <bool name="default_is_app_compat_activity_active" translatable="false" tools:keep="@bool/default_is_app_compat_activity_active">false</bool>

    <!-- Internal DSP -->
    <bool name="default_powered_on" translatable="false" tools:keep="@bool/default_powered_on">true</bool>
    <string name="default_benchmark_c0" translatable="false" tools:keep="@string/default_benchmark_c0" />
    <string name="default_benchmark_c1" translatable="false" tools:keep="@string/default_benchmark_c0" />

    <!-- Settings -->
    <bool name="default_share_crash_reports" translatable="false">true</bool>
    <string name="default_network_autoeq_api_url" translatable="false">https://aeq.timschneeberger.me/</string>
    <bool name="default_session_exclude_restricted">true</bool>
    <string name="default_session_detection_method" translatable="false">1</string>
    <string name="default_session_continuous_polling_rate" translatable="false">3000</string>
    <bool name="default_session_continuous_polling" translatable="false">false</bool>
    <bool name="default_session_loss_ignore" translatable="false">false</bool>
    <bool name="default_session_app_problem_ignore" translatable="false">false</bool>
    <bool name="default_exclude_app_from_recents" translatable="false">false</bool>
    <bool name="default_autostart_prompt_at_boot" translatable="false">true</bool>
    <bool name="default_powersave_suspend" translatable="false">true</bool>
    <string name="default_audioformat_encoding" translatable="false">1</string>
    <integer name="default_audioformat_buffersize" translatable="false">8192</integer>
    <bool name="default_audioformat_processing" translatable="false">true</bool>
    <bool name="default_audioformat_enhanced_processing" translatable="false">false</bool>
    <bool name="default_audioformat_optimization_benchmark" translatable="false">false</bool>

    <!-- Device profiles -->
    <bool name="default_device_profiles_enable" translatable="false">false</bool>

    <!-- Appearance -->
    <string name="default_appearance_theme_mode" translatable="false">0</string>
    <bool name="default_appearance_pure_black" translatable="false">false</bool>
    <string name="default_appearance_app_theme" translatable="false">DEFAULT</string>
    <bool name="default_appearance_nav_hide" translatable="false">false</bool>
    <bool name="default_appearance_show_icons" translatable="false">true</bool>

    <!-- Backups -->
    <string name="default_backup_frequency" translatable="false">0</string>
    <string name="default_backup_maximum" translatable="false">2</string>
    <string name="default_backup_location" translatable="false" />

    <!-- Script editor -->
    <integer name="default_editor_font_size" translatable="false" tools:keep="@integer/default_editor_font_size">13</integer>

</resources>