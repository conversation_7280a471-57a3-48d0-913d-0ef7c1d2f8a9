desc: STFT CentreBoost
//tags: stft

gaindb:3<-15,15>Boost gain (dB)

@init
gaindb = 3.00;

function db2mag(db)
(
  pow(10, db / 20);
);
function mag2db(mag)
(
  20 * log10(mag);
);
fftsize = 4096;
bufpos = idx = 0;
stftIndexLeft = 2;
stftIndexRight = 50;
memreq = stftCheckMemoryRequirement(stftIndexLeft, fftsize, 4, 2);
memreq = stftCheckMemoryRequirement(stftIndexRight, fftsize, 4, 2);
stftStructLeft = 120;
stftStructRight = stftStructLeft + memreq;
requiredSamples = stftInit(stftIndexLeft, stftStructLeft);
requiredSamples = stftInit(stftIndexRight, stftStructRight);
inBufLeft = stftStructRight + memreq + 10; // Pointer to memory
outBufLeft = inBufLeft + fftsize + 10; // Pointer to memory plus safe zone
inBufRight = outBufLeft + fftsize + 10; // ...
outBufRight = inBufRight + fftsize + 10; // ...
gain=db2mag(gaindb) - 1; // Boost 3 dB

@sample
inBufLeft[bufpos] = spl0;
spl0 = outBufLeft[bufpos];
inBufRight[bufpos] = spl1;
spl1 = outBufRight[bufpos];
bufpos += 1;
bufpos >= requiredSamples ?
(
  error1 = stftForward(inBufLeft, stftIndexLeft, stftStructLeft, 1);
  error2 = stftForward(inBufRight, stftIndexRight, stftStructRight, 1);
  idx=0;

  loop(error1 / 2, 
  sumR = inBufLeft[idx] + inBufRight[idx];
  sumI = inBufLeft[idx + 1] + inBufRight[idx + 1];
  diffR = inBufLeft[idx] - inBufRight[idx];
  diffI = inBufLeft[idx + 1] - inBufRight[idx + 1];
  sumSq = sumR*sumR + sumI*sumI;
  diffSq = diffR*diffR + diffI*diffI;
  alpha = 0.0;
  sumSq > $EPS ? (alpha = 0.5 - sqrt(diffSq / sumSq) * 0.5;);
  cR = sumR * alpha;
  cI = sumI * alpha;
  inBufLeft[idx] = inBufLeft[idx] + cR * gain;
  inBufLeft[idx + 1] = inBufLeft[idx + 1] + cI * gain;
  inBufRight[idx] = inBufRight[idx] + cR * gain;
  inBufRight[idx + 1] = inBufRight[idx + 1] + cI * gain;
  idx+=2);
  error = stftBackward(inBufLeft, stftIndexLeft, stftStructLeft, 1);
  error = stftBackward(inBufRight, stftIndexRight, stftStructRight, 1);
  idx = 0;
  loop(requiredSamples,
  outBufLeft[idx] = inBufLeft[idx];
  outBufRight[idx] = inBufRight[idx];
  idx+=1);
  bufpos = 0;
);
