<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="james.dsp" android:versionCode="91" android:versionName="9.1"><!--Add android:sharedUserId="android.uid.system" before release-->
	<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="29"/>
	<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="android.permission.BLUETOOTH" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
	<application
        android:icon="@drawable/icon"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:persistent="true"
        android:allowBackup="true">
		<activity
            android:label="@string/app_name"
            android:name=".activity.DSPManager"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|screenSize">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
				<category android:name="android.intent.category.MULTIWINDOW_LAUNCHER" />
			</intent-filter>
			<intent-filter>
				<action android:name="android.media.action.DISPLAY_AUDIO_EFFECT_CONTROL_PANEL" />
				<category android:name="android.intent.category.DEFAULT" />
			</intent-filter>
		</activity>
		<service
            android:exported="false"
            android:enabled="true"
            android:name=".service.HeadsetService" />
		<receiver android:name=".receiver.BootCompletedReceiver">
			<intent-filter>
				<action android:name="android.intent.action.BOOT_COMPLETED" />
				<category android:name="android.intent.category.DEFAULT" />
			</intent-filter>
		</receiver>
		<meta-data android:name="com.sec.android.support.multiwindow" android:value="true"></meta-data>
	</application>
</manifest>