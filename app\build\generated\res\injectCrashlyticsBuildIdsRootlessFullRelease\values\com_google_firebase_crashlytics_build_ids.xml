<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
<!--
  This file is automatically generated by Crashlytics to uniquely
  identify the build ids for your Android application.

  Do NOT modify or commit to source control!
-->
<string-array name="com.google.firebase.crashlytics.build_ids_lib" tools:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
<item>libcrashlytics-common.so</item>
<item>libcrashlytics-connector.so</item>
<item>libcrashlytics-handler.so</item>
<item>libcrashlytics-trampoline.so</item>
<item>libcrashlytics.so</item>
<item>libjamesdsp-wrapper.so</item>
<item>libjamesdsp.so</item>
<item>libjdspimprestoolbox.so</item>
</string-array>
<string-array name="com.google.firebase.crashlytics.build_ids_arch" tools:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>aarch64</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>arm</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
<item>x86_64</item>
</string-array>
<string-array name="com.google.firebase.crashlytics.build_ids_build_id" tools:ignore="UnusedResources,TypographyDashes" translatable="false">
<item>84458063500873d88f928022e40ebab571b0eb6f</item>
<item>a565453f1003b63ac305d7933979e88c67bede7a</item>
<item>6b90d7edb171dc8d08e8048e7f0bbf7ff3f6b715</item>
<item>acf6365f1a57d0513d7e78424b14f07c579aa5fc</item>
<item>988a3ef475baabbb23fefbe3a78321b0fdabcf83</item>
<item>ae4f326edc5b2f13cd6cd61ed6df466a92cd3528</item>
<item>f8b50c9c60f31ffe69daa7cf173d0cdf60975cd2</item>
<item>184e1bb99d4837e51ec20604790753aa182d2586</item>
<item>0a3dbcd586db47ff982af5b0d84abecf3dc275eb</item>
<item>c4fdddae11c183d3fc282c4055e0c415498e3be2</item>
<item>6ba77dbb7533b0d39295bf0b298fdc35477d4d0d</item>
<item>bc860efe07caf057f84f5a5507e1a3fee7bfbe88</item>
<item>5e64ab61bdc0fc0292344c878691de20f1f84d61</item>
<item>a996254f592aa4c819ff32f0efa38ba48bf3f1d3</item>
<item>637842a841637a2ab0f6f323713b5aa102083e2d</item>
<item>d809f88a10792119ea09c7df8bfb28063bea405b</item>
<item>1db1b3ec55d1bbbb3bd2601c448b8bfb83a038bb</item>
<item>fc263ea01b1dab970c94f2d5e6053af6a903f3cf</item>
<item>3f6a9e117ea061ef3dd21b33b3b47af883598f70</item>
<item>a764ec2fc98c82741950b8d0f8779b43a11140ec</item>
<item>ede5b131780f8d0c88a9d57aea00889421276385</item>
<item>18b72934460e45ac0fe9e5adcaa65c6b4bd247e1</item>
<item>d36cc13c1f8f3b5959ccb1351282d0c87dbc0e14</item>
<item>4c53e52ef22c3df72390baaf38633461423cfe44</item>
<item>316d533ee882d9420bfcfc20e4f3d1c56c86672f</item>
<item>f9c66dd64815e600b7265e551f847a38dc6fae3d</item>
<item>56a1bea1e58b10dcba9c1ab8a843a20f5e17a736</item>
<item>5d3b215b3145206bcd102b4f29b3cf1224255124</item>
<item>d0160199dfaac60b67577b845a3c3e3969747b22</item>
<item>3e3ced1daa72aff917ebd66b9bfece444cc7230f</item>
<item>949371520ae8c2c473823048a1973b5b31e61ee0</item>
<item>b1c9176c9d5bf2bf6a94cab2632c330a97945753</item>
</string-array>
</resources>
