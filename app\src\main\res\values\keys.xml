<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Internal -->
    <string name="key_first_boot" translatable="false">first_boot</string>
    <string name="key_snooze_translation_notice" translatable="false">snooze_translation_notice</string>
    <string name="key_android15_screenrecord_restriction_seen" translatable="false">android15_screenrecord_restriction_seen</string>
    <string name="key_update_check_timeout" translatable="false">update_check_timeout</string>
    <string name="key_update_check_skip" translatable="false">update_check_skip</string>
    <string name="key_reset_proc_mode_fix_applied" translatable="false">reset_proc_mode_fix_applied</string>
    <string name="key_is_activity_active" translatable="false">is_activity_active</string>
    <string name="key_is_app_compat_activity_active" translatable="false">is_app_compat_activity_active</string>

    <!-- Internal DSP -->
    <string name="key_powered_on" translatable="false">powered_on</string>
    <string name="key_benchmark_c0" translatable="false">benchmark_c0</string>
    <string name="key_benchmark_c1" translatable="false">benchmark_c1</string>

    <!-- Settings categories (static) -->
    <string name="key_appearance" translatable="false">appearance</string>
    <string name="key_audio_format" translatable="false">audio_format</string>
    <string name="key_device_profiles" translatable="false">device_profiles</string>
    <string name="key_troubleshooting" translatable="false">troubleshooting</string>
    <string name="key_misc" translatable="false">misc</string>
    <string name="key_backup" translatable="false">backup</string>
    <string name="key_about" translatable="false">about</string>
    <string name="key_translators" translatable="false">translators</string>

    <!-- Settings -->
    <string name="key_share_crash_reports" translatable="false">share_crash_reports</string>
    <string name="key_network_autoeq_api_url" translatable="false">network_autoeq_api_url</string>
    <string name="key_session_exclude_restricted" translatable="false">session_exclude_restricted</string>
    <string name="key_session_detection_method" translatable="false">session_detection_method</string>
    <string name="key_session_continuous_polling_rate" translatable="false">session_continuous_polling_rate</string>
    <string name="key_session_continuous_polling" translatable="false">session_continuous_polling</string>
    <string name="key_session_loss_ignore" translatable="false">session_loss_ignore</string>
    <string name="key_session_app_problem_ignore" translatable="false">session_app_problem_ignore</string>
    <string name="key_exclude_app_from_recents" translatable="false">exclude_app_from_recents</string>
    <string name="key_autostart_prompt_at_boot" translatable="false">autostart_prompt_at_boot</string>
    <string name="key_powersave_suspend" translatable="false">powersave_suspend</string>
    <string name="key_audioformat_encoding" translatable="false">audioformat_encoding</string>
    <string name="key_audioformat_buffersize" translatable="false">audioformat_buffersize</string>
    <string name="key_audioformat_processing" translatable="false">audioformat_processing</string>
    <string name="key_audioformat_enhanced_processing" translatable="false">audioformat_enhanced_processing</string>
    <string name="key_audioformat_optimization_benchmark" translatable="false">audioformat_optimization_benchmark</string>

    <!-- Device profiles -->
    <string name="key_device_profiles_enable" translatable="false">device_profiles_enable</string>

    <!-- Appearance -->
    <string name="key_appearance_theme_mode" translatable="false">appearance_theme_mode</string>
    <string name="key_appearance_pure_black" translatable="false">appearance_pure_black</string>
    <string name="key_appearance_app_theme" translatable="false">appearance_app_theme</string>
    <string name="key_appearance_nav_hide" translatable="false">appearance_nav_hide</string>
    <string name="key_appearance_show_icons" translatable="false">appearance_show_icons</string>

    <!-- Backups -->
    <string name="key_backup_frequency" translatable="false">backup_frequency</string>
    <string name="key_backup_location" translatable="false">backup_location</string>
    <string name="key_backup_maximum" translatable="false">backup_maximum</string>

    <!-- Settings (static) -->
    <string name="key_backup_create" translatable="false">backup_create</string>
    <string name="key_backup_restore" translatable="false">backup_restore</string>
    <string name="key_troubleshooting_dump" translatable="false">troubleshooting_dump</string>
    <string name="key_troubleshooting_notification_access" translatable="false">troubleshooting_notification_access</string>
    <string name="key_troubleshooting_repair_assets" translatable="false">troubleshooting_repair_assets</string>
    <string name="key_troubleshooting_view_limitations" translatable="false">troubleshooting_view_limitations</string>
    <string name="key_credits_version" translatable="false">credits_version</string>
    <string name="key_credits_build_info" translatable="false">credits_build_info</string>
    <string name="key_credits_check_update" translatable="false">credits_check_update</string>
    <string name="key_credits_google_play" translatable="false">credits_google_play</string>
    <string name="key_audioformat_enhanced_processing_info" translatable="false">audioformat_enhanced_processing_info</string>
    <string name="key_audioformat_optimization_refresh" translatable="false">audioformat_optimization_refresh</string>
    <string name="key_device_profiles_info" translatable="false">device_profiles_info</string>
    <string name="key_misc_permission_skip_prompt" translatable="false">misc_permission_skip_prompt</string>
    <string name="key_misc_permission_auto_start" translatable="false">misc_permission_auto_start</string>
    <string name="key_misc_permission_restart_setup" translatable="false">misc_permission_restart_setup</string>

    <!-- Script editor -->
    <string name="key_editor_font_size" translatable="false">editor_font_size</string>

    <!-- Deprecated -->
    <string name="key_debug_database" translatable="false">debug_database</string>

    <!-- Device profiles -->
    <string name="key_profile_active" translatable="false">profile_active</string> <!-- static -->

    <!-- DSP -->
    <string name="key_compander_enable" translatable="false">compander_enable</string>
    <string name="key_compander_timeconstant" translatable="false">compander_timeconstant</string>
    <string name="key_compander_granularity" translatable="false">compander_granularity</string>
    <string name="key_compander_tftransforms" translatable="false">compander_tftransforms</string>
    <string name="key_compander_response" translatable="false">compander_response</string>

    <string name="key_bass_enable" translatable="false">bass_enable</string>
    <string name="key_bass_max_gain" translatable="false">bass_max_gain</string>

    <string name="key_eq_enable" translatable="false">eq_enable</string>
    <string name="key_eq_bands" translatable="false">eq_bands</string>
    <string name="key_eq_filter_type" translatable="false">eq_filter_type</string>
    <string name="key_eq_interpolation" translatable="false">eq_interpolation</string>

    <string name="key_geq_enable" translatable="false">geq_enable</string>
    <string name="key_geq_nodes" translatable="false">geq_nodes</string>

    <string name="key_reverb_enable" translatable="false">reverb_enable</string>
    <string name="key_reverb_preset" translatable="false">reverb_preset</string>

    <string name="key_stereowide_enable" translatable="false">stereowide_enable</string>
    <string name="key_stereowide_mode" translatable="false">stereowide_mode</string>

    <string name="key_crossfeed_enable" translatable="false">bs2b_crossfeed_enable</string>
    <string name="key_crossfeed_mode" translatable="false">bs2b_crossfeed_mode</string>

    <string name="key_convolver_enable" translatable="false">convolver_enable</string>
    <string name="key_convolver_file" translatable="false">convolver_file</string>
    <string name="key_convolver_mode" translatable="false">convolver_mode</string>
    <string name="key_convolver_adv_imp" translatable="false">convolver_adv_imp</string>

    <string name="key_tube_enable" translatable="false">tube_enable</string>
    <string name="key_tube_drive" translatable="false">tube_drive</string>

    <string name="key_ddc_enable" translatable="false">ddc_enable</string>
    <string name="key_ddc_file" translatable="false">ddc_file</string>

    <string name="key_liveprog_enable" translatable="false">liveprog_enable</string>
    <string name="key_liveprog_file" translatable="false">liveprog_file</string>
    <string name="key_liveprog_params" translatable="false">liveprog_params</string> <!-- static -->
    <string name="key_liveprog_edit" translatable="false">liveprog_edit</string> <!-- static -->

    <string name="key_limiter_threshold" translatable="false">limiter_threshold</string>
    <string name="key_limiter_release" translatable="false">limiter_release</string>
    <string name="key_output_postgain" translatable="false">output_postgain</string>
</resources>