{"src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\DumpManager.kt": ["Method:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method.AudioFlingerService", "unregisterOnDumpMethodChangeListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "OnDumpMethodChangeListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "fromInt:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method.Companion", "AudioPolicyService:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method.AudioPolicyService", "registerOnDumpMethodChangeListener:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager", "collectDebugDumps:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "dumpSessions:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method.AudioService", "AudioFlingerService:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method", "AudioService:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DumpManager.Method.Companion", "dumpCaptureAllowlistLog:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager", "onDumpMethodChange:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump.DumpManager.OnDumpMethodChangeListener", "DumpManager:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\FlavorConditionals.kt": ["isRoot:me.tims<PERSON>.rootlessjamesdsp.utils", "isRootless:me.tims<PERSON>.rootlessjamesdsp.utils", "isPlugin:me.tims<PERSON>.rootlessjamesdsp.utils"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\PackageServiceDump.kt": ["apps:me.timschneeberger.rootlessjamesdsp.session.dump.data.PackageServiceDump", "PackageServiceDump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data", "toString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.PackageServiceDump"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\BaseEqualizerSurface.kt": ["maxDb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "setBand:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "computeCurve:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "BaseEqualizerSurface:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "onLayout:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "findClosest:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "onRestoreInstanceState:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "onDraw:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "maxHz:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "minHz:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "bandsNum:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "areKnobsVisible:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "minDb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "onSaveInstanceState:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "horizLineInterval:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "nPts:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "addElement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "onAttachedToWindow:me.timschnee<PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface", "frequencyScale:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.BaseEqualizerSurface"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\service\\BaseAudioProcessorService.kt": ["onCreate:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService", "BaseAudioProcessorService:me.timschnee<PERSON>.rootlessjamesdsp.service", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService", "activeServices:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService", "service:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService.LocalBinder", "onBind:me.timschneeberger.rootlessjamesdsp.service.BaseAudioProcessorService", "onDestroy:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService.LocalBinder", "LocalBinder:me.timschnee<PERSON>.rootlessjamesdsp.service.BaseAudioProcessorService"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\DialogFilelibraryBinding.java": ["parentPanel:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "message:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "DialogFilelibraryBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "textSpacerNoButtons:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "scrollView:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "tags:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "scrollIndicatorDown:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "textSpacerNoTitle:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding", "contentPanel:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogFilelibraryBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceIconBinding.java": ["icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding", "PreferenceIconBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "summary:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceIconBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\CompanderSurface.kt": ["SCALE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.CompanderSurface.Companion", "CompanderSurface:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "computeCurve:me.timschnee<PERSON>.rootlessjamesdsp.view.CompanderSurface", "frequencyScale:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.CompanderSurface", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.CompanderSurface", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.view.CompanderSurface.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\CompatExtensions.kt": ["CompatExtensions:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "getInstalledApplicationsCompat:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions", "getPackageInfoCompat:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions", "getApplicationInfoCompat:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions", "getSerializableAs:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions", "getParcelableAs:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.CompatExtensions"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceMaterialswitchBinding.java": ["inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialswitchBinding", "switchWidget:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialswitchBinding", "PreferenceMaterialswitchBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialswitchBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialswitchBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage4Binding.java": ["OnboardingPage4Binding:me.timschnee<PERSON>.rootlessjamesdsp.databinding", "step6:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step5b:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step4:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step2:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step3:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step1:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "step5cOptional:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding", "inflate:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage4Binding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\MainApplication.kt": ["mediaProjectionStartIntent:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.MainApplication.CrashReportingTree", "log:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication.CrashReportingTree", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "onCreate:me.tims<PERSON><PERSON>.rootlessjamesdsp.MainApplication", "rootSessionDatabase:me.tims<PERSON><PERSON>.rootlessjamesdsp.MainApplication", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.MainApplication", "engineSampleRate:me.timschnee<PERSON>.rootlessjamesdsp.MainApplication", "profileManager:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication.Companion", "instance:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication.Companion", "onTerminate:me.timschnee<PERSON>.rootlessjamesdsp.MainApplication", "onSharedPreferenceChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "onLowMemory:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "blockedAppRepository:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.MainApplication", "isEnhancedProcessing:me.timsch<PERSON><PERSON>.rootlessjamesdsp.MainApplication", "MainApplication:me.t<PERSON><PERSON>.rootlessjamesdsp", "isLegacyMode:me.tims<PERSON>.rootlessjamesdsp.MainApplication"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\AudioEffectExtensions.kt": ["setParameter:me.t<PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "AudioEffectExtensions:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions", "setParameterCharArray:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "setParameterCharBuffer:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "getParameterInt:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "setParameterFloatArray:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "setParameterImpulseResponseBuffer:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions", "setParameterIntArray:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.AudioEffectExtensions"], "src\\full\\java\\me\\timschneeberger\\rootlessjamesdsp\\flavor\\CrashlyticsImpl.kt": ["CrashlyticsImpl:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor", "setCollectionEnabled:me.timsch<PERSON><PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl", "sendUnsentReports:me.timschnee<PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl", "recordException:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl", "setCustomKey:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl", "log:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor.CrashlyticsImpl"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\MaterialSeekbarPreference.kt": ["mLabelMinWidth:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "isAdjustable:me.t<PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setAdjustable:me.t<PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setUpdatesContinuously:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mAdjustable:me.t<PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mUpdatesContinuously:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mPrecision:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mUnit:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "MaterialSeekbarPreference:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference", "valueLabelOverride:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setShowSeekBarValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setMax:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getUpdatesContinuously:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getMin:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getShowSeekBarValue:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setSeekBarIncrement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mTrackingTouch:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "onBindViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mSeekBar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "onGetDefaultValue:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mMin:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "updateLabelValue:me.timschnee<PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getSeekBarIncrement:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "getMax:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "mSeekBarValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "syncValueInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "setMin:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference", "onSetInitialValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.MaterialSeekbarPreference"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage6Binding.java": ["startRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage6Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage6Binding", "OnboardingPage6Binding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage6Binding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage6Binding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\DspFragment.kt": ["onCreateView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "restartFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "DspFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "setUpdateCardVisible:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "setUpdateCardOnClick:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment.Companion", "setUpdateCardOnCloseClick:me.timschnee<PERSON>.rootlessjamesdsp.fragment.DspFragment", "setUpdateCardTitle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment.Companion", "onSharedPreferenceChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DspFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\EqualizerDialogFragment.kt": ["onDialogClosed:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment.Companion", "onBindDialogView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment.Companion", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.EqualizerDialogFragment", "EqualizerDialogFragment:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\rootless\\SessionRecordingPolicyEntry.kt": ["packageName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.SessionRecordingPolicyEntry", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.SessionRecordingPolicyEntry", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.SessionRecordingPolicyEntry", "SessionRecordingPolicyEntry:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless", "isRestricted:me.timschnee<PERSON>.rootlessjamesdsp.model.rootless.SessionRecordingPolicyEntry"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\FileLibraryPreference.kt": ["types:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "hasPresetExtension:me.tims<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "createFullPathCompat:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "FileLibraryPreference:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "hasLiveprogExtension:me.t<PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "isIrs:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "onClick:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "isVdc:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "isLiveprog:me.tims<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "type:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "showDialog:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "hasValidContent:me.tims<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "hasIrsExtension:me.tims<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "createFullPathNullCompat:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "refresh:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "onSetInitialValue:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "isPreset:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "directory:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "onGetDefaultValue:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "hasVdcExtension:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference.Companion", "hasCorrectExtension:me.timsch<PERSON><PERSON>.rootlessjamesdsp.preference.FileLibraryPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\ThemesPreference.kt": ["onBindViewHolder:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.ThemesPreference", "entries:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.ThemesPreference", "lastScrollPosition:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.ThemesPreference", "ThemesPreference:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference", "onItemClick:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.ThemesPreference", "onClick:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.ThemesPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\EelListProperty.kt": ["definitionRegex:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelListProperty.Companion", "findVariable:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty.Companion", "manipulateProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty", "parse:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty.Companion", "replaceVariable:me.t<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty.Companion", "valueAsString:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelListProperty", "options:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelListProperty", "EelListProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\DeviceProfilesCardFragment.kt": ["onCreatePreferences:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "onRoutingDeviceChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "DeviceProfilesCardFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "onCreateAdapter:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "onConfigurationChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "onCreateRecyclerView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.DeviceProfilesCardFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\utils\\AudioFlingerServiceDumpUtils.kt": ["pid:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils.Dataset", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils", "AudioFlingerServiceDumpUtils:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils", "sid:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils.Dataset", "dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils", "Dataset:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils", "toString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils.Dataset", "TARGET_SERVICE:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils", "uid:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils.Dataset", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.AudioFlingerServiceDumpUtils"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\service\\RootlessAudioProcessorService.kt": ["EXTRA_APP_UID:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "stopRecording:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "ACTION_STOP:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "stop:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "start:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "onDestroy:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "RootlessAudioProcessorService:me.timschnee<PERSON>.rootlessjamesdsp.service", "onCreate:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "ACTION_START:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "onStartCommand:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "EXTRA_MEDIA_PROJECTION_DATA:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "requestAudioRecordRecreation:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "restartRecording:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService", "SESSION_LOSS_MAX_RETRIES:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion", "EXTRA_APP_COMPAT_INTERNAL_CALL:me.timschnee<PERSON>.rootlessjamesdsp.service.RootlessAudioProcessorService.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\LiveprogParamsActivity.kt": ["setResetVisible:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity.Companion", "EXTRA_TARGET_FILE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity.Companion", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "onCreateOptionsMenu:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "setResetEnabled:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "onOptionsItemSelected:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogParamsActivity", "LiveprogParamsActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsBaseFragment.kt": ["SettingsBaseFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBaseFragment", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsBaseFragment", "onCreateView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBaseFragment", "app:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBaseFragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemEditorAutocompleteBinding.java": ["getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding", "ItemEditorAutocompleteBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "codeType:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding", "codeTitle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding", "inflate:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding", "codeContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemEditorAutocompleteBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\preference\\ThemeMode.kt": ["<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.preference.ThemeMode.Light", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.ThemeMode.FollowSystem", "ThemeMode:<PERSON>.t<PERSON>.rootlessjamesdsp.model.preference", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode.Dark", "Dark:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode", "Companion:<PERSON>.t<PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode", "Light:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode", "fromInt:me.t<PERSON>.rootlessjamesdsp.model.preference.ThemeMode.Companion", "FollowSystem:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.ThemeMode"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\api\\AeqSearchResult.kt": ["name:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.api.AeqSearchResult", "source:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.api.AeqSearchResult", "AeqSearchResult:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.api", "id:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.api.AeqSearchResult", "rank:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.api.AeqSearchResult"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemGeqNodeListBinding.java": ["freq:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding", "gain:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding", "ItemGeqNodeListBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "delete:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemGeqNodeListBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\delegates\\ThemingDelegate.kt": ["ThemingDelegate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates", "getThemeResIds:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates.ThemingDelegate.Companion", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates.ThemingDelegate.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.delegates.ThemingDelegateImpl", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates.ThemingDelegate", "applyAppTheme:me.timsch<PERSON><PERSON>.rootlessjamesdsp.delegates.ThemingDelegateImpl", "ThemingDelegateImpl:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates", "applyAppTheme:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.delegates.ThemingDelegate"], "src\\rootless\\java\\me\\timschneeberger\\rootlessjamesdsp\\flavor\\RootShellImpl.kt": ["RootShellImpl:me.timschnee<PERSON>.rootlessjamesdsp.flavor", "OnShellAttachedCallback:me.timschnee<PERSON>.rootlessjamesdsp.flavor.RootShellImpl", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.flavor.RootShellImpl", "cmd:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor.RootShellImpl", "getShell:me.timschnee<PERSON>.rootlessjamesdsp.flavor.RootShellImpl", "onShellAttached:me.timschnee<PERSON>.rootlessjamesdsp.flavor.RootShellImpl.OnShellAttachedCallback"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\AudioServiceDump.kt": ["toString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.AudioServiceDump", "AudioServiceDump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data", "sessions:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.data.AudioServiceDump"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceGraphicEqualizerBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceGraphicEqualizerBinding", "layoutEqualizer:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceGraphicEqualizerBinding", "PreferenceGraphicEqualizerBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "nodeCount:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceGraphicEqualizerBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceGraphicEqualizerBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceGraphicEqualizerBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\AppsListAdapter.kt": ["AppsListAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "getItemCount:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "getFilter:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "dataList:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "setOnItemClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "onCreateViewHolder:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "OnItemClickListener:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "ViewHolder:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "data:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter.ViewHolder", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter", "onItemClick:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter.OnItemClickListener", "onClick:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppsListAdapter.ViewHolder"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentDspBinding.java": ["dspScrollview:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "FragmentDspBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "cardContainer:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardDdc:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardGeq:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardEq:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardOutputControl:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "translationNotice:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardLiveprog:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardCompressor:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "updateNotice:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardCrossfeed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardConvolver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardBass:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardDeviceProfiles:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardReverb:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardStereowide:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding", "cardTube:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentDspBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\storage\\Cache.kt": ["delete:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "getReleaseUri:me.tims<PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache", "onCreate:me.tims<PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "Companion:<PERSON>.t<PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider.Companion", "Cache:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage", "getReleaseFile:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache", "update:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "Provider:me.t<PERSON>.rootlessjamesdsp.utils.storage.Cache", "openFile:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "getType:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "cleanup:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache", "getTemporaryFile:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.Cache", "query:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "cleanupNow:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.Cache", "insert:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.Cache.Provider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\service\\RootAudioProcessorService.kt": ["onSharedPreferenceChanged:me.timschneeberger.rootlessjamesdsp.service.RootAudioProcessorService", "stopService:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService", "onDestroy:me.timschneeberger.rootlessjamesdsp.service.RootAudioProcessorService", "startService:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.service.RootAudioProcessorService", "ACTION_STOP:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "onCreate:me.timschneeberger.rootlessjamesdsp.service.RootAudioProcessorService", "onStartCommand:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService", "ACTION_START_ENHANCED_PROCESSING:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "updateLegacyMode:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "onSessionChanged:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService", "startServiceEnhanced:me.timschnee<PERSON>.rootlessjamesdsp.service.RootAudioProcessorService.Companion", "RootAudioProcessorService:me.timschnee<PERSON>.rootlessjamesdsp.service"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\service\\QuickTileService.kt": ["onStartListening:me.timschnee<PERSON>.rootlessjamesdsp.service.QuickTileService", "onSharedPreferenceChanged:me.timschnee<PERSON>.rootlessjamesdsp.service.QuickTileService", "QuickTileService:me.timsch<PERSON><PERSON>.rootlessjamesdsp.service", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.QuickTileService", "onClick:me.timschneeberger.rootlessjamesdsp.service.QuickTileService", "onStopListening:me.timschnee<PERSON>.rootlessjamesdsp.service.QuickTileService"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\IDump.kt": ["toString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.IDump", "IDump:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.data"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\EelParser.kt": ["restoreDefaults:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "load:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "hasDefaults:me.tims<PERSON>.rootlessjamesdsp.liveprog.EelParser", "canLoadDefaults:me.timsch<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "hasDescription:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "save:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "manipulateProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "refresh:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "findAnnotationLine:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "isFileLoaded:me.tims<PERSON>.rootlessjamesdsp.liveprog.EelParser", "properties:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "fileName:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelParser", "contents:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelParser", "description:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog", "path:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "parse:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser", "tags:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelParser"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\backup\\BackupRestoreService.kt": ["BackupRestoreService:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService.Companion", "start:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupRestoreService.Companion", "onDestroy:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "onBind:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "stopService:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "onStartCommand:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupRestoreService", "isRunning:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService.Companion", "onCreate:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupRestoreService"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\AudioFlingerServiceDumpProvider.kt": ["TARGET_SERVICE:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider.Companion", "dump:me.timschneeberger.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider", "AudioFlingerServiceDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider.Companion", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioFlingerServiceDumpProvider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\ApiExtensions.kt": ["Finished:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState", "error:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState.Failed", "DownloadState:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions", "save:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions", "Failed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState", "Downloading:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState", "currentBytes:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState.Downloading", "ApiExtensions:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "totalBytes:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState.Downloading", "file:me.timschneeberger.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState.Finished", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions", "progress:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ApiExtensions.DownloadState.Downloading"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\EngineLauncherActivity.kt": ["disableAppTheme:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.EngineLauncherActivity", "onCreate:me.tims<PERSON>.rootlessjamesdsp.activity.EngineLauncherActivity", "EngineLauncherActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.activity.EngineLauncherActivity"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivitySettingsBinding.java": ["settings:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivitySettingsBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivitySettingsBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivitySettingsBinding", "settingsToolbar:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivitySettingsBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivitySettingsBinding", "ActivitySettingsBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\AppInfo.kt": ["AppInfo:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "appName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AppInfo", "icon:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AppInfo", "toString:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.AppInfo", "isSystem:me.timschnee<PERSON>.rootlessjamesdsp.model.AppInfo", "packageName:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.AppInfo", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AppInfo"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\Translator.kt": ["name:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator.Language", "translated:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "readAll:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator.Companion", "name:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "approved:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "username:me.timschnee<PERSON>.rootlessjamesdsp.model.Translator", "id:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "languages:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "Translator:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "picture:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "id:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator.Language", "readLanguageMap:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator.Companion", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator", "Language:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.Translator"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\OnboardingFragment.kt": ["<PERSON><PERSON><PERSON>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods", "Adb:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.OnRequestPermissionResult", "OnboardingFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onViewCreated:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "SHIZUKU_PKG:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods.Shizuku", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "PAGE_LIMITATIONS:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "onRequestPermissionResult:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.OnRequestPermissionResult", "PAGE_READY:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "onDestroyView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "REQUEST_CODE_SHIZUKU_GRANT:me.timschnee<PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "onSaveInstanceState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "PAGE_ADB_SETUP:me.timschnee<PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "PAGE_WELCOME:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods.Adb", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "None:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods", "onCreateView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "onBackPressed:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment", "PAGE_RUNTIME_PERMISSIONS:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "PAGE_METHOD_SELECT:me.timschnee<PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.SetupMethods.None", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.OnboardingFragment.Companion"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemAppListBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding", "ItemAppListBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "summary:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAppListBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\CompanderDialogFragment.kt": ["<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "onDialogClosed:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "CompanderDialogFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onBindDialogView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment", "onSaveInstanceState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.CompanderDialogFragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentApplistSheetBinding.java": ["filter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "FragmentApplistSheetBinding:me.timschnee<PERSON>.rootlessjamesdsp.databinding", "recyclerview:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "root:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding", "loader:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentApplistSheetBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\PreferenceGroupFragment.kt": ["<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "onCreateRecyclerView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "onCreatePreferences:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "PreferenceGroupFragment:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment.Companion", "onCreateAdapter:me.t<PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "onDisplayPreferenceDialog:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment", "cloneInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.PreferenceGroupFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\widget\\SymbolInputView.kt": ["addSymbols:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView", "forEachButton:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView", "accept:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView.ButtonConsumer", "bindEditor:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView", "SymbolInputView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget", "setTextColor:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView", "removeSymbols:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView", "ButtonConsumer:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.widget.SymbolInputView"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsDeviceProfilesFragment.kt": ["SettingsDeviceProfilesFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsDeviceProfilesFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\AudioPolicyServiceDump.kt": ["capturePermissionLog:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.AudioPolicyServiceDump", "AudioPolicyServiceDump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data", "toString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.AudioPolicyServiceDump", "sessions:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.data.AudioPolicyServiceDump"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\CompanderPreference.kt": ["onSetInitialValue:me.t<PERSON>.rootlessjamesdsp.preference.CompanderPreference", "updateFromPreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.CompanderPreference", "onBindViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.CompanderPreference", "CompanderPreference:me.t<PERSON>.rootlessjamesdsp.preference", "onGetDefaultValue:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.CompanderPreference", "initialValue:me.tims<PERSON>.rootlessjamesdsp.preference.CompanderPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\Result.kt": ["Success:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.Result", "Loading:me.t<PERSON>.rootlessjamesdsp.utils.Result", "exception:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.Result.Error", "data:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.Result.Success", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.Result.Loading", "asResult:me.tims<PERSON>.rootlessjamesdsp.utils", "Error:me.t<PERSON>.rootlessjamesdsp.utils.Result", "data:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.Result.Error", "Result:me.tims<PERSON>.rootlessjamesdsp.utils"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\structure\\EelVmVariable.kt": ["name:me.tims<PERSON><PERSON>.rootlessjamesdsp.interop.structure.EelVmVariable", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.structure.EelVmVariable", "EelVmVariable:me.tims<PERSON>.rootlessjamesdsp.interop.structure", "isString:me.tims<PERSON>.rootlessjamesdsp.interop.structure.EelVmVariable"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\preferences\\NonPersistentDatastore.kt": ["OnPreferenceChanged:me.tims<PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore", "NonPersistentDatastore:me.t<PERSON>.rootlessjamesdsp.utils.preferences", "putFloat:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore", "putString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore", "onFloatPreferenceChanged:me.tims<PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore.OnPreferenceChanged", "setOnPreferenceChanged:me.tims<PERSON><PERSON>.rootlessjamesdsp.utils.preferences.NonPersistentDatastore"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\preferences\\Preferences.kt": ["getDefault:me.timschnee<PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.preferences.Preferences.Var", "registerOnSharedPreferenceChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "App:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.preferences.Preferences.App", "namespace:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "preferences:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "unregisterOnSharedPreferenceChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "namespace:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.App", "Preferences:me.t<PERSON>.rootlessjamesdsp.utils.preferences", "set:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "reset:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "namespace:me.t<PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.Var", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences", "context:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences", "Var:me.t<PERSON>.rootlessjamesdsp.utils.preferences.Preferences", "AbstractPreferences:me.t<PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences", "get:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.preferences.Preferences.AbstractPreferences"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\preference\\SessionUpdateMode.kt": ["fromInt:me.tims<PERSON><PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode.Companion", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode.Listener", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode.ContinuousPolling", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode", "SessionUpdateMode:me.t<PERSON><PERSON>.rootlessjamesdsp.model.preference", "ContinuousPolling:me.t<PERSON><PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode.Companion", "Listener:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.SessionUpdateMode"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingControlsBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "controlsLayout:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "nextButton:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "backButton:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingControlsBinding", "OnboardingControlsBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\AudioPolicyServiceDumpProvider.kt": ["TARGET_SERVICE:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider.Companion", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider.Companion", "AudioPolicyServiceDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider", "dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioPolicyServiceDumpProvider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\receiver\\PowerStateReceiver.kt": ["EXTRA_ENABLED:me.timschnee<PERSON>.rootlessjamesdsp.receiver.PowerStateReceiver.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.receiver.PowerStateReceiver.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.receiver.PowerStateReceiver", "onReceive:me.timschnee<PERSON>.rootlessjamesdsp.receiver.PowerStateReceiver", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.receiver.PowerStateReceiver", "PowerStateReceiver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.receiver"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceThemeItemBinding.java": ["PreferenceThemeItemBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "name:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "bottomNavUnselectedItem:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "topNavText:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "bottomNav:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "topNav:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "bottomNavSelectedItem:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "centerGuideline:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "coverContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "badges:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding", "themeCard:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemeItemBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityAppCompatibilityBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAppCompatibilityBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityAppCompatibilityBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAppCompatibilityBinding", "fragment:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAppCompatibilityBinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ActivityAppCompatibilityBinding", "ActivityAppCompatibilityBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\JamesDspWrapper.kt": ["setMultiEqualizer:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "eelErrorCodeToString:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "alloc:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "onVdcParseError:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper.JamesDspCallbacks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "free:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "loadBenchmark:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "processInt8U24:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setSamplingRate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "JamesDspCallbacks:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "onLiveprogResult:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper.JamesDspCallbacks", "setBassBoost:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "isHandleValid:me.tims<PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "onConvolverParseError:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspWrapper.JamesDspCallbacks", "onLiveprogExec:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper.JamesDspCallbacks", "freezeLiveprogExecution:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setPostGain:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setStereoEnhancement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "processFloat:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "processInt16:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setCompander:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "enumerateEelVariables:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setVdc:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "processInt32:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "runBenchmark:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setLimiter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setReverb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setLiveprog:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setGraphicEq:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "processInt24Packed:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setVacuumTube:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "onLiveprogOutput:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper.JamesDspCallbacks", "manipulateEelVariable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "JamesDspWrapper:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "getBenchmarkSize:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setCrossfeed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper", "setConvolver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspWrapper"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\SdkCheck.kt": ["isVanillaIceCream:me.tims<PERSON>.rootlessjamesdsp.utils.SdkCheck", "isSnowCake:me.timsch<PERSON>.rootlessjamesdsp.utils.SdkCheck", "SdkCheck:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils", "SdkCheckElseBranch:me.timsch<PERSON>.rootlessjamesdsp.utils", "below:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.SdkCheckElseBranch", "sdkAbove:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils", "isPie:me.tims<PERSON>.rootlessjamesdsp.utils.SdkCheck", "valueOrNull:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.SdkCheckElseBranch", "isQ:me.tims<PERSON>.rootlessjamesdsp.utils.SdkCheck", "isUpsideDownCake:me.timschnee<PERSON>.rootlessjamesdsp.utils.SdkCheck", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.SdkCheck", "isTiramisu:me.tims<PERSON>.rootlessjamesdsp.utils.SdkCheck"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceCompanderDialogBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderDialogBinding", "companderSurface:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderDialogBinding", "PreferenceCompanderDialogBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderDialogBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderDialogBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\IconPreference.kt": ["IconPreference:me.t<PERSON>.rootlessjamesdsp.preference", "attrs:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.IconPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\rootless\\MutedEffectSession.kt": ["uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.MutedEffectSession", "packageName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.MutedEffectSession", "audioMuteEffect:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.MutedEffectSession", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless.MutedEffectSession", "MutedEffectSession:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.rootless"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\syntax\\Constant.kt": ["codeTitle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.Constant", "Constant:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax", "codeBody:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.Constant", "codePrefix:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.Constant"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\shared\\BaseSessionDatabase.kt": ["onSessionChanged:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase.OnSessionChangeListener", "clearSessions:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "destroy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "excludedPackages:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "createSession:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "sessionList:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "unregisterOnSessionChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "setExcludedUids:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "OnSessionChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "update:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "shouldAcceptSessionDump:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "addSession:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "BaseSessionDatabase:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared", "removeSession:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "registerOnSessionChangeListener:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "onSessionRemoved:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase", "shouldAddSession:me.timschnee<PERSON>.rootlessjamesdsp.session.shared.BaseSessionDatabase"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\PackageServiceDumpProvider.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider", "PackageServiceDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider", "TARGET_SERVICE:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.PackageServiceDumpProvider.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\AudioServiceDumpProvider.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider", "dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider", "AudioServiceDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "TARGET_SERVICE:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.AudioServiceDumpProvider.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsAudioFormatFragment.kt": ["Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment", "SettingsAudioFormatFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAudioFormatFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\ProcessorMessageHandler.kt": ["onLiveprogExec:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler", "onVdcParseError:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler", "ProcessorMessageHandler:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "onLiveprogOutput:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler", "onConvolverParseError:me.timschnee<PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler", "onLiveprogResult:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.ProcessorMessageHandler"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\DialogProgressBinding.java": ["alertTitle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "progressPercent:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "progressNumber:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding", "DialogProgressBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "progress:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogProgressBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\EelBaseProperty.kt": ["manipulateProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "key:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "EelBaseProperty:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog", "valueAsString:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "hasDefault:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "isDefault:me.tims<PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "restoreDefaults:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty", "description:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelBaseProperty"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\GraphicEqualizerFragment.kt": ["onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "STATE_NODES:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "STATE_EDITOR_NODE_BACKUP:me.timschnee<PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "onStop:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "STATE_EDITOR_UI_GAIN_INPUT:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "<init>:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "STATE_EDITOR_NODE_UUID:me.timschnee<PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "onConfigurationChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "onCreateView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "GraphicEqualizerFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "STATE_EDITOR_UI_FREQ_INPUT:me.timschnee<PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion", "onSaveInstanceState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment", "STATE_EDITOR_ACTIVE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.GraphicEqualizerFragment.Companion"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceThemesListBinding.java": ["getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemesListBinding", "PreferenceThemesListBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemesListBinding", "themesList:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemesListBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemesListBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceThemesListBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\CustomCodeViewAdapter.kt": ["getView:me.timschnee<PERSON>.rootlessjamesdsp.adapter.CustomCodeViewAdapter", "CustomCodeViewAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\backup\\BackupNotifier.kt": ["showRestoreComplete:me.timsch<PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "showBackupComplete:me.timsch<PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "showBackupProgress:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "showRestoreError:me.timsch<PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "showBackupError:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "showRestoreProgress:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupNotifier", "BackupNotifier:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\AutoEqResultAdapter.kt": ["title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter.AutoEqResultViewHolder", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter", "onClickListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter", "AutoEqResultAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "container:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter.AutoEqResultViewHolder", "onCreateViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter", "results:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter", "AutoEqResultViewHolder:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter", "subtitle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter.AutoEqResultViewHolder", "getItemCount:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AutoEqResultAdapter"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistDatabase.kt": ["appBlocklistDao:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase", "getDatabase:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase", "AppBlocklistDatabase:me.timschnee<PERSON>.rootlessjamesdsp.model.room"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\FileLibraryDialogFragment.kt": ["allItems:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "getCount:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "onCreateDialog:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.Companion", "onCreateView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "onFiltered:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "getItemId:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "allowFilter:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "hasStableIds:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.Entry", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "Entry:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "onPrepareDialogBuilder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "name:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.Entry", "indexOf:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "onDialogClosed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "getFilter:me.timschnee<PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.Entry", "findPreference:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment", "getItem:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.FileLibraryDialogFragment.ListItemAdapter", "FileLibraryDialogFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingFragmentBinding.java": ["OnboardingFragmentBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "adbSetup:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "otherPerms:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage1:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "controlsContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage2:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "methodSelect:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage3:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage4:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage5:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage6:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding", "onboardingPage7:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingFragmentBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityGraphicEqBinding.java": ["params:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityGraphicEqBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityGraphicEqBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityGraphicEqBinding", "ActivityGraphicEqBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityGraphicEqBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityGraphicEqBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceEqualizerDialogBinding.java": ["equalizerPresets:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerDialogBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerDialogBinding", "equalizerSurface:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerDialogBinding", "PreferenceEqualizerDialogBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerDialogBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerDialogBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\IEffectSession.kt": ["IEffectSession:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.IEffectSession", "packageName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.IEffectSession"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\Constants.kt": ["ACTION_SERVICE_STARTED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_LIVEPROG:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "DEFAULT_CONVOLVER_ADVIMP:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_SERVICE_SOFT_REBOOT_CORE:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_STEREOWIDE:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_CONVOLVER:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_DISCARD_AUTHORIZATION:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "DEFAULT_GEQ_INTERNAL:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_REPORT_SAMPLE_RATE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "PREF_COMPANDER:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_OUTPUT:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_TUBE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_SERVICE_RELOAD_LIVEPROG:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_BASS:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_PROCESSOR_MESSAGE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "PREF_REVERB:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_APP:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_CROSSFEED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "EXTRA_SAMPLE_RATE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "PREF_DDC:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_EQ:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.Constants", "DEFAULT_EQ:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_PREFERENCES_UPDATED:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_SESSION_CHANGED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "Constants:me.t<PERSON>.rootlessjamesdsp.utils", "ACTION_SERVICE_HARD_REBOOT_CORE:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_GRAPHIC_EQ_CHANGED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_PRESET_LOADED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "PREF_VAR:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_SERVICE_STOPPED:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_BACKUP_RESTORED:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "PREF_GEQ:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants", "ACTION_SAMPLE_RATE_UPDATED:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.Constants", "DEFAULT_GEQ:me.timschnee<PERSON>.rootlessjamesdsp.utils.Constants"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\NotificationExtensions.kt": ["buildNotificationChannelGroup:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions", "buildNotificationChannel:me.tims<PERSON>.rootlessjamesdsp.utils.extensions"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsAppearanceFragment.kt": ["SettingsAppearanceFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment.Companion", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment.Companion", "onSaveInstanceState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsAppearanceFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\BlockedApp.kt": ["appName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.BlockedApp", "appIcon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.BlockedApp", "BlockedApp:me.timschnee<PERSON>.rootlessjamesdsp.model.room", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.BlockedApp", "packageName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.BlockedApp"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemPresetListBinding.java": ["text1:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemPresetListBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemPresetListBinding", "ItemPresetListBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemPresetListBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemPresetListBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityDspMainBinding.java": ["getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "powerToggle:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "appBarLayout:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "ActivityDspMainBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "leftMenu:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "bar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityDspMainBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\ISessionPolicyDumpProvider.kt": ["dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.ISessionPolicyDumpProvider", "dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.ISessionPolicyDumpProvider", "ISessionPolicyDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\root\\RemoteEffectSession.kt": ["effect:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.root.RemoteEffectSession", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.root.RemoteEffectSession", "packageName:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.root.RemoteEffectSession", "RemoteEffectSession:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.root", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.root.RemoteEffectSession"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\ISessionDumpProvider.kt": ["dump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.ISessionDumpProvider", "ISessionDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.ISessionDumpProvider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\Card.kt": ["Card:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "buttonEnabled:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "titleText:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "buttonText:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "iconSrc:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "onDetachedFromWindow:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "cardBackground:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "closeButtonVisible:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "bodyText:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "checkboxVisible:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card", "checkboxIsChecked:me.timschnee<PERSON>.rootlessjamesdsp.view.Card", "setOnCloseClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "setOnRootClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "onAttachedToWindow:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "setOnCheckChangedListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "setClickable:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "setOnButtonClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.Card", "iconTint:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.Card"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\MutedAudioEffectFactory.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams.MUTE", "ENABLESTEREOPOSITION:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams.STEREOPOSITION", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams.ENABLESTEREOPOSITION", "Companion:<PERSON>.t<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory", "make:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.MuteEffects.Volume", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams.MAXLEVEL", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.MuteEffects.DynamicsProcessing", "MuteEffects:me.tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion", "VolumeParams:<PERSON><PERSON>t<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion", "LEVEL:me.tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams", "MAXLEVEL:me.tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion", "MutedAudioEffectFactory:me.tims<PERSON><PERSON>.rootlessjamesdsp.utils", "isDeviceCompatible:me.tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion", "STEREOPOSITION:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams", "DynamicsProcessing:<PERSON><PERSON>tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.MuteEffects", "Volume:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.MuteEffects", "MUTE:me.tims<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory", "sessionLossListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.MutedAudioEffectFactory.Companion.VolumeParams.LEVEL"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemBlockedAppListBinding.java": ["getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding", "ItemBlockedAppListBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding", "summary:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemBlockedAppListBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\DropDownPreference.kt": ["onMenuItemClick:me.timschnee<PERSON>.rootlessjamesdsp.preference.DropDownPreference", "setValueIndex:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.DropDownPreference", "DropDownPreference:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference", "onBindViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.DropDownPreference", "setValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.DropDownPreference", "onClick:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.DropDownPreference", "isStatic:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.preference.DropDownPreference", "setEntries:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.DropDownPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\receiver\\BootCompletedReceiver.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.receiver.BootCompletedReceiver", "BootCompletedReceiver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.receiver", "onReceive:me.timschnee<PERSON>.rootlessjamesdsp.receiver.BootCompletedReceiver"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\ISessionPolicyInfoDump.kt": ["ISessionPolicyInfoDump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data", "capturePermissionLog:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data.ISessionPolicyInfoDump"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage3Binding.java": ["methodsShizukuTitle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "inflate:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsShizukuBody:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsAdbBody:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsRootCard:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsShizukuCard:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsAdbCard:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "methodsRootTitle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "OnboardingPage3Binding:me.timschnee<PERSON>.rootlessjamesdsp.databinding", "methodsRootBody:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage3Binding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\storage\\Tar.kt": ["Composer:<PERSON><PERSON>t<PERSON>.rootlessjamesdsp.utils.storage.Tar", "Reader:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Tar", "validate:me.t<PERSON>.rootlessjamesdsp.utils.storage.Tar.Reader", "metadata:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Tar.Composer", "extract:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Tar.Reader", "add:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Tar.Composer", "close:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage.Tar.Composer", "Tar:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.storage", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.Tar"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsBackupFragment.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "SettingsBackupFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "startManualBackup:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "updateSummaries:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "startManualRestore:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment", "resetFrequencyIfLocationUnset:me.timsch<PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsBackupFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\rootless\\RootlessSessionDatabase.kt": ["onSessionRemoved:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "RootlessSessionDatabase:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.rootless", "shouldAcceptSessionDump:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "OnRootlessSessionChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless", "shouldAddSession:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "OnSessionLossListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "onSessionLost:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase.OnSessionLossListener", "EXTRA_IGNORE:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase.Companion", "OnAppProblemListener:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "setOnAppProblemListener:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "setOnSessionLossListener:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "excludedPackages:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase.Companion", "createSession:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase", "onAppProblemDetected:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionDatabase.OnAppProblemListener"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage2Binding.java": ["inflate:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage2Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage2Binding", "header:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage2Binding", "OnboardingPage2Binding:me.timschnee<PERSON>.rootlessjamesdsp.databinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage2Binding", "notice:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage2Binding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityLiveprogParamsBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogParamsBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogParamsBinding", "ActivityLiveprogParamsBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogParamsBinding", "params:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogParamsBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogParamsBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\receiver\\SessionReceiver.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.receiver.SessionReceiver", "SessionReceiver:<PERSON>.tims<PERSON><PERSON>.rootlessjamesdsp.receiver", "onReceive:me.timschnee<PERSON>.rootlessjamesdsp.receiver.SessionReceiver"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage1Binding.java": ["getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage1Binding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage1Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage1Binding", "OnboardingPage1Binding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "startRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage1Binding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\AeqSelectorActivity.kt": ["onSaveInstanceState:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AeqSelectorActivity", "AeqSelectorActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AeqSelectorActivity.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AeqSelectorActivity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.AeqSelectorActivity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AeqSelectorActivity"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\DialogTextinputBinding.java": ["text1:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogTextinputBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogTextinputBinding", "textInputLayout:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogTextinputBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogTextinputBinding", "DialogTextinputBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.DialogTextinputBinding"], "src\\rootless\\java\\me\\timschneeberger\\rootlessjamesdsp\\flavor\\UpdateManager.kt": ["context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.flavor.UpdateManager", "installUpdate:me.timschnee<PERSON>.rootlessjamesdsp.flavor.UpdateManager", "UpdateManager:me.tims<PERSON><PERSON>.rootlessjamesdsp.flavor", "isUpdateAvailable:me.timschnee<PERSON>.rootlessjamesdsp.flavor.UpdateManager", "getUpdateVersionInfo:me.timschneeberger.rootlessjamesdsp.flavor.UpdateManager"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityBlocklistBinding.java": ["ActivityBlocklistBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding", "blocklistHost:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding", "fab:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityBlocklistBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\plugin\\UndoRedoManager.kt": ["NOT_DEF:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType", "onTextChanged:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.TextChangeWatcher", "current:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "PASTE:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType", "beforeTextChanged:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.TextChangeWatcher", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.TextChangeWatcher", "setMaxHistorySize:me.timsch<PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "add:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "INSERT:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType", "UndoRedoManager:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin", "afterTextChanged:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.TextChangeWatcher", "setMaxHistorySize:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "clearHistory:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType.PASTE", "actionType:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.TextChangeWatcher", "canUndo:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "position:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "historyList:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "clear:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType.INSERT", "canRedo:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "next:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType.DELETE", "after:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditNode", "DELETE:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType", "previous:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditHistory", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.ActionType.NOT_DEF", "undo:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "connect:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "disconnect:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager", "start:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditNode", "before:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager.EditNode", "redo:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.plugin.UndoRedoManager"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ViewCardBinding.java": ["closeButtonLayout:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "text:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "ViewCardBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "checkbox:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "close:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding", "button:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewCardBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\EngineUtils.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.EngineUtils", "EngineUtils:me.timschnee<PERSON>.rootlessjamesdsp.utils", "toggleEnginePower:me.timschnee<PERSON>.rootlessjamesdsp.utils.EngineUtils"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\SwitchPreferenceGroup.kt": ["setValue:me.t<PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "SwitchPreferenceGroup:me.t<PERSON><PERSON>.rootlessjamesdsp.preference", "onSetInitialValue:me.t<PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "onPrepareAddPreference:me.t<PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "onGetDefaultValue:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "setIsIconVisible:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup.Companion", "onBindViewHolder:me.tims<PERSON>.rootlessjamesdsp.preference.SwitchPreferenceGroup"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\EqualizerSurface.kt": ["Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface", "iirOrder:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface", "computeCurve:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.view.EqualizerSurface", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface.Companion", "EqualizerSurface:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "Fir:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface.Mode", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.view.EqualizerSurface.Mode.Fir", "Iir:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface.Mode", "frequencyScale:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface", "SCALE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface.Companion", "Mode:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface", "mode:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.EqualizerSurface.Mode.Iir"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\PermissionExtensions.kt": ["hasRecordPermission:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "hasPackageUsagePermission:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "PermissionExtensions:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "hasNotificationPermission:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "hasProjectMediaAppOp:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "hasPackageUsageAppOp:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions", "hasDumpPermission:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.PermissionExtensions"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistViewModel.kt": ["AppBlocklistViewModel:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room", "create:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistViewModelFactory", "delete:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistViewModel", "blockedApps:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistViewModel", "AppBlocklistViewModelFactory:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room", "insert:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistViewModel"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistRepository.kt": ["AppBlocklistRepository:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room", "insert:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistRepository", "delete:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistRepository", "blocklist:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistRepository"], "build\\generated\\source\\buildConfig\\rootlessFull\\release\\me\\timschneeberger\\rootlessjamesdsp\\BuildConfig.java": ["COMMIT_COUNT:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "FLAVOR_version:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "VERSION_NAME:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "FOSS_ONLY:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "ROOTLESS:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "DEBUG:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "COMMIT_SHA:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "APPLICATION_ID:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "FLAVOR:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.BuildConfig", "BuildConfig:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp", "PLUGIN:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "PREVIEW:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "BUILD_TIME:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "VERSION_CODE:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "BUILD_TYPE:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig", "FLAVOR_dependencies:me.timschnee<PERSON>.rootlessjamesdsp.BuildConfig"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\IPropertyCompanion.kt": ["IPropertyCompanion:me.timsch<PERSON><PERSON>.rootlessjamesdsp.liveprog", "parse:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.IPropertyCompanion", "definitionRegex:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.IPropertyCompanion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\ItemViewModel.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ItemViewModel", "ItemViewModel:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "selectedItem:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ItemViewModel", "selectItem:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ItemViewModel"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\AppIconPreference.kt": ["onBindViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.AppIconPreference", "AppIconPreference:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\ProfileManager.kt": ["save:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile", "Companion:<PERSON>.t<PERSON>.rootlessjamesdsp.utils.ProfileManager", "finalize:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager", "delete:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager", "<init>:me.tims<PERSON>.rootlessjamesdsp.utils.ProfileManager.Companion", "id:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile", "name:me.tims<PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.ProfileManager", "ProfileManager:me.tims<PERSON>.rootlessjamesdsp.utils", "copy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager", "Profile:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager", "FILE_PROFILE:me.timschnee<PERSON>.rootlessjamesdsp.utils.ProfileManager.Companion", "onRoutingDeviceChanged:me.timsch<PERSON>.rootlessjamesdsp.utils.ProfileManager", "Companion:<PERSON>.t<PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile", "from:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile.Companion", "FILE_PROFILE_PRESET:me.timschnee<PERSON>.rootlessjamesdsp.utils.ProfileManager.Companion", "onReceive:me.tims<PERSON>.rootlessjamesdsp.utils.ProfileManager", "allProfiles:me.tims<PERSON>.rootlessjamesdsp.utils.ProfileManager", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile.Companion", "rotate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager", "group:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.ProfileManager.Profile"], "build\\generated\\ksp\\rootlessFullRelease\\java\\byRounds\\1\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistDatabase_Impl.java": ["createInvalidationTracker:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "getRequiredAutoMigrationSpecs:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "getAutoMigrations:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "appBlocklistDao:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "getRequiredTypeConverters:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "AppBlocklistDatabase_Impl:me.timschnee<PERSON>.rootlessjamesdsp.model.room", "createOpenHelper:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl", "clearAllTables:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDatabase_Impl"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityLiveprogEditorBinding.java": ["fileNameText:me.timschneeberger.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "ActivityLiveprogEditorBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "sourceInfoLayout:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "codeViewScroller:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "codeViewHorizScroller:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "sourcePositionTxt:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "toolbarFrame:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "codeView:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "codeFrame:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding", "symbolInput:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityLiveprogEditorBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\utils\\DumpUtils.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils.DumpUtils", "DumpUtils:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.utils", "dumpAll:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.session.dump.utils.DumpUtils", "dumpLines:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.dump.utils.DumpUtils"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\contract\\AutoEqSelectorContract.kt": ["AutoEqSelectorContract:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.contract", "parseResult:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract", "createIntent:me.timschnee<PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract", "EXTRA_RESULT:me.timschnee<PERSON>.rootlessjamesdsp.contract.AutoEqSelectorContract.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\notifications\\Notifications.kt": ["ID_SERVICE_STARTUP:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_BACKUP_COMPLETE:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_SERVICE_SESSION_LOSS:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_SERVICE_STATUS:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_BACKUP_RESTORE_PROGRESS:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_SERVICE_SESSION_LOSS:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_BACKUP_RESTORE_COMPLETE:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_SERVICE_STATUS:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_SERVICE_APPCOMPAT:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "Notifications:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications", "ID_RESTORE_COMPLETE:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_BACKUP_PROGRESS:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_SERVICE_APP_COMPAT:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "CHANNEL_SERVICE_STARTUP:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "ID_RESTORE_PROGRESS:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.Notifications", "createChannels:me.tims<PERSON>.rootlessjamesdsp.utils.notifications.Notifications"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceAltBinding.java": ["PreferenceAltBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding", "summary:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAltBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\OnboardingPage5Binding.java": ["OnboardingPage5Binding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "privacyCard:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage5Binding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.OnboardingPage5Binding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage5Binding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage5Binding", "onboardingNotificationPermission:me.timschnee<PERSON>.rootlessjamesdsp.databinding.OnboardingPage5Binding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\provider\\IDumpProvider.kt": ["IDumpProvider:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider", "dumpString:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.provider.IDumpProvider"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentLibraryLoadErrorBinding.java": ["getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.FragmentLibraryLoadErrorBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentLibraryLoadErrorBinding", "architectureNotice:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentLibraryLoadErrorBinding", "FragmentLibraryLoadErrorBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "rootlessNotice:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentLibraryLoadErrorBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentLibraryLoadErrorBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\RoundedRipplePreferenceGroupAdapter.kt": ["RoundedRipplePreferenceGroupAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.RoundedRipplePreferenceGroupAdapter"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\GraphicEqualizerActivity.kt": ["GraphicEqualizerActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.GraphicEqualizerActivity", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.GraphicEqualizerActivity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\api\\AutoEqClient.kt": ["AutoEqClient:me.timschnee<PERSON>.rootlessjamesdsp.api", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.api.AutoEqClient.Companion", "queryProfiles:me.timschnee<PERSON>.rootlessjamesdsp.api.AutoEqClient", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.api.AutoEqClient", "getProfile:me.timschnee<PERSON>.rootlessjamesdsp.api.AutoEqClient", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.api.AutoEqClient"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceEqualizerBinding.java": ["layoutEqualizer:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerBinding", "PreferenceEqualizerBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceEqualizerBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\BlocklistFragment.kt": ["showAppSelector:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment", "onCreateView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment", "onResume:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment.Companion", "BlocklistFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onDestroyView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.BlocklistFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\backup\\BackupManager.kt": ["<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupManager.Companion", "getBackupFilename:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupManager.Companion", "BackupManager:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.backup", "META_IS_BACKUP:me.timsch<PERSON><PERSON>.rootlessjamesdsp.backup.BackupManager.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupManager", "restoreBackup:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupManager", "createBackup:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupManager", "job:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\LimitationsFragment.kt": ["<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LimitationsFragment", "onCreateView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LimitationsFragment", "LimitationsFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LimitationsFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\plugin\\SourcePositionListener.kt": ["OnPositionChanged:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.SourcePositionListener", "setOnPositionChanged:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.SourcePositionListener", "onPositionChange:me.timschnee<PERSON>.rootlessjamesdsp.editor.plugin.SourcePositionListener.OnPositionChanged", "SourcePositionListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.editor.plugin"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\notifications\\ServiceNotificationHelper.kt": ["createAppTroubleshootIntent:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "pushSessionLossNotification:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "createStopIntent:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "ServiceNotificationHelper:me.tims<PERSON>.rootlessjamesdsp.utils.notifications", "pushServiceNotification:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "pushAppIssueNotification:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "pushServiceNotificationLegacy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "createServiceNotificationLegacy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "createServiceNotification:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "createStartIntent:me.timschnee<PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper", "pushPermissionPromptNotification:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.notifications.ServiceNotificationHelper"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\api\\UserAgentInterceptor.kt": ["UserAgentInterceptor:me.tims<PERSON><PERSON>.rootlessjamesdsp.api", "intercept:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.api.UserAgentInterceptor"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\EelNumberRangeProperty.kt": ["handleAsInt:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "default:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty.Companion", "minimum:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "definitionRegex:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty.Companion", "hasDefault:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "replaceVariable:me.tims<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty.Companion", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "validateRange:me.tims<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "valueAsString:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "maximum:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "findVariable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty.Companion", "parse:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty.Companion", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "step:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "EelNumberRangeProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog", "manipulateProperty:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "isDefault:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "restoreDefaults:me.tims<PERSON><PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty", "toString:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.liveprog.EelNumberRangeProperty"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\AssetManagerExtensions.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.AssetManagerExtensions", "AssetManagerExtensions:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "installPrivateAssets:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.AssetManagerExtensions"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\root\\RootSessionDumpManager.kt": ["RootSessionDumpManager:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.root", "setOnSessionDump:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDumpManager", "onDumpMethodChange:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.root.RootSessionDumpManager", "setOnDumpMethodChanged:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDumpManager", "handleSessionDump:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.root.RootSessionDumpManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\JdspImpResToolbox.kt": ["ReadImpulseResponseToFloat:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "JdspImpResToolbox:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "ComputeCompResponse:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "OfflineAudioResample:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "ComputeIIREqualizerCplx:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "ComputeEqResponse:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox", "ComputeIIREqualizerResponse:me.timschnee<PERSON>.rootlessjamesdsp.interop.JdspImpResToolbox"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\PreferenceCache.kt": ["clear:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "cache:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "changedNamespaces:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "select:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "mark<PERSON><PERSON>esAsCommitted:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "getPreferences:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache.Companion", "selectedNamespace:me.timschnee<PERSON>.rootlessjamesdsp.interop.PreferenceCache", "uncachedGet:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache", "PreferenceCache:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "get:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.PreferenceCache"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\AudioSessionDumpEntry.kt": ["isUsageRecordable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry.Companion", "uid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry.Companion", "AudioSessionDumpEntry:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "packageName:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "content:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "usage:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "toString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry", "isUsageRecordable:me.timschnee<PERSON>.rootlessjamesdsp.model.AudioSessionDumpEntry"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityOnboardingBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityOnboardingBinding", "onboardingFragmentContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityOnboardingBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityOnboardingBinding", "ActivityOnboardingBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityOnboardingBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ActivityAeqSelectorBinding.java": ["progress:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "toolbar:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "emptyView:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "searchView:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "ActivityAeqSelectorBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "profileListContainer:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "emptyViewText:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "partialResultsCard:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "profileList:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ActivityAeqSelectorBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\OnboardingActivity.kt": ["onCreate:me.t<PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "EXTRA_ROOT_SETUP_DUMP_PERM:me.timschnee<PERSON>.rootlessjamesdsp.activity.OnboardingActivity.Companion", "EXTRA_ROOTLESS_REDO_ADB_SETUP:me.timschnee<PERSON>.rootlessjamesdsp.activity.OnboardingActivity.Companion", "OnboardingActivity:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "onBackPressed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "onSupportNavigateUp:me.timsch<PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity.Companion", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity", "EXTRA_FIX_PERMS:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.OnboardingActivity.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\EqualizerPreference.kt": ["entryValues:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "initialValue:me.t<PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "updateFilterType:me.timschnee<PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "onSharedPreferenceChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "onSetInitialValue:me.t<PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "onBindViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "entries:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "updateFromPreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "EqualizerPreference:me.t<PERSON>.rootlessjamesdsp.preference", "onAttached:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "onDetached:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference", "onGetDefaultValue:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.EqualizerPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\GraphicEqNode.kt": ["freq:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNode", "gain:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNode", "uuid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNode", "GraphicEqNode:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\LiveprogParamsFragment.kt": ["onCreate:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment.Companion", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onCreateRecyclerView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onViewCreated:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onCreatePreferences:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onCreateAdapter:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "onFloatPreferenceChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "restoreDefaults:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "LiveprogParamsFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onDestroy:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LiveprogParamsFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\root\\RootSessionDatabase.kt": ["OnRootSessionChangeListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.root", "RootSessionDatabase:me.timschnee<PERSON>.rootlessjamesdsp.session.root", "removeSessionByIntent:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "createSession:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "shouldAcceptSessionDump:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "enabled:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "shouldAddSession:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "onSessionRemoved:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase", "addSessionByIntent:me.timschnee<PERSON>.rootlessjamesdsp.session.root.RootSessionDatabase"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\DebugDumpFileProvider.kt": ["DebugDumpFileProvider:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.dump", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.DebugDumpFileProvider"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\AppCompatibilityActivity.kt": ["onResume:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AppCompatibilityActivity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.AppCompatibilityActivity", "onPause:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AppCompatibilityActivity", "AppCompatibilityActivity:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "onDestroy:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.AppCompatibilityActivity", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.AppCompatibilityActivity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\BlocklistActivity.kt": ["<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.BlocklistActivity", "BlocklistActivity:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.BlocklistActivity"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceSwitchgroupBinding.java": ["iconFrame:me.timschnee<PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "widgetFrame:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "PreferenceSwitchgroupBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "summary:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceSwitchgroupBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\MiscUtils.kt": ["animatedValueAs:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions", "md5:me.t<PERSON>.rootlessjamesdsp.utils.extensions", "setBackgroundFromAttribute:me.tims<PERSON><PERSON>.rootlessjamesdsp.utils.extensions", "asHtml:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "crc:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions", "toShort:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions", "isDynamicColorAvailable:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "equalsDelta:me.tims<PERSON>.rootlessjamesdsp.utils.extensions", "ensureIsFile:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.extensions", "prettyNumberFormat:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.extensions", "ensureIsDirectory:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ContentMainBinding.java": ["bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ContentMainBinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ContentMainBinding", "ContentMainBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "inflate:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ContentMainBinding", "dspFragmentContainer:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.ContentMainBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\NumberInputBox.kt": ["max:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "step:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "hintText:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "isCurrentValueValid:me.tims<PERSON>.rootlessjamesdsp.view.NumberInputBox", "NumberInputBox:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "suffixText:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "precision:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "setOnValueChangedListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "customStepScale:me.timschnee<PERSON>.rootlessjamesdsp.view.NumberInputBox", "min:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "onAttachedToWindow:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "helperText:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "helperTextEnabled:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox", "onDetachedFromWindow:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.NumberInputBox"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsTroubleshootingFragment.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment.Companion", "SettingsTroubleshootingFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsTroubleshootingFragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceCompanderBinding.java": ["getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderBinding", "PreferenceCompanderBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "layoutEqualizer:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceCompanderBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsMiscFragment.kt": ["newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment.Companion", "SettingsMiscFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment", "onResume:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsMiscFragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentGraphicEqBinding.java": ["cards:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "nodeDetailContextButtons:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "freqInput:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "nodeEdit:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "autoeq:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "equalizerSurface:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "reset:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "editCardTitle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "previewCard:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "divider:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "editString:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "nodeList:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "editCard:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "horizontalScrollView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "emptyView:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "previewTitle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "FragmentGraphicEqBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "cancel:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "confirm:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "add:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding", "gainInput:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentGraphicEqBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\FloatingToggleButton.kt": ["isToggled:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton", "toggleOnClick:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton", "setOnToggleClickListener:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton", "OnToggleClickListener:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton", "onToggled:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton.OnToggledListener", "setOnToggledListener:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton", "onClick:me.timschnee<PERSON>.rootlessjamesdsp.view.FloatingToggleButton.OnToggleClickListener", "FloatingToggleButton:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view", "OnToggledListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.FloatingToggleButton"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\extensions\\ContextExtensions.kt": ["getAppNameFromUidSafe:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showYesNoAlert:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "unregisterLocalReceiver:me.timsch<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getAppNameFromUid:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showMultipleChoiceAlert:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showChoiceAlert:me.tims<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "resolveColorAttribute:me.tims<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showSingleChoiceAlert:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "toast:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showAlert:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "resolveReservedUid:me.tims<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getAppIcon:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "hideKeyboardFrom:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getResourceColor:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "isServiceRunning:me.tims<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "ensureCacheDir:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getUidFromPackage:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "dpToPx:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "isPackageInstalled:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "restoreDspSettings:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "requestIgnoreBatteryOptimizations:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getAppName:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "getPackageNameFromUid:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "check:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "registerLocalReceiver:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "pxToDp:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "sendLocalBroadcast:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "ContextExtensions:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.extensions", "broadcastPresetLoadEvent:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "openPlayStoreApp:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "showInputAlert:me.tims<PERSON><PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "acquireWakeLock:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions", "launchApp:me.timschnee<PERSON>.rootlessjamesdsp.utils.extensions.ContextExtensions"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\MaterialSwitchPreference.kt": ["MaterialSwitchPreference:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\backup\\BackupCreatorJob.kt": ["setupTask:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupCreatorJob.Companion", "doWork:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupCreatorJob", "isManualJobRunning:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupCreatorJob.Companion", "BackupCreatorJob:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup", "Companion:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupCreatorJob", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.backup.BackupCreatorJob.Companion", "startNow:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.backup.BackupCreatorJob.Companion"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentBlocklistBinding.java": ["notice:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "noticeLabel:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "recyclerview:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "FragmentBlocklistBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "emptyview:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentBlocklistBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\RoutingObserver.kt": ["ANALOG:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "BLUETOOTH:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "registerOnRoutingChangeListener:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "retrigger:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "currentDevice:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver", "HDMI:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.Companion", "OTHER:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "SPEAKER:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "onRouteRemoved:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.SPEAKER", "onSharedPreferenceChanged:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver", "group:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.Device", "Device:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.USB", "DeviceGroup:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "usesSingleProfile:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.Companion", "unregisterOnRoutingChangeListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.BLUETOOTH", "RoutingObserver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils", "onRoutingDeviceChanged:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.RoutingChangedCallback", "nameRes:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "name:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.Device", "USB:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.ANALOG", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.OTHER", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup", "from:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.Companion", "toString:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.Device", "onRouteChanged:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver.DeviceGroup.HDMI", "RoutingChangedCallback:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver", "id:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.utils.RoutingObserver.Device", "onRouteSelected:me.timschnee<PERSON>.rootlessjamesdsp.utils.RoutingObserver"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsFragment.kt": ["SettingsFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsFragment.Companion", "onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsFragment.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsFragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\SettingsActivity.kt": ["backupSaveFileSelectLauncher:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "backupLoadFileSelectLauncher:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "onCreate:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "onPreferenceStartFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "backupLocationSelectLauncher:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.SettingsActivity", "SettingsActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\ThemesPreferenceAdapter.kt": ["getItemViewType:me.timschnee<PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "OnItemClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "getItemCount:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "ThemesPreferenceAdapter:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "onCreateViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "ThemeViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter.ThemeViewHolder", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "setItems:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter", "onItemClick:me.timschnee<PERSON>.rootlessjamesdsp.adapter.ThemesPreferenceAdapter.OnItemClickListener"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceAppiconBinding.java": ["PreferenceAppiconBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAppiconBinding", "getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.PreferenceAppiconBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAppiconBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAppiconBinding", "preferenceAppicon:me.tims<PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceAppiconBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\api\\AutoEqService.kt": ["queryProfiles:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.api.AutoEqService", "AutoEqService:me.timsch<PERSON><PERSON>.rootlessjamesdsp.api", "getProfile:me.timschnee<PERSON>.rootlessjamesdsp.api.AutoEqService"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\settings\\SettingsAboutFragment.kt": ["onCreatePreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAboutFragment", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.settings.SettingsAboutFragment", "SettingsAboutFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAboutFragment.Companion", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAboutFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.settings.SettingsAboutFragment.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\LibraryLoadErrorFragment.kt": ["newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LibraryLoadErrorFragment.Companion", "onCreateView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LibraryLoadErrorFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LibraryLoadErrorFragment.Companion", "LibraryLoadErrorFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.LibraryLoadErrorFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.LibraryLoadErrorFragment"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\FragmentAppCompatibilityBinding.java": ["FragmentAppCompatibilityBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "icon:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "appName:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "launchApp:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "packageName:me.timschnee<PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "retry:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "appCard:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "exclude:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding", "hint:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.FragmentAppCompatibilityBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\rootless\\SessionRecordingPolicyManager.kt": ["clearSessions:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "getRestrictedUids:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "SessionRecordingPolicyManager:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless", "onSessionRecordingPolicyChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager.OnSessionRecordingPolicyChangeListener", "destroy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "OnSessionRecordingPolicyChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "update:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "unregisterOnRestrictedSessionChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager", "registerOnRestrictedSessionChangeListener:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.SessionRecordingPolicyManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\dump\\data\\ISessionInfoDump.kt": ["sessions:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.session.dump.data.ISessionInfoDump", "ISessionInfoDump:me.timschnee<PERSON>.rootlessjamesdsp.session.dump.data"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\AppBlocklistAdapter.kt": ["onClick:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.AppBlocklistViewHolder", "areItemsTheSame:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.BlockedAppComparator", "setOnItemClickListener:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.AppBlocklistViewHolder", "onItemClick:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.OnItemClickListener", "BlockedAppComparator:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "AppBlocklistViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "AppBlocklistAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "areContentsTheSame:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.BlockedAppComparator", "onCreateViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "OnItemClickListener:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.adapter.AppBlocklistAdapter.BlockedAppComparator"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\BaseActivity.kt": ["onDestroy:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "onSharedPreferenceChanged:me.tims<PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "prefsApp:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.BaseActivity", "prefsVar:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "app:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "onResume:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "disableAppTheme:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.BaseActivity", "BaseActivity:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistDao.kt": ["findByPackage:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "AppBlocklistDao:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room", "findAllByUid:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "findByUid:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "insertAll:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "delete:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "getAll:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao", "deleteAll:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\rootless\\RootlessSessionManager.kt": ["handleSessionDump:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionManager", "destroy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionManager", "RootlessSessionManager:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless", "onDumpMethodChange:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionManager", "sessionDatabase:me.timschnee<PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionManager", "sessionPolicyDatabase:me.timsch<PERSON><PERSON>.rootlessjamesdsp.session.rootless.RootlessSessionManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\ProcessorMessage.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.LiveprogResultCode", "Corrupted:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "NoFrames:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "VdcParseError:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.ConvolverParseError", "LiveprogStdout:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.VdcParseError", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.NoFrames", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.Unknown", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage", "AdvParamsInvalid:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.Corrupted", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.LiveprogExec", "LiveprogOutput:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.Unknown", "LiveprogResult:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "ConvolverErrorCode:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.AdvParamsInvalid", "Type:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage", "Param:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage", "TYPE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Companion", "fromInt:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.Companion", "ConvolverParseError:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "ConvolverErrorCode:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "fromInt:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode.Unknown", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.LiveprogOutput", "LiveprogResultCode:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.LiveprogStdout", "LiveprogFileId:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.ConvolverErrorCode", "LiveprogExec:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type", "fromInt:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.LiveprogErrorMessage", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param.LiveprogFileId", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.Companion", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "ProcessorMessage:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model", "Unknown:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "LiveprogErrorMessage:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Param", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type.LiveprogResult", "Unknown:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.ConvolverErrorCode", "Unknown:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.ProcessorMessage.Type"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\preset\\Preset.kt": ["META_LIVEPROG_INCLUDED:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "name:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset", "save:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset", "file:me.timschnee<PERSON>.rootlessjamesdsp.model.preset.Preset", "MIN_VERSION_CODE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "PRESET_VERSION:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "FILE_LIVEPROG:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "Preset:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset", "validate:me.t<PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "load:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "validate:me.t<PERSON>.rootlessjamesdsp.model.preset.Preset", "<init>:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "META_MIN_VERSION_CODE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "META_APP_VERSION:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "PresetMetadata:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset", "load:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset", "META_APP_FLAVOR:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset", "rename:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset", "META_VERSION:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preset.Preset.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\JamesDspLocalEngine.kt": ["setBassBoost:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "handle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "processInt16:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "processInt32:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setVacuumTube:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "processFloat:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setCompanderInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "supportsEelVmAccess:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "freezeLiveprogExecution:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setMultiEqualizerInternal:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "supportsCustomCrossfeed:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "close:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "manipulateEelVariable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setVdcInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setStereoEnhancement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setConvolverInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setLiveprogInternal:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setReverb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setOutputControl:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "enabled:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setCrossfeed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "enumerateEelVariables:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "JamesDspLocalEngine:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "setGraphicEqInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "sampleRate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine", "setCrossfeedCustom:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspLocalEngine"], "build\\generated\\ksp\\rootlessFullRelease\\java\\byRounds\\1\\me\\timschneeberger\\rootlessjamesdsp\\model\\room\\AppBlocklistDao_Impl.java": ["findAllByUid:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "getRequiredConverters:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "getAll:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "delete:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "AppBlocklistDao_Impl:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.room", "deleteAll:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "findByPackage:me.timschnee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "insertAll:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl", "findByUid:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.room.AppBlocklistDao_Impl"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\GraphicEqualizerSurface.kt": ["addElement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "onLayout:me.timschnee<PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "setNodes:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "GraphicEqualizerSurface:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "onRestoreInstanceState:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "onAttachedToWindow:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "onDraw:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface.Companion", "findClosest:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "onSaveInstanceState:me.timsch<PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.GraphicEqualizerSurface"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\preference\\GraphicEqualizerPreference.kt": ["onGetDefaultValue:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference", "onSetInitialValue:me.tims<PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference", "GraphicEqualizerPreference:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.preference", "updateFromPreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference", "onDetached:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference", "onBindViewHolder:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference", "onAttached:me.tims<PERSON><PERSON>.rootlessjamesdsp.preference.GraphicEqualizerPreference"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\JamesDspRemoteEngine.kt": ["setConvolverInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setStereoEnhancement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setLiveprogInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "close:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "sessionId:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "sampleRate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "isPluginInstalled:me.tims<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.Companion", "setCrossfeed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "syncWithPreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "enabled:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "freezeLiveprogExecution:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setMultiEqualizerInternal:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "supportsCustomCrossfeed:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setCrossfeedCustom:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setVdcInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setGraphicEqInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setBassBoost:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "manipulateEelVariable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "JamesDspRemoteEngine:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "convolverHash:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "Unsupported:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState.Unsupported", "isPidValid:me.tims<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "Available:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState", "Unavailable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState", "bufferLength:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState.Unavailable", "setVacuumTube:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "supportsEelVmAccess:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setOutputControl:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "allocatedBlockLength:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "ddcHash:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setReverb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "liveprogHash:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "paramCommitCount:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "priority:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "enumerateEelVariables:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "effect:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "graphicEqHash:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "PluginState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "setCompanderInternal:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "isSampleRateAbnormal:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "pid:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.PluginState.Available", "isPresetInitialized:me.tims<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.interop.JamesDspRemoteEngine.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\AppsListFragment.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.AppsListFragment", "AppsListFragment:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment", "onDestroyView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppsListFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppsListFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppsListFragment.Companion", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppsListFragment.Companion", "onCreateView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppsListFragment", "supportsPredictiveItemAnimations:me.timschnee<PERSON>.rootlessjamesdsp.fragment.AppsListFragment.LinearLayoutManagerWrapper"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\GraphicEqNodeList.kt": ["deserialize:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "fromBundle:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "toArrays:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList.Companion", "serialize:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "GraphicEqNodeList:me.t<PERSON><PERSON>.rootlessjamesdsp.model", "toBundle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.GraphicEqNodeList", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.GraphicEqNodeList"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\syntax\\EelLanguage.kt": ["EelLanguage:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax", "getCodeList:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.EelLanguage", "indentationEnds:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.EelLanguage", "indentationStarts:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.EelLanguage"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\view\\ProgressDialog.kt": ["title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "unit:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "cancel:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "divisor:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "currentProgress:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "isIndeterminate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "maxProgress:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "isCancelable:me.tims<PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "ProgressDialog:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog", "dismiss:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.view.ProgressDialog"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ItemAutoeqProfileListBinding.java": ["getRoot:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ItemAutoeqProfileListBinding", "subtitle:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAutoeqProfileListBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAutoeqProfileListBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAutoeqProfileListBinding", "ItemAutoeqProfileListBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ItemAutoeqProfileListBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\editor\\syntax\\Function.kt": ["codeTitle:me.timschnee<PERSON>.rootlessjamesdsp.editor.syntax.Function", "codePrefix:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.Function", "codeBody:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax.Function", "Function:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.editor.syntax"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\preference\\AudioEncoding.kt": ["PcmShort:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding.Companion", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AudioEncoding.PcmShort", "PcmFloat:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AudioEncoding.PcmFloat", "value:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding", "AudioEncoding:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference", "fromInt:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AudioEncoding.Companion"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\adapter\\GraphicEqNodeAdapter.kt": ["ViewHolder:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onCreateViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onItemClicked:me.timschnee<PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onBindViewHolder:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onItemsChanged:me.tims<PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onAttachedToRecyclerView:me.timsch<PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "GraphicEqNodeAdapter:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter", "nodes:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "onDetachedFromRecyclerView:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter", "deleteButton:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter.ViewHolder", "freq:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter.ViewHolder", "gain:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter.ViewHolder", "getItemCount:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.adapter.GraphicEqNodeAdapter"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\DialogEditorSearchReplaceBinding.java": ["replaceAction:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "inflate:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "searchEditFrame:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "replacementEdit:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "bind:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "dragHandle:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "findPrevAction:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "replacementEditFrame:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "searchEdit:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "DialogEditorSearchReplaceBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "findNextAction:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding", "getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.DialogEditorSearchReplaceBinding"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\ViewNumberInputBoxBinding.java": ["getRoot:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "inflate:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "ViewNumberInputBoxBinding:me.timsch<PERSON><PERSON>.rootlessjamesdsp.databinding", "minus:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "inputLayout:me.timschnee<PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "input:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding", "plus:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.ViewNumberInputBoxBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\liveprog\\EelPropertyFactory.kt": ["create:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog.EelPropertyFactory", "EelPropertyFactory:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.liveprog", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.liveprog.EelPropertyFactory"], "build\\generated\\data_binding_base_class_source_out\\rootlessFullRelease\\out\\me\\timschneeberger\\rootlessjamesdsp\\databinding\\PreferenceMaterialsliderBinding.java": ["seekbar:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding", "bind:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding", "title:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding", "PreferenceMaterialsliderBinding:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding", "getRoot:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding", "seekbarValue:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding", "inflate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.databinding.PreferenceMaterialsliderBinding"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\fragment\\AppCompatibilityFragment.kt": ["<init>:me.timschnee<PERSON>.rootlessjamesdsp.fragment.AppCompatibilityFragment", "onCreateView:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppCompatibilityFragment", "Companion:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppCompatibilityFragment", "<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppCompatibilityFragment.Companion", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment.AppCompatibilityFragment.Companion", "AppCompatibilityFragment:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.fragment"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\BenchmarkManager.kt": ["BenchmarkManager:me.tims<PERSON>.rootlessjamesdsp.interop", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.BenchmarkManager.BenchmarkState.BenchmarkDone", "Benchmarking:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.BenchmarkManager.BenchmarkState", "BenchmarkDone:me.t<PERSON><PERSON>.rootlessjamesdsp.interop.BenchmarkManager.BenchmarkState", "clearBenchmarks:me.tims<PERSON>.rootlessjamesdsp.interop.BenchmarkManager", "hasBenchmarksCached:me.tims<PERSON>.rootlessjamesdsp.interop.BenchmarkManager", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.BenchmarkManager", "BenchmarkState:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.BenchmarkManager", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.BenchmarkManager.BenchmarkState", "runBenchmarks:me.tims<PERSON>.rootlessjamesdsp.interop.BenchmarkManager", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.BenchmarkManager.BenchmarkState.Benchmarking", "loadBenchmarksFromCache:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.BenchmarkManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\session\\shared\\BaseSessionManager.kt": ["handleSessionDump:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "pollOnce:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "onReceive:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "dumpManager:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "onDumpMethodChange:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "BaseSessionManager:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared", "destroy:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager", "onActiveSessionsChanged:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.session.shared.BaseSessionManager"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\LiveprogEditorActivity.kt": ["<init>:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "onOptionsItemSelected:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "EXTRA_TARGET_FILE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity.Companion", "onDestroy:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "STATE_IS_DIRTY:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity.Companion", "LiveprogEditorActivity:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "onCreateOptionsMenu:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "onPostCreate:<PERSON><PERSON>t<PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity.Companion", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.LiveprogEditorActivity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\activity\\MainActivity.kt": ["onSharedPreferenceChanged:me.tims<PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.MainActivity", "findPreference:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment", "onCreate:me.t<PERSON>.rootlessjamesdsp.activity.MainActivity", "onPause:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "FakePresetFragment:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "pref:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment", "onResume:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "binding:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "onStart:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "onDestroy:me.t<PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "Companion:<PERSON>.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment", "newInstance:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment.Companion", "EXTRA_FORCE_SHOW_CAPTURE_PROMPT:me.timschnee<PERSON>.rootlessjamesdsp.activity.MainActivity.Companion", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment.Companion", "<init>:me.tims<PERSON>nee<PERSON>.rootlessjamesdsp.activity.MainActivity.FakePresetFragment", "onSaveInstanceState:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "<init>:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity.Companion", "MainActivity:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity", "onStop:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity", "requestCapturePermission:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.activity.MainActivity"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\interop\\JamesDspBaseEngine.kt": ["setReverb:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "enumerateEelVariables:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "onLiveprogResult:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "setOutputControl:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setVacuumTube:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "supportsEelVmAccess:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setVdc:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "onLiveprogExec:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "setConvolver:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setLiveprog:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "supportsCustomCrossfeed:me.timschnee<PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "manipulateEelVariable:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "sampleRate:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setCrossfeedCustom:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "JamesDspBaseEngine:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop", "onLiveprogOutput:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "setGraphicEqInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setVdcInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setGraphicEq:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setBassBoost:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "cache:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setLiveprogInternal:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "callbacks:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "enabled:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "context:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setConvolverInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setMultiEqualizer:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setCompander:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setCrossfeed:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setStereoEnhancement:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "setMultiEqualizerInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "onConvolverParseError:me.timsch<PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "close:me.t<PERSON><PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "clearCache:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "freezeLiveprogExecution:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "syncWithPreferences:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "DummyCallbacks:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine", "onVdcParseError:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine.DummyCallbacks", "setCompanderInternal:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.interop.JamesDspBaseEngine"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\service\\NotificationListenerService.kt": ["onBind:me.timschnee<PERSON>.rootlessjamesdsp.service.NotificationListenerService", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.service.NotificationListenerService", "NotificationListenerService:me.timsch<PERSON><PERSON>.rootlessjamesdsp.service", "onNotificationPosted:me.timschnee<PERSON>.rootlessjamesdsp.service.NotificationListenerService", "onNotificationRemoved:me.timschnee<PERSON>.rootlessjamesdsp.service.NotificationListenerService"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\model\\preference\\AppTheme.kt": ["Companion:<PERSON>.t<PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.t<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme.Companion", "STRAWBERRY_DAIQUIRI:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.HONEY", "TEALTURQUOISE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "titleResId:me.t<PERSON>.rootlessjamesdsp.model.preference.AppTheme", "TIDAL_WAVE:me.timsch<PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.YINYANG", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.GREEN_APPLE", "HONEY:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "fromInt:me.t<PERSON>.rootlessjamesdsp.model.preference.AppTheme.Companion", "AppTheme:me.t<PERSON>.rootlessjamesdsp.model.preference", "MONET:me.t<PERSON>.rootlessjamesdsp.model.preference.AppTheme", "GREEN_APPLE:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.TIDAL_WAVE", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.YOTSUBA", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.DEFAULT", "YOTSUBA:<PERSON>.t<PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.STRAWBERRY_DAIQUIRI", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.TEALTURQUOISE", "DEFAULT:me.tims<PERSON><PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.model.preference.AppTheme.MONET", "YINYANG:me.tims<PERSON><PERSON>.rootlessjamesdsp.model.preference.AppTheme"], "src\\main\\java\\me\\timschneeberger\\rootlessjamesdsp\\utils\\storage\\StorageUtils.kt": ["importFile:me.tims<PERSON>.rootlessjamesdsp.utils.storage.StorageUtils", "StorageUtils:me.tims<PERSON>.rootlessjamesdsp.utils.storage", "openInputStreamSafe:me.timsch<PERSON><PERSON>.rootlessjamesdsp.utils.storage.StorageUtils", "<init>:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.StorageUtils", "queryName:me.timschnee<PERSON>.rootlessjamesdsp.utils.storage.StorageUtils"]}