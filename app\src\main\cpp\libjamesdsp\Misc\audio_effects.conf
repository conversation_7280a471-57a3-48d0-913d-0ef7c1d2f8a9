libraries {
  bundle {
    path /system/lib/soundfx/libbundlewrapper.so
  }
  jdsp {
    path /system/lib/soundfx/libjamesdsp.so
  }
  reverb {
    path /system/lib/soundfx/libreverbwrapper.so
  }
  visualizer {
    path /system/lib/soundfx/libvisualizer.so
  }
  pre_processing {
    path /system/lib/soundfx/libaudiopreprocessing.so
  }
  downmix {
    path /system/lib/soundfx/libdownmix.so
  }
  loudness_enhancer {
    path /system/lib/soundfx/libldnhncr.so
  }
}
effects {
  jamesdsp {
    library jdsp
    uuid f27317f4-c984-4de6-9a90-545759495bf2
  }
  bassboost {
    library bundle
    uuid 8631f300-72e2-11df-b57e-0002a5d5c51b
  }
  virtualizer {
    library bundle
    uuid 1d4033c0-8557-11df-9f2d-0002a5d5c51b
  }
  equalizer {
    library bundle
    uuid ce772f20-847d-11df-bb17-0002a5d5c51b
  }
  volume {
    library bundle
    uuid 119341a0-8469-11df-81f9-0002a5d5c51b
  }
  reverb_env_aux {
    library reverb
    uuid 4a387fc0-8ab3-11df-8bad-0002a5d5c51b
  }
  reverb_env_ins {
    library reverb
    uuid c7a511a0-a3bb-11df-860e-0002a5d5c51b
  }
  reverb_pre_aux {
    library reverb
    uuid f29a1400-a3bb-11df-8ddc-0002a5d5c51b
  }
  reverb_pre_ins {
    library reverb
    uuid 172cdf00-a3bc-11df-a72f-0002a5d5c51b
  }
  visualizer {
    library visualizer
    uuid d069d9e0-8329-11df-9168-0002a5d5c51b
  }
  downmix {
    library downmix
    uuid 93f04452-e4fe-41cc-91f9-e475b6d1d69f
  }
  loudness_enhancer {
    library loudness_enhancer
    uuid fa415329-2034-4bea-b5dc-5b381c8d1e2c
  }
  agc {
    library pre_processing
    uuid aa8130e0-66fc-11e0-bad0-0002a5d5c51b
  }
  aec {
    library pre_processing
    uuid bb392ec0-8d4d-11e0-a896-0002a5d5c51b
  }
  ns {
    library pre_processing
    uuid c06c8400-8e06-11e0-9cb6-0002a5d5c51b
  }
}
