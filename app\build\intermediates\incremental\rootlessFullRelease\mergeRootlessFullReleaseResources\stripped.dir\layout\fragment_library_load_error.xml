<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="48dp"
            android:layout_marginHorizontal="16dp"
            android:paddingBottom="16dp">
            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_gravity="center"
                android:layout_marginBottom="8dp"
                android:alpha="0.8"
                app:srcCompat="@drawable/ic_twotone_error_24dp"
                app:tint="?attr/colorOnSurface"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginBottom="16dp"
                android:layout_gravity="center"
                android:text="@string/load_fail_header"
                android:textAppearance="?attr/textAppearanceHeadline5" />


            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackground="?attr/colorErrorContainer"
                app:titleText="@string/load_fail_card_title"
                app:bodyText="@string/load_fail_card"
                app:cardMargin="8dp" />

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/architectureNotice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:titleText="@string/load_fail_arch_card_title"
                app:bodyText="@string/load_fail_arch_card"
                app:cardMargin="8dp"/>

            <me.timschneeberger.rootlessjamesdsp.view.Card
                android:id="@+id/rootlessNotice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                app:titleText="@string/load_fail_rootless_card_title"
                app:bodyText="@string/load_fail_rootless_card"
                app:cardMargin="8dp"
                android:layout_marginBottom="48dp" />

        </LinearLayout>
</androidx.core.widget.NestedScrollView>
