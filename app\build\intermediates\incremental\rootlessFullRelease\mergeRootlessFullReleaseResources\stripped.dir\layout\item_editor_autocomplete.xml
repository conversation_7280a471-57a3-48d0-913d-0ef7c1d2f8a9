<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/code_container"
        android:layout_width="40dp"
        android:layout_centerVertical="true"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/code_type"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_twotone_chevron_right_24dp"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="K"
            android:importantForAccessibility="no" />
    </RelativeLayout>

    <TextView
        android:id="@+id/code_title"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_toEndOf="@id/code_container"
        android:gravity="start|center_vertical"
        android:paddingStart="5dp"
        tools:ignore="RtlSymmetry"
        tools:text="Keyword" />
</RelativeLayout>