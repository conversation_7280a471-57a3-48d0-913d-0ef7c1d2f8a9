package me.timschneeberger.rootlessjamesdsp.fragment

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ListView
import androidx.fragment.app.DialogFragment
import me.timschneeberger.rootlessjamesdsp.R
import me.timschneeberger.rootlessjamesdsp.utils.Constants
import me.timschneeberger.rootlessjamesdsp.utils.preferences.Preferences
import org.koin.android.ext.android.inject

class SpatialAudioPresetFragment : DialogFragment() {
    
    private val preferences: Preferences.App by inject()
    
    private val presets = arrayOf(
        "Default",
        "Gaming",
        "Music - Studio",
        "Music - Live Concert",
        "Movie Theater",
        "Podcast/Voice",
        "Outdoor",
        "Headphone Crossfeed",
        "Wide Stereo",
        "Binaural Focus"
    )
    
    override fun onCreateDialog(savedInstanceState: Bundle?) = 
        AlertDialog.Builder(requireContext())
            .setTitle("Spatial Audio Presets")
            .setItems(presets) { _, which ->
                applyPreset(which)
                dismiss()
            }
            .setNegativeButton("Cancel", null)
            .create()

    private fun applyPreset(presetIndex: Int) {
        val prefs = preferences.getSharedPreferences(Constants.PREF_SPATIALAUDIO)
        val editor = prefs.edit()
        
        when (presetIndex) {
            0 -> applyDefaultPreset(editor)
            1 -> applyGamingPreset(editor)
            2 -> applyStudioMusicPreset(editor)
            3 -> applyLiveConcertPreset(editor)
            4 -> applyMovieTheaterPreset(editor)
            5 -> applyPodcastPreset(editor)
            6 -> applyOutdoorPreset(editor)
            7 -> applyHeadphoneCrossfeedPreset(editor)
            8 -> applyWideStereoPreset(editor)
            9 -> applyBinauralFocusPreset(editor)
        }
        
        editor.apply()
    }
    
    private fun applyDefaultPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), false)
        editor.putString(getString(R.string.key_spatialaudio_mode), "1")
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 100)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 100)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "0")
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 50)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 50)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 70)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 30)
    }
    
    private fun applyGamingPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "2") // Binaural
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 120)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 150)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "1") // Simulated
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 30)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "2") // Medium room
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 40)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 60)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 80)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 20)
    }
    
    private fun applyStudioMusicPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "1") // Stereo widening
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 130)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 100)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "1") // Small room
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 30)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 80)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 50)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 40)
    }
    
    private fun applyLiveConcertPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "3") // Surround
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 150)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), -10)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 300)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "1")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 20)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "4") // Concert hall
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 80)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 30)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 60)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 10)
    }
    
    private fun applyMovieTheaterPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "3") // Surround
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 140)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 250)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "3") // Large room
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 70)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 40)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 70)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 15)
    }
    
    private fun applyPodcastPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "2") // Binaural
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 90)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 80)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "1") // Small room
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 20)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 90)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 40)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 50)
    }
    
    private fun applyOutdoorPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "2") // Binaural
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 160)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 500)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "1")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 40)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "5") // Outdoor
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 100)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 95)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 90)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 5)
    }
    
    private fun applyHeadphoneCrossfeedPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "1") // Stereo widening
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 110)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 100)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "0") // None
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 50)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 50)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 30)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 70)
    }
    
    private fun applyWideStereoPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "1") // Stereo widening
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 180)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 100)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "0")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 50)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "0") // None
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 50)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 50)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 50)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 20)
    }
    
    private fun applyBinauralFocusPreset(editor: SharedPreferences.Editor) {
        editor.putBoolean(getString(R.string.key_spatialaudio_enable), true)
        editor.putString(getString(R.string.key_spatialaudio_mode), "4") // HRTF
        editor.putInt(getString(R.string.key_spatialaudio_stereo_width), 100)
        editor.putInt(getString(R.string.key_spatialaudio_azimuth), 0)
        editor.putInt(getString(R.string.key_spatialaudio_elevation), 0)
        editor.putInt(getString(R.string.key_spatialaudio_distance), 120)
        editor.putString(getString(R.string.key_spatialaudio_head_tracking), "1")
        editor.putInt(getString(R.string.key_spatialaudio_head_tracking_speed), 60)
        editor.putString(getString(R.string.key_spatialaudio_room_type), "1") // Small room
        editor.putInt(getString(R.string.key_spatialaudio_room_size), 35)
        editor.putInt(getString(R.string.key_spatialaudio_room_damping), 70)
        editor.putInt(getString(R.string.key_spatialaudio_distance_attenuation), 60)
        editor.putInt(getString(R.string.key_spatialaudio_crossfeed_strength), 35)
    }
    
    companion object {
        fun newInstance() = SpatialAudioPresetFragment()
    }
}
