/*******************************************************************************
*  Copyright 2007 - 2011, <PERSON>                                  *
*  This program is free software: you can redistribute it and/or modify        *
*  it under the terms of the GNU General Public License as published by        *
*  the Free Software Foundation, either version 3 of the License, or           *
*  (at your option) any later version.                                         *
*                                                                              *
*  This program is distributed in the hope that it will be useful,             *
*  but WITHOUT ANY WARRANTY; without even the implied warranty of              *
*  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the                *
*  GNU General Public License (http://www.gnu.org/licenses/)for more details.  *
*******************************************************************************/

desc:Channel Polarity Control
//tags: processing phase stereo
//author: IXix

mode:0<0,3,1{Normal, Left, Right, Both}>Polarity Mode

@init
mode = 0;
modL = mode & 1 ? -1 : 1;
modR = mode & 2 ? -1 : 1;

@sample
spl0 *= modL;
spl1 *= modR;
