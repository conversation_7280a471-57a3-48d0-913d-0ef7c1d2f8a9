<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="20dp"
    android:paddingStart="?attr/dialogPreferredPadding"
    android:paddingEnd="?attr/dialogPreferredPadding"
    android:paddingBottom="?attr/dialogPreferredPadding">

    <TextView
        android:id="@+id/alertTitle"
        tools:text="Dialog title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceHeadline5" />
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progress"
        android:paddingTop="?attr/dialogPreferredPadding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:layout_below="@id/alertTitle"
        android:layout_centerHorizontal="true" />
    <TextView
        android:id="@+id/progress_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_below="@id/progress" />
    <TextView
        android:id="@+id/progress_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_below="@id/progress" />
</RelativeLayout>
