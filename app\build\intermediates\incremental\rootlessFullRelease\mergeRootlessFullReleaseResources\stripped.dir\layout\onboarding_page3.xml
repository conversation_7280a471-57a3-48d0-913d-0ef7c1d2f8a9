<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:alpha="0.8"
            app:srcCompat="@drawable/ic_twotone_auto_fix_high_24"
            app:tint="?attr/colorOnSurface"
            android:importantForAccessibility="no" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:text="@string/onboarding_methods_title"
            android:textAppearance="?attr/textAppearanceHeadline4" />

    </LinearLayout>

    <ScrollView
        android:layout_marginTop="16dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <com.google.android.material.card.MaterialCardView
                style="?attr/materialCardViewStyle"
                android:id="@+id/methods_root_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="?attr/colorSecondaryContainer"
                app:contentPadding="16dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/methods_root_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/onboarding_methods_root_title"
                        android:textAppearance="?attr/textAppearanceTitleLarge" />
                    <TextView
                        android:id="@+id/methods_root_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/onboarding_methods_root_root"
                        android:textAppearance="?attr/textAppearanceBody1" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                style="?attr/materialCardViewStyle"
                android:id="@+id/methods_shizuku_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                app:contentPadding="16dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/methods_shizuku_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/onboarding_methods_shizuku_title"
                        android:textAppearance="?attr/textAppearanceTitleLarge" />
                    <TextView
                        android:id="@+id/methods_shizuku_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/onboarding_methods_rootless_shizuku"
                        android:textAppearance="?attr/textAppearanceBody1" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                style="?attr/materialCardViewStyle"
                android:id="@+id/methods_adb_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                app:contentPadding="16dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/onboarding_methods_adb_title"
                        android:textAppearance="?attr/textAppearanceTitleLarge" />
                    <TextView
                        android:id="@+id/methods_adb_body"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/onboarding_methods_rootless_adb"
                        android:textAppearance="?attr/textAppearanceBody1" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </ScrollView>

</LinearLayout>
