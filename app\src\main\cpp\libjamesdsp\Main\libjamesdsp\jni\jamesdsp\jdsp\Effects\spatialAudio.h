#ifndef SPATIALAUDIO_H
#define SPATIALAUDIO_H

#include "../jdsp_header.h"

#ifdef __cplusplus
extern "C" {
#endif

// Spatial audio processing modes
typedef enum {
    SPATIAL_MODE_DISABLED = 0,
    SPATIAL_MODE_STEREO_WIDENING = 1,
    SPATIAL_MODE_BINAURAL = 2,
    SPATIAL_MODE_SURROUND = 3,
    SPATIAL_MODE_HRTF = 4
} SpatialAudioMode;

// Head tracking simulation modes
typedef enum {
    HEAD_TRACKING_DISABLED = 0,
    HEAD_TRACKING_SIMULATED = 1,
    HEAD_TRACKING_SENSOR = 2
} HeadTrackingMode;

// Room simulation types
typedef enum {
    ROOM_TYPE_NONE = 0,
    ROOM_TYPE_SMALL = 1,
    ROOM_TYPE_MEDIUM = 2,
    ROOM_TYPE_LARGE = 3,
    ROOM_TYPE_HALL = 4,
    ROOM_TYPE_OUTDOOR = 5
} RoomType;

// HRTF data structure
typedef struct {
    float *left_impulse;
    float *right_impulse;
    int impulse_length;
    float azimuth;
    float elevation;
    float distance;
} HRTFData;

// 3D position structure
typedef struct {
    float x, y, z;          // Cartesian coordinates
    float azimuth;          // Horizontal angle in radians (-π to π)
    float elevation;        // Vertical angle in radians (-π/2 to π/2)
    float distance;         // Distance from listener (0.1 to 100.0)
} Position3D;

// Head orientation structure
typedef struct {
    float yaw;              // Head rotation around Y axis (radians)
    float pitch;            // Head rotation around X axis (radians)
    float roll;             // Head rotation around Z axis (radians)
} HeadOrientation;

// Spatial audio processor structure
typedef struct {
    // Core settings
    SpatialAudioMode mode;
    HeadTrackingMode head_tracking_mode;
    RoomType room_type;
    int enabled;
    
    // 3D positioning
    Position3D source_position;
    HeadOrientation head_orientation;
    float listener_height;
    
    // Processing parameters
    float stereo_width;         // 0.0 to 2.0, 1.0 = normal
    float room_size;            // 0.0 to 1.0
    float room_damping;         // 0.0 to 1.0
    float distance_attenuation; // 0.0 to 1.0
    float doppler_factor;       // 0.0 to 2.0
    
    // HRTF processing
    HRTFData *hrtf_database;
    int hrtf_count;
    float *hrtf_left_buffer;
    float *hrtf_right_buffer;
    int hrtf_buffer_size;
    
    // Convolution buffers
    float *conv_buffer_left;
    float *conv_buffer_right;
    float *overlap_buffer_left;
    float *overlap_buffer_right;
    int conv_buffer_size;
    int overlap_size;
    
    // Delay lines for distance simulation
    float *delay_line_left;
    float *delay_line_right;
    int delay_line_size;
    int delay_write_pos;
    float current_delay_samples;
    
    // Room simulation
    float *room_reverb_left;
    float *room_reverb_right;
    int room_reverb_size;
    float room_reverb_feedback;
    float room_reverb_mix;
    
    // Head tracking simulation
    float head_tracking_speed;
    float head_tracking_amplitude;
    double head_tracking_phase;
    
    // Crossfeed for headphone processing
    float crossfeed_strength;
    float crossfeed_delay_samples;
    float *crossfeed_delay_left;
    float *crossfeed_delay_right;
    int crossfeed_delay_size;
    int crossfeed_delay_pos;
    
    // Sample rate and block size
    float sample_rate;
    int block_size;
    
    // Temporary processing buffers
    float *temp_left;
    float *temp_right;
    float *temp_mono;
    
    // Performance optimization
    int force_refresh;
    int processing_enabled;
    
} SpatialAudio;

// Function declarations
void SpatialAudioInit(SpatialAudio *spatial, float sample_rate, int block_size);
void SpatialAudioFree(SpatialAudio *spatial);
void SpatialAudioProcess(SpatialAudio *spatial, float *left, float *right, int samples);

// Configuration functions
void SpatialAudioSetMode(SpatialAudio *spatial, SpatialAudioMode mode);
void SpatialAudioSetHeadTracking(SpatialAudio *spatial, HeadTrackingMode mode);
void SpatialAudioSetRoomType(SpatialAudio *spatial, RoomType room_type);
void SpatialAudioSetSourcePosition(SpatialAudio *spatial, float x, float y, float z);
void SpatialAudioSetHeadOrientation(SpatialAudio *spatial, float yaw, float pitch, float roll);
void SpatialAudioSetStereoWidth(SpatialAudio *spatial, float width);
void SpatialAudioSetRoomParameters(SpatialAudio *spatial, float size, float damping);
void SpatialAudioSetDistanceAttenuation(SpatialAudio *spatial, float attenuation);
void SpatialAudioSetCrossfeedStrength(SpatialAudio *spatial, float strength);

// HRTF functions
int SpatialAudioLoadHRTF(SpatialAudio *spatial, const char *hrtf_data, int data_size);
void SpatialAudioSelectHRTF(SpatialAudio *spatial, float azimuth, float elevation);
void SpatialAudioInterpolateHRTF(SpatialAudio *spatial, float azimuth, float elevation, 
                                 float *left_impulse, float *right_impulse);

// Utility functions
void SpatialAudioCartesianToPolar(float x, float y, float z, float *azimuth, float *elevation, float *distance);
void SpatialAudioPolarToCartesian(float azimuth, float elevation, float distance, float *x, float *y, float *z);
float SpatialAudioCalculateDelay(float distance, float sample_rate);
float SpatialAudioCalculateAttenuation(float distance, float attenuation_factor);

// Processing helper functions
void SpatialAudioProcessStereoWidening(SpatialAudio *spatial, float *left, float *right, int samples);
void SpatialAudioProcessBinaural(SpatialAudio *spatial, float *left, float *right, int samples);
void SpatialAudioProcessHRTF(SpatialAudio *spatial, float *left, float *right, int samples);
void SpatialAudioProcessRoomSimulation(SpatialAudio *spatial, float *left, float *right, int samples);
void SpatialAudioProcessHeadTracking(SpatialAudio *spatial);
void SpatialAudioProcessCrossfeed(SpatialAudio *spatial, float *left, float *right, int samples);

// Enable/disable functions
void SpatialAudioEnable(SpatialAudio *spatial);
void SpatialAudioDisable(SpatialAudio *spatial);
int SpatialAudioIsEnabled(SpatialAudio *spatial);

#ifdef __cplusplus
}
#endif

#endif // SPATIALAUDIO_H
